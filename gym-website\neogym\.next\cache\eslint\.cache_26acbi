[{"C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\layout\\Footer.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\layout\\Header.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\layout\\Layout.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\About.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Classes.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Contact.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Hero.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Membership.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Testimonials.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Trainers.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\ui\\Button.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\ui\\Card.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\lib\\utils.ts": "15"}, {"size": 689, "mtime": 1750267514597, "results": "16", "hashOfConfig": "17"}, {"size": 637, "mtime": 1750269873552, "results": "18", "hashOfConfig": "17"}, {"size": 6508, "mtime": 1750281328046, "results": "19", "hashOfConfig": "17"}, {"size": 3479, "mtime": 1750280668461, "results": "20", "hashOfConfig": "17"}, {"size": 389, "mtime": 1750272855178, "results": "21", "hashOfConfig": "17"}, {"size": 6007, "mtime": 1750281514992, "results": "22", "hashOfConfig": "17"}, {"size": 9339, "mtime": 1750281354018, "results": "23", "hashOfConfig": "17"}, {"size": 11587, "mtime": 1750281368246, "results": "24", "hashOfConfig": "17"}, {"size": 3046, "mtime": 1750281303605, "results": "25", "hashOfConfig": "17"}, {"size": 8141, "mtime": 1750281316353, "results": "26", "hashOfConfig": "17"}, {"size": 9974, "mtime": 1750281574198, "results": "27", "hashOfConfig": "17"}, {"size": 10530, "mtime": 1750281493839, "results": "28", "hashOfConfig": "17"}, {"size": 1494, "mtime": 1750281255870, "results": "29", "hashOfConfig": "17"}, {"size": 1001, "mtime": 1750271381592, "results": "30", "hashOfConfig": "17"}, {"size": 169, "mtime": 1750268426211, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "jmtx33", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\About.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Classes.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Membership.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Testimonials.tsx", ["77"], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Trainers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\lib\\utils.ts", [], [], {"ruleId": "78", "severity": 1, "message": "79", "line": 69, "column": 9, "nodeType": "80", "endLine": 71, "endColumn": 4, "suggestions": "81"}, "react-hooks/exhaustive-deps", "The 'nextTestimonial' function makes the dependencies of useEffect Hook (at line 81) change on every render. To fix this, wrap the definition of 'nextTestimonial' in its own useCallback() Hook.", "VariableDeclarator", ["82"], {"desc": "83", "fix": "84"}, "Wrap the definition of 'nextTestimonial' in its own useCallback() Hook.", {"range": "85", "text": "86"}, [2688, 2764], "useCallback(() => {\n    setCurrentIndex((prev) => (prev + 1) % testimonials.length);\n  })"]