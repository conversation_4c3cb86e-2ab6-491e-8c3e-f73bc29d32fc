[{"C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\layout\\Footer.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\layout\\Header.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\layout\\Layout.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\About.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Classes.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Contact.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Hero.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Membership.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Testimonials.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Trainers.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\ui\\Button.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\ui\\Card.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\lib\\utils.ts": "15"}, {"size": 689, "mtime": 1750267514597, "results": "16", "hashOfConfig": "17"}, {"size": 637, "mtime": 1750269873552, "results": "18", "hashOfConfig": "17"}, {"size": 6547, "mtime": 1750268215941, "results": "19", "hashOfConfig": "17"}, {"size": 3479, "mtime": 1750280668461, "results": "20", "hashOfConfig": "17"}, {"size": 389, "mtime": 1750272855178, "results": "21", "hashOfConfig": "17"}, {"size": 6031, "mtime": 1750271198138, "results": "22", "hashOfConfig": "17"}, {"size": 9424, "mtime": 1750271291234, "results": "23", "hashOfConfig": "17"}, {"size": 11672, "mtime": 1750271685898, "results": "24", "hashOfConfig": "17"}, {"size": 3060, "mtime": 1750281173923, "results": "25", "hashOfConfig": "17"}, {"size": 8148, "mtime": 1750280813625, "results": "26", "hashOfConfig": "17"}, {"size": 9996, "mtime": 1750271594864, "results": "27", "hashOfConfig": "17"}, {"size": 10615, "mtime": 1750271514688, "results": "28", "hashOfConfig": "17"}, {"size": 1494, "mtime": 1750281255870, "results": "29", "hashOfConfig": "17"}, {"size": 1001, "mtime": 1750271381592, "results": "30", "hashOfConfig": "17"}, {"size": 169, "mtime": 1750268426211, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "jmtx33", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\layout\\Footer.tsx", ["77"], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\About.tsx", ["78", "79", "80", "81", "82", "83"], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Classes.tsx", ["84", "85"], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Contact.tsx", ["86", "87"], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Hero.tsx", ["88", "89"], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Membership.tsx", ["90"], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Testimonials.tsx", ["91", "92", "93", "94"], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Trainers.tsx", ["95", "96"], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\lib\\utils.ts", [], [], {"ruleId": "97", "severity": 2, "message": "98", "line": 4, "column": 10, "nodeType": null, "messageId": "99", "endLine": 4, "endColumn": 16}, {"ruleId": "97", "severity": 2, "message": "100", "line": 5, "column": 8, "nodeType": null, "messageId": "99", "endLine": 5, "endColumn": 12}, {"ruleId": "101", "severity": 2, "message": "102", "line": 125, "column": 15, "nodeType": "103", "messageId": "104", "suggestions": "105"}, {"ruleId": "101", "severity": 2, "message": "106", "line": 125, "column": 41, "nodeType": "103", "messageId": "104", "suggestions": "107"}, {"ruleId": "101", "severity": 2, "message": "106", "line": 125, "column": 73, "nodeType": "103", "messageId": "104", "suggestions": "108"}, {"ruleId": "101", "severity": 2, "message": "106", "line": 127, "column": 58, "nodeType": "103", "messageId": "104", "suggestions": "109"}, {"ruleId": "101", "severity": 2, "message": "102", "line": 127, "column": 111, "nodeType": "103", "messageId": "104", "suggestions": "110"}, {"ruleId": "97", "severity": 2, "message": "100", "line": 6, "column": 8, "nodeType": null, "messageId": "99", "endLine": 6, "endColumn": 12}, {"ruleId": "97", "severity": 2, "message": "111", "line": 7, "column": 8, "nodeType": null, "messageId": "99", "endLine": 7, "endColumn": 14}, {"ruleId": "97", "severity": 2, "message": "100", "line": 6, "column": 8, "nodeType": null, "messageId": "99", "endLine": 6, "endColumn": 12}, {"ruleId": "97", "severity": 2, "message": "111", "line": 7, "column": 8, "nodeType": null, "messageId": "99", "endLine": 7, "endColumn": 14}, {"ruleId": "97", "severity": 2, "message": "112", "line": 3, "column": 28, "nodeType": null, "messageId": "99", "endLine": 3, "endColumn": 33}, {"ruleId": "97", "severity": 2, "message": "113", "line": 3, "column": 40, "nodeType": null, "messageId": "99", "endLine": 3, "endColumn": 45}, {"ruleId": "97", "severity": 2, "message": "114", "line": 137, "column": 29, "nodeType": null, "messageId": "99", "endLine": 137, "endColumn": 34}, {"ruleId": "97", "severity": 2, "message": "100", "line": 6, "column": 8, "nodeType": null, "messageId": "99", "endLine": 6, "endColumn": 12}, {"ruleId": "115", "severity": 1, "message": "116", "line": 81, "column": 6, "nodeType": "117", "endLine": 81, "endColumn": 8, "suggestions": "118"}, {"ruleId": "101", "severity": 2, "message": "102", "line": 141, "column": 19, "nodeType": "103", "messageId": "104", "suggestions": "119"}, {"ruleId": "101", "severity": 2, "message": "102", "line": 141, "column": 53, "nodeType": "103", "messageId": "104", "suggestions": "120"}, {"ruleId": "97", "severity": 2, "message": "100", "line": 5, "column": 8, "nodeType": null, "messageId": "99", "endLine": 5, "endColumn": 12}, {"ruleId": "97", "severity": 2, "message": "111", "line": 6, "column": 8, "nodeType": null, "messageId": "99", "endLine": 6, "endColumn": 14}, "@typescript-eslint/no-unused-vars", "'motion' is defined but never used.", "unusedVar", "'Card' is defined but never used.", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["121", "122", "123", "124"], "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", ["125", "126", "127", "128"], ["129", "130", "131", "132"], ["133", "134", "135", "136"], ["137", "138", "139", "140"], "'Button' is defined but never used.", "'Users' is defined but never used.", "'Clock' is defined but never used.", "'index' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'nextTestimonial'. Either include it or remove the dependency array.", "ArrayExpression", ["141"], ["142", "143", "144", "145"], ["146", "147", "148", "149"], {"messageId": "150", "data": "151", "fix": "152", "desc": "153"}, {"messageId": "150", "data": "154", "fix": "155", "desc": "156"}, {"messageId": "150", "data": "157", "fix": "158", "desc": "159"}, {"messageId": "150", "data": "160", "fix": "161", "desc": "162"}, {"messageId": "150", "data": "163", "fix": "164", "desc": "165"}, {"messageId": "150", "data": "166", "fix": "167", "desc": "168"}, {"messageId": "150", "data": "169", "fix": "170", "desc": "171"}, {"messageId": "150", "data": "172", "fix": "173", "desc": "174"}, {"messageId": "150", "data": "175", "fix": "176", "desc": "165"}, {"messageId": "150", "data": "177", "fix": "178", "desc": "168"}, {"messageId": "150", "data": "179", "fix": "180", "desc": "171"}, {"messageId": "150", "data": "181", "fix": "182", "desc": "174"}, {"messageId": "150", "data": "183", "fix": "184", "desc": "165"}, {"messageId": "150", "data": "185", "fix": "186", "desc": "168"}, {"messageId": "150", "data": "187", "fix": "188", "desc": "171"}, {"messageId": "150", "data": "189", "fix": "190", "desc": "174"}, {"messageId": "150", "data": "191", "fix": "192", "desc": "153"}, {"messageId": "150", "data": "193", "fix": "194", "desc": "156"}, {"messageId": "150", "data": "195", "fix": "196", "desc": "159"}, {"messageId": "150", "data": "197", "fix": "198", "desc": "162"}, {"desc": "199", "fix": "200"}, {"messageId": "150", "data": "201", "fix": "202", "desc": "153"}, {"messageId": "150", "data": "203", "fix": "204", "desc": "156"}, {"messageId": "150", "data": "205", "fix": "206", "desc": "159"}, {"messageId": "150", "data": "207", "fix": "208", "desc": "162"}, {"messageId": "150", "data": "209", "fix": "210", "desc": "153"}, {"messageId": "150", "data": "211", "fix": "212", "desc": "156"}, {"messageId": "150", "data": "213", "fix": "214", "desc": "159"}, {"messageId": "150", "data": "215", "fix": "216", "desc": "162"}, "replaceWithAlt", {"alt": "217"}, {"range": "218", "text": "219"}, "Replace with `&quot;`.", {"alt": "220"}, {"range": "221", "text": "222"}, "Replace with `&ldquo;`.", {"alt": "223"}, {"range": "224", "text": "225"}, "Replace with `&#34;`.", {"alt": "226"}, {"range": "227", "text": "228"}, "Replace with `&rdquo;`.", {"alt": "229"}, {"range": "230", "text": "231"}, "Replace with `&apos;`.", {"alt": "232"}, {"range": "233", "text": "234"}, "Replace with `&lsquo;`.", {"alt": "235"}, {"range": "236", "text": "237"}, "Replace with `&#39;`.", {"alt": "238"}, {"range": "239", "text": "240"}, "Replace with `&rsquo;`.", {"alt": "229"}, {"range": "241", "text": "242"}, {"alt": "232"}, {"range": "243", "text": "244"}, {"alt": "235"}, {"range": "245", "text": "246"}, {"alt": "238"}, {"range": "247", "text": "248"}, {"alt": "229"}, {"range": "249", "text": "250"}, {"alt": "232"}, {"range": "251", "text": "252"}, {"alt": "235"}, {"range": "253", "text": "254"}, {"alt": "238"}, {"range": "255", "text": "256"}, {"alt": "217"}, {"range": "257", "text": "258"}, {"alt": "220"}, {"range": "259", "text": "260"}, {"alt": "223"}, {"range": "261", "text": "262"}, {"alt": "226"}, {"range": "263", "text": "264"}, "Update the dependencies array to be: [nextTestimonial]", {"range": "265", "text": "266"}, {"alt": "217"}, {"range": "267", "text": "268"}, {"alt": "220"}, {"range": "269", "text": "270"}, {"alt": "223"}, {"range": "271", "text": "272"}, {"alt": "226"}, {"range": "273", "text": "274"}, {"alt": "217"}, {"range": "275", "text": "276"}, {"alt": "220"}, {"range": "277", "text": "278"}, {"alt": "223"}, {"range": "279", "text": "280"}, {"alt": "226"}, {"range": "281", "text": "282"}, "&quot;", [5180, 5494], "\n              &quot;The future of fitness isn't just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.\"\n            ", "&ldquo;", [5180, 5494], "\n              &ldquo;The future of fitness isn't just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.\"\n            ", "&#34;", [5180, 5494], "\n              &#34;The future of fitness isn't just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.\"\n            ", "&rdquo;", [5180, 5494], "\n              &rdquo;The future of fitness isn't just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.\"\n            ", "&apos;", [5180, 5494], "\n              \"The future of fitness isn&apos;t just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.\"\n            ", "&lsquo;", [5180, 5494], "\n              \"The future of fitness isn&lsquo;t just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.\"\n            ", "&#39;", [5180, 5494], "\n              \"The future of fitness isn&#39;t just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.\"\n            ", "&rsquo;", [5180, 5494], "\n              \"The future of fitness isn&rsquo;t just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.\"\n            ", [5180, 5494], "\n              \"The future of fitness isn't just about stronger bodies—it&apos;s about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.\"\n            ", [5180, 5494], "\n              \"The future of fitness isn't just about stronger bodies—it&lsquo;s about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.\"\n            ", [5180, 5494], "\n              \"The future of fitness isn't just about stronger bodies—it&#39;s about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.\"\n            ", [5180, 5494], "\n              \"The future of fitness isn't just about stronger bodies—it&rsquo;s about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.\"\n            ", [5180, 5494], "\n              \"The future of fitness isn't just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don&apos;t just train your body; we evolve your entire being.\"\n            ", [5180, 5494], "\n              \"The future of fitness isn't just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don&lsquo;t just train your body; we evolve your entire being.\"\n            ", [5180, 5494], "\n              \"The future of fitness isn't just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don&#39;t just train your body; we evolve your entire being.\"\n            ", [5180, 5494], "\n              \"The future of fitness isn't just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don&rsquo;t just train your body; we evolve your entire being.\"\n            ", [5180, 5494], "\n              \"The future of fitness isn't just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.&quot;\n            ", [5180, 5494], "\n              \"The future of fitness isn't just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.&ldquo;\n            ", [5180, 5494], "\n              \"The future of fitness isn't just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.&#34;\n            ", [5180, 5494], "\n              \"The future of fitness isn't just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.&rdquo;\n            ", [3089, 3091], "[nextTestimonial]", [5702, 5722], "\n                  &quot;", [5702, 5722], "\n                  &ldquo;", [5702, 5722], "\n                  &#34;", [5702, 5722], "\n                  &rdquo;", [5755, 5773], "&quot;\n                ", [5755, 5773], "&ldquo;\n                ", [5755, 5773], "&#34;\n                ", [5755, 5773], "&rdquo;\n                "]