import type { Metadata } from "next";
import { <PERSON>, <PERSON> } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const oswald = <PERSON>({
  subsets: ["latin"],
  variable: "--font-oswald",
});

export const metadata: Metadata = {
  title: "EliteGym - Premium Fitness Experience",
  description: "Transform your body and mind at EliteGym. Join our community of fitness enthusiasts with world-class facilities, expert trainers, and comprehensive fitness programs.",
  keywords: "gym, fitness, personal training, classes, workout, health, wellness, strength training, cardio",
  authors: [{ name: "<PERSON><PERSON><PERSON>" }],
  creator: "EliteG<PERSON>",
  publisher: "EliteGym",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://elitegym.com"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "EliteGym - Premium Fitness Experience",
    description: "Transform your body and mind at EliteGym. Join our community of fitness enthusiasts with world-class facilities and expert trainers.",
    url: "https://elitegym.com",
    siteName: "EliteGym",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "EliteGym - Premium Fitness Experience",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "EliteGym - Premium Fitness Experience",
    description: "Transform your body and mind at EliteGym. Join our community of fitness enthusiasts with world-class facilities and expert trainers.",
    images: ["/og-image.jpg"],
    creator: "@elitegym",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${inter.variable} ${oswald.variable} antialiased`}
        style={{
          fontFamily: 'var(--font-inter), system-ui, sans-serif',
        }}
      >
        {children}
      </body>
    </html>
  );
}
