[{"name": "generate-buildid", "duration": 299, "timestamp": 1509007902757, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750281272043, "traceId": "29eeeda267b58bf2"}, {"name": "load-custom-routes", "duration": 454, "timestamp": 1509007903326, "id": 5, "parentId": 1, "tags": {}, "startTime": 1750281272044, "traceId": "29eeeda267b58bf2"}, {"name": "create-dist-dir", "duration": 977, "timestamp": 1509008022422, "id": 6, "parentId": 1, "tags": {}, "startTime": 1750281272163, "traceId": "29eeeda267b58bf2"}, {"name": "create-pages-mapping", "duration": 2177, "timestamp": 1509008038354, "id": 7, "parentId": 1, "tags": {}, "startTime": 1750281272179, "traceId": "29eeeda267b58bf2"}, {"name": "collect-app-paths", "duration": 4504, "timestamp": 1509008040674, "id": 8, "parentId": 1, "tags": {}, "startTime": 1750281272181, "traceId": "29eeeda267b58bf2"}, {"name": "create-app-mapping", "duration": 6298, "timestamp": 1509008045243, "id": 9, "parentId": 1, "tags": {}, "startTime": 1750281272186, "traceId": "29eeeda267b58bf2"}, {"name": "public-dir-conflict-check", "duration": 1725, "timestamp": 1509008052783, "id": 10, "parentId": 1, "tags": {}, "startTime": 1750281272193, "traceId": "29eeeda267b58bf2"}, {"name": "generate-routes-manifest", "duration": 6732, "timestamp": 1509008055647, "id": 11, "parentId": 1, "tags": {}, "startTime": 1750281272196, "traceId": "29eeeda267b58bf2"}, {"name": "create-entrypoints", "duration": 38050, "timestamp": 1509009641769, "id": 15, "parentId": 13, "tags": {}, "startTime": 1750281273787, "traceId": "29eeeda267b58bf2"}, {"name": "generate-webpack-config", "duration": 763697, "timestamp": 1509009680240, "id": 16, "parentId": 14, "tags": {}, "startTime": 1750281273826, "traceId": "29eeeda267b58bf2"}, {"name": "next-trace-entrypoint-plugin", "duration": 5085, "timestamp": 1509010694468, "id": 18, "parentId": 17, "tags": {}, "startTime": 1750281274840, "traceId": "29eeeda267b58bf2"}, {"name": "add-entry", "duration": 420675, "timestamp": 1509010715635, "id": 22, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1750281274861, "traceId": "29eeeda267b58bf2"}, {"name": "add-entry", "duration": 480245, "timestamp": 1509010715790, "id": 25, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1750281274861, "traceId": "29eeeda267b58bf2"}, {"name": "add-entry", "duration": 544836, "timestamp": 1509010715691, "id": 23, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1750281274861, "traceId": "29eeeda267b58bf2"}, {"name": "add-entry", "duration": 668953, "timestamp": 1509010714392, "id": 20, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=C%3A%5CUsers%5CZbook%5CDesktop%5CStore%5Cgym-website%5Cneogym%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750281274860, "traceId": "29eeeda267b58bf2"}, {"name": "add-entry", "duration": 667997, "timestamp": 1509010715387, "id": 21, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5CZbook%5CDesktop%5CStore%5Cgym-website%5Cneogym%5Csrc%5Capp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750281274861, "traceId": "29eeeda267b58bf2"}, {"name": "add-entry", "duration": 704754, "timestamp": 1509010715753, "id": 24, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CZbook%5CDesktop%5CStore%5Cgym-website%5Cneogym%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750281274861, "traceId": "29eeeda267b58bf2"}, {"name": "make", "duration": 1326648, "timestamp": 1509010713661, "id": 19, "parentId": 17, "tags": {}, "startTime": 1750281274859, "traceId": "29eeeda267b58bf2"}, {"name": "get-entries", "duration": 3120, "timestamp": 1509012043334, "id": 37, "parentId": 36, "tags": {}, "startTime": 1750281276189, "traceId": "29eeeda267b58bf2"}, {"name": "node-file-trace-plugin", "duration": 130337, "timestamp": 1509012051812, "id": 38, "parentId": 36, "tags": {"traceEntryCount": "8"}, "startTime": 1750281276197, "traceId": "29eeeda267b58bf2"}, {"name": "collect-traced-files", "duration": 1063, "timestamp": 1509012182162, "id": 39, "parentId": 36, "tags": {}, "startTime": 1750281276328, "traceId": "29eeeda267b58bf2"}, {"name": "finish-modules", "duration": 140482, "timestamp": 1509012042752, "id": 36, "parentId": 18, "tags": {}, "startTime": 1750281276188, "traceId": "29eeeda267b58bf2"}, {"name": "chunk-graph", "duration": 21720, "timestamp": 1509012256220, "id": 41, "parentId": 40, "tags": {}, "startTime": 1750281276402, "traceId": "29eeeda267b58bf2"}, {"name": "optimize-modules", "duration": 70, "timestamp": 1509012278304, "id": 43, "parentId": 40, "tags": {}, "startTime": 1750281276424, "traceId": "29eeeda267b58bf2"}, {"name": "optimize-chunks", "duration": 21841, "timestamp": 1509012278650, "id": 44, "parentId": 40, "tags": {}, "startTime": 1750281276424, "traceId": "29eeeda267b58bf2"}, {"name": "optimize-tree", "duration": 181, "timestamp": 1509012300602, "id": 45, "parentId": 40, "tags": {}, "startTime": 1750281276446, "traceId": "29eeeda267b58bf2"}, {"name": "optimize-chunk-modules", "duration": 44016, "timestamp": 1509012300904, "id": 46, "parentId": 40, "tags": {}, "startTime": 1750281276446, "traceId": "29eeeda267b58bf2"}, {"name": "optimize", "duration": 66896, "timestamp": 1509012278183, "id": 42, "parentId": 40, "tags": {}, "startTime": 1750281276424, "traceId": "29eeeda267b58bf2"}, {"name": "module-hash", "duration": 38259, "timestamp": 1509012373468, "id": 47, "parentId": 40, "tags": {}, "startTime": 1750281276519, "traceId": "29eeeda267b58bf2"}, {"name": "code-generation", "duration": 19465, "timestamp": 1509012411860, "id": 48, "parentId": 40, "tags": {}, "startTime": 1750281276557, "traceId": "29eeeda267b58bf2"}, {"name": "hash", "duration": 13442, "timestamp": 1509012445002, "id": 49, "parentId": 40, "tags": {}, "startTime": 1750281276590, "traceId": "29eeeda267b58bf2"}, {"name": "code-generation-jobs", "duration": 628, "timestamp": 1509012458437, "id": 50, "parentId": 40, "tags": {}, "startTime": 1750281276604, "traceId": "29eeeda267b58bf2"}, {"name": "module-assets", "duration": 652, "timestamp": 1509012458897, "id": 51, "parentId": 40, "tags": {}, "startTime": 1750281276604, "traceId": "29eeeda267b58bf2"}, {"name": "create-chunk-assets", "duration": 2392, "timestamp": 1509012459571, "id": 52, "parentId": 40, "tags": {}, "startTime": 1750281276605, "traceId": "29eeeda267b58bf2"}, {"name": "minify-js", "duration": 544, "timestamp": 1509012478510, "id": 54, "parentId": 53, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1750281276624, "traceId": "29eeeda267b58bf2"}, {"name": "minify-js", "duration": 288, "timestamp": 1509012478778, "id": 55, "parentId": 53, "tags": {"name": "../app/favicon.ico/route.js", "cache": "HIT"}, "startTime": 1750281276624, "traceId": "29eeeda267b58bf2"}, {"name": "minify-js", "duration": 267, "timestamp": 1509012478803, "id": 56, "parentId": 53, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1750281276624, "traceId": "29eeeda267b58bf2"}, {"name": "minify-js", "duration": 238, "timestamp": 1509012478833, "id": 57, "parentId": 53, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1750281276624, "traceId": "29eeeda267b58bf2"}, {"name": "minify-js", "duration": 226, "timestamp": 1509012478846, "id": 58, "parentId": 53, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1750281276624, "traceId": "29eeeda267b58bf2"}, {"name": "minify-js", "duration": 217, "timestamp": 1509012478859, "id": 59, "parentId": 53, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1750281276624, "traceId": "29eeeda267b58bf2"}, {"name": "minify-js", "duration": 208, "timestamp": 1509012478869, "id": 60, "parentId": 53, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1750281276624, "traceId": "29eeeda267b58bf2"}, {"name": "minify-js", "duration": 200, "timestamp": 1509012478878, "id": 61, "parentId": 53, "tags": {"name": "447.js", "cache": "HIT"}, "startTime": 1750281276624, "traceId": "29eeeda267b58bf2"}, {"name": "minify-js", "duration": 191, "timestamp": 1509012478889, "id": 62, "parentId": 53, "tags": {"name": "145.js", "cache": "HIT"}, "startTime": 1750281276624, "traceId": "29eeeda267b58bf2"}, {"name": "minify-js", "duration": 104, "timestamp": 1509012478977, "id": 63, "parentId": 53, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1750281276624, "traceId": "29eeeda267b58bf2"}, {"name": "minify-webpack-plugin-optimize", "duration": 10510, "timestamp": 1509012468581, "id": 53, "parentId": 17, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1750281276614, "traceId": "29eeeda267b58bf2"}, {"name": "css-minimizer-plugin", "duration": 197, "timestamp": 1509012479334, "id": 64, "parentId": 17, "tags": {}, "startTime": 1750281276625, "traceId": "29eeeda267b58bf2"}, {"name": "create-trace-assets", "duration": 2266, "timestamp": 1509012479807, "id": 65, "parentId": 18, "tags": {}, "startTime": 1750281276625, "traceId": "29eeeda267b58bf2"}, {"name": "seal", "duration": 267015, "timestamp": 1509012227451, "id": 40, "parentId": 17, "tags": {}, "startTime": 1750281276373, "traceId": "29eeeda267b58bf2"}, {"name": "webpack-compilation", "duration": 1813015, "timestamp": 1509010689425, "id": 17, "parentId": 14, "tags": {"name": "server"}, "startTime": 1750281274835, "traceId": "29eeeda267b58bf2"}, {"name": "emit", "duration": 22517, "timestamp": 1509012503024, "id": 66, "parentId": 14, "tags": {}, "startTime": 1750281276648, "traceId": "29eeeda267b58bf2"}, {"name": "webpack-close", "duration": 1162, "timestamp": 1509012528862, "id": 67, "parentId": 14, "tags": {"name": "server"}, "startTime": 1750281276674, "traceId": "29eeeda267b58bf2"}, {"name": "webpack-generate-error-stats", "duration": 4122, "timestamp": 1509012530105, "id": 68, "parentId": 67, "tags": {}, "startTime": 1750281276676, "traceId": "29eeeda267b58bf2"}, {"name": "run-webpack-compiler", "duration": 2892992, "timestamp": 1509009641690, "id": 14, "parentId": 13, "tags": {}, "startTime": 1750281273787, "traceId": "29eeeda267b58bf2"}, {"name": "format-webpack-messages", "duration": 161, "timestamp": 1509012534712, "id": 69, "parentId": 13, "tags": {}, "startTime": 1750281276680, "traceId": "29eeeda267b58bf2"}, {"name": "worker-main-server", "duration": 2894035, "timestamp": 1509009641099, "id": 13, "parentId": 1, "tags": {}, "startTime": 1750281273787, "traceId": "29eeeda267b58bf2"}, {"name": "create-entrypoints", "duration": 24235, "timestamp": 1509014099475, "id": 72, "parentId": 70, "tags": {}, "startTime": 1750281278245, "traceId": "29eeeda267b58bf2"}, {"name": "generate-webpack-config", "duration": 703524, "timestamp": 1509014123940, "id": 73, "parentId": 71, "tags": {}, "startTime": 1750281278270, "traceId": "29eeeda267b58bf2"}, {"name": "make", "duration": 1319, "timestamp": 1509015083520, "id": 75, "parentId": 74, "tags": {}, "startTime": 1750281279229, "traceId": "29eeeda267b58bf2"}, {"name": "chunk-graph", "duration": 1021, "timestamp": 1509015090147, "id": 77, "parentId": 76, "tags": {}, "startTime": 1750281279236, "traceId": "29eeeda267b58bf2"}, {"name": "optimize-modules", "duration": 46, "timestamp": 1509015091374, "id": 79, "parentId": 76, "tags": {}, "startTime": 1750281279237, "traceId": "29eeeda267b58bf2"}, {"name": "optimize-chunks", "duration": 1582, "timestamp": 1509015091566, "id": 80, "parentId": 76, "tags": {}, "startTime": 1750281279237, "traceId": "29eeeda267b58bf2"}, {"name": "optimize-tree", "duration": 198, "timestamp": 1509015093268, "id": 81, "parentId": 76, "tags": {}, "startTime": 1750281279239, "traceId": "29eeeda267b58bf2"}, {"name": "optimize-chunk-modules", "duration": 871, "timestamp": 1509015093813, "id": 82, "parentId": 76, "tags": {}, "startTime": 1750281279240, "traceId": "29eeeda267b58bf2"}, {"name": "optimize", "duration": 3618, "timestamp": 1509015091278, "id": 78, "parentId": 76, "tags": {}, "startTime": 1750281279237, "traceId": "29eeeda267b58bf2"}, {"name": "module-hash", "duration": 137, "timestamp": 1509015096893, "id": 83, "parentId": 76, "tags": {}, "startTime": 1750281279243, "traceId": "29eeeda267b58bf2"}, {"name": "code-generation", "duration": 353, "timestamp": 1509015097104, "id": 84, "parentId": 76, "tags": {}, "startTime": 1750281279243, "traceId": "29eeeda267b58bf2"}, {"name": "hash", "duration": 666, "timestamp": 1509015097910, "id": 85, "parentId": 76, "tags": {}, "startTime": 1750281279244, "traceId": "29eeeda267b58bf2"}, {"name": "code-generation-jobs", "duration": 218, "timestamp": 1509015098571, "id": 86, "parentId": 76, "tags": {}, "startTime": 1750281279244, "traceId": "29eeeda267b58bf2"}, {"name": "module-assets", "duration": 135, "timestamp": 1509015098738, "id": 87, "parentId": 76, "tags": {}, "startTime": 1750281279245, "traceId": "29eeeda267b58bf2"}, {"name": "create-chunk-assets", "duration": 293, "timestamp": 1509015098887, "id": 88, "parentId": 76, "tags": {}, "startTime": 1750281279245, "traceId": "29eeeda267b58bf2"}, {"name": "minify-js", "duration": 379, "timestamp": 1509015117172, "id": 90, "parentId": 89, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1750281279263, "traceId": "29eeeda267b58bf2"}, {"name": "minify-webpack-plugin-optimize", "duration": 4084, "timestamp": 1509015113499, "id": 89, "parentId": 74, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1750281279259, "traceId": "29eeeda267b58bf2"}, {"name": "css-minimizer-plugin", "duration": 163, "timestamp": 1509015117738, "id": 91, "parentId": 74, "tags": {}, "startTime": 1750281279264, "traceId": "29eeeda267b58bf2"}, {"name": "seal", "duration": 35350, "timestamp": 1509015088843, "id": 76, "parentId": 74, "tags": {}, "startTime": 1750281279235, "traceId": "29eeeda267b58bf2"}, {"name": "webpack-compilation", "duration": 53009, "timestamp": 1509015071721, "id": 74, "parentId": 71, "tags": {"name": "edge-server"}, "startTime": 1750281279218, "traceId": "29eeeda267b58bf2"}, {"name": "emit", "duration": 4571, "timestamp": 1509015125291, "id": 92, "parentId": 71, "tags": {}, "startTime": 1750281279271, "traceId": "29eeeda267b58bf2"}, {"name": "webpack-close", "duration": 1129, "timestamp": 1509015131719, "id": 93, "parentId": 71, "tags": {"name": "edge-server"}, "startTime": 1750281279278, "traceId": "29eeeda267b58bf2"}, {"name": "webpack-generate-error-stats", "duration": 4205, "timestamp": 1509015132925, "id": 94, "parentId": 93, "tags": {}, "startTime": 1750281279279, "traceId": "29eeeda267b58bf2"}, {"name": "run-webpack-compiler", "duration": 1037954, "timestamp": 1509014099387, "id": 71, "parentId": 70, "tags": {}, "startTime": 1750281278245, "traceId": "29eeeda267b58bf2"}, {"name": "format-webpack-messages", "duration": 111, "timestamp": 1509015137352, "id": 95, "parentId": 70, "tags": {}, "startTime": 1750281279283, "traceId": "29eeeda267b58bf2"}, {"name": "worker-main-edge-server", "duration": 1038891, "timestamp": 1509014098795, "id": 70, "parentId": 1, "tags": {}, "startTime": 1750281278245, "traceId": "29eeeda267b58bf2"}, {"name": "create-entrypoints", "duration": 44678, "timestamp": 1509016618626, "id": 98, "parentId": 96, "tags": {}, "startTime": 1750281280765, "traceId": "29eeeda267b58bf2"}, {"name": "generate-webpack-config", "duration": 710809, "timestamp": 1509016663931, "id": 99, "parentId": 97, "tags": {}, "startTime": 1750281280810, "traceId": "29eeeda267b58bf2"}, {"name": "add-entry", "duration": 458633, "timestamp": 1509017653415, "id": 105, "parentId": 101, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1750281281800, "traceId": "29eeeda267b58bf2"}, {"name": "add-entry", "duration": 663185, "timestamp": 1509017653575, "id": 106, "parentId": 101, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1750281281800, "traceId": "29eeeda267b58bf2"}, {"name": "add-entry", "duration": 663213, "timestamp": 1509017653610, "id": 107, "parentId": 101, "tags": {"request": "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\node_modules\\next\\dist\\client\\router.js"}, "startTime": 1750281281800, "traceId": "29eeeda267b58bf2"}, {"name": "add-entry", "duration": 663249, "timestamp": 1509017653636, "id": 108, "parentId": 101, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1750281281800, "traceId": "29eeeda267b58bf2"}, {"name": "add-entry", "duration": 720002, "timestamp": 1509017653380, "id": 104, "parentId": 101, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1750281281800, "traceId": "29eeeda267b58bf2"}, {"name": "add-entry", "duration": 813595, "timestamp": 1509017653272, "id": 103, "parentId": 101, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1750281281799, "traceId": "29eeeda267b58bf2"}, {"name": "add-entry", "duration": 1395401, "timestamp": 1509017652467, "id": 102, "parentId": 101, "tags": {"request": "./node_modules/next/dist/client/next.js"}, "startTime": 1750281281799, "traceId": "29eeeda267b58bf2"}, {"name": "postcss-process", "duration": 265686, "timestamp": 1509019015925, "id": 114, "parentId": 113, "tags": {}, "startTime": 1750281283162, "traceId": "29eeeda267b58bf2"}, {"name": "postcss-loader", "duration": 1011216, "timestamp": 1509018270530, "id": 113, "parentId": 112, "tags": {}, "startTime": 1750281282417, "traceId": "29eeeda267b58bf2"}, {"name": "css-loader", "duration": 78508, "timestamp": 1509019282053, "id": 115, "parentId": 112, "tags": {"astUsed": "true"}, "startTime": 1750281283428, "traceId": "29eeeda267b58bf2"}, {"name": "add-entry", "duration": 1759790, "timestamp": 1509017653667, "id": 110, "parentId": 101, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CClasses.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CMembership.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CTestimonials.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CTrainers.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"}, "startTime": 1750281281800, "traceId": "29eeeda267b58bf2"}, {"name": "build-module-css", "duration": 1221895, "timestamp": 1509018193123, "id": 112, "parentId": 111, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\app\\globals.css", "layer": null}, "startTime": 1750281282339, "traceId": "29eeeda267b58bf2"}, {"name": "build-module-css", "duration": 1307155, "timestamp": 1509018128022, "id": 111, "parentId": 100, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\app\\globals.css", "layer": "app-pages-browser"}, "startTime": 1750281282274, "traceId": "29eeeda267b58bf2"}, {"name": "build-module", "duration": 337, "timestamp": 1509019435709, "id": 116, "parentId": 111, "tags": {}, "startTime": 1750281283582, "traceId": "29eeeda267b58bf2"}, {"name": "add-entry", "duration": 1782459, "timestamp": 1509017653651, "id": 109, "parentId": 101, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZbook%5C%5CDesktop%5C%5CStore%5C%5Cgym-website%5C%5Cneogym%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1750281281800, "traceId": "29eeeda267b58bf2"}, {"name": "make", "duration": 1784801, "timestamp": 1509017651633, "id": 101, "parentId": 100, "tags": {}, "startTime": 1750281281798, "traceId": "29eeeda267b58bf2"}, {"name": "chunk-graph", "duration": 17126, "timestamp": 1509019579921, "id": 118, "parentId": 117, "tags": {}, "startTime": 1750281283726, "traceId": "29eeeda267b58bf2"}, {"name": "optimize-modules", "duration": 73, "timestamp": 1509019597488, "id": 120, "parentId": 117, "tags": {}, "startTime": 1750281283744, "traceId": "29eeeda267b58bf2"}]