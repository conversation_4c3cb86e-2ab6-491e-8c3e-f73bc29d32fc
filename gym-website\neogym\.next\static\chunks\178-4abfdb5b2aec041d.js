"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[178],{224:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])},488:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},760:(t,e,i)=>{i.d(e,{N:()=>v});var n=i(5155),r=i(2115),s=i(869),a=i(2885),o=i(7494),l=i(845),u=i(7351),h=i(1508);class d extends r.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=(0,u.s)(t)&&t.offsetWidth||0,n=this.props.sizeRef.current;n.height=e.offsetHeight||0,n.width=e.offsetWidth||0,n.top=e.offsetTop,n.left=e.offsetLeft,n.right=i-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function c(t){let{children:e,isPresent:i,anchorX:s}=t,a=(0,r.useId)(),o=(0,r.useRef)(null),l=(0,r.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,r.useContext)(h.Q);return(0,r.useInsertionEffect)(()=>{let{width:t,height:e,top:n,left:r,right:h}=l.current;if(i||!o.current||!t||!e)return;o.current.dataset.motionPopId=a;let d=document.createElement("style");return u&&(d.nonce=u),document.head.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===s?"left: ".concat(r):"right: ".concat(h),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{document.head.contains(d)&&document.head.removeChild(d)}},[i]),(0,n.jsx)(d,{isPresent:i,childRef:o,sizeRef:l,children:r.cloneElement(e,{ref:o})})}let p=t=>{let{children:e,initial:i,isPresent:s,onExitComplete:o,custom:u,presenceAffectsLayout:h,mode:d,anchorX:p}=t,f=(0,a.M)(m),y=(0,r.useId)(),g=!0,v=(0,r.useMemo)(()=>(g=!1,{id:y,initial:i,isPresent:s,custom:u,onExitComplete:t=>{for(let e of(f.set(t,!0),f.values()))if(!e)return;o&&o()},register:t=>(f.set(t,!1),()=>f.delete(t))}),[s,f,o]);return h&&g&&(v={...v}),(0,r.useMemo)(()=>{f.forEach((t,e)=>f.set(e,!1))},[s]),r.useEffect(()=>{s||f.size||!o||o()},[s]),"popLayout"===d&&(e=(0,n.jsx)(c,{isPresent:s,anchorX:p,children:e})),(0,n.jsx)(l.t.Provider,{value:v,children:e})};function m(){return new Map}var f=i(2082);let y=t=>t.key||"";function g(t){let e=[];return r.Children.forEach(t,t=>{(0,r.isValidElement)(t)&&e.push(t)}),e}let v=t=>{let{children:e,custom:i,initial:l=!0,onExitComplete:u,presenceAffectsLayout:h=!0,mode:d="sync",propagate:c=!1,anchorX:m="left"}=t,[v,x]=(0,f.xQ)(c),w=(0,r.useMemo)(()=>g(e),[e]),A=c&&!v?[]:w.map(y),b=(0,r.useRef)(!0),T=(0,r.useRef)(w),P=(0,a.M)(()=>new Map),[M,S]=(0,r.useState)(w),[k,E]=(0,r.useState)(w);(0,o.E)(()=>{b.current=!1,T.current=w;for(let t=0;t<k.length;t++){let e=y(k[t]);A.includes(e)?P.delete(e):!0!==P.get(e)&&P.set(e,!1)}},[k,A.length,A.join("-")]);let V=[];if(w!==M){let t=[...w];for(let e=0;e<k.length;e++){let i=k[e],n=y(i);A.includes(n)||(t.splice(e,0,i),V.push(i))}return"wait"===d&&V.length&&(t=V),E(g(t)),S(w),null}let{forceRender:C}=(0,r.useContext)(s.L);return(0,n.jsx)(n.Fragment,{children:k.map(t=>{let e=y(t),r=(!c||!!v)&&(w===k||A.includes(e));return(0,n.jsx)(p,{isPresent:r,initial:(!b.current||!!l)&&void 0,custom:i,presenceAffectsLayout:h,mode:d,onExitComplete:r?void 0:()=>{if(!P.has(e))return;P.set(e,!0);let t=!0;P.forEach(e=>{e||(t=!1)}),t&&(null==C||C(),E(T.current),c&&(null==x||x()),u&&u())},anchorX:m,children:t},e)})})}},845:(t,e,i)=>{i.d(e,{t:()=>n});let n=(0,i(2115).createContext)(null)},869:(t,e,i)=>{i.d(e,{L:()=>n});let n=(0,i(2115).createContext)({})},1007:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1508:(t,e,i)=>{i.d(e,{Q:()=>n});let n=(0,i(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},1539:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},1586:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},1714:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("headset",[["path",{d:"M3 11h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-5Zm0 0a9 9 0 1 1 18 0m0 0v5a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3Z",key:"12oyoe"}],["path",{d:"M21 16v2a4 4 0 0 1-4 4h-5",key:"1x7m43"}]])},2082:(t,e,i)=>{i.d(e,{xQ:()=>s});var n=i(2115),r=i(845);function s(t=!0){let e=(0,n.useContext)(r.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:a,register:o}=e,l=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return o(l)},[t]);let u=(0,n.useCallback)(()=>t&&a&&a(l),[l,a,t]);return!i&&a?[!1,u]:[!0]}},2138:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2355:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},2486:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},2657:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2664:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isLocalURL",{enumerable:!0,get:function(){return s}});let n=i(9991),r=i(7102);function s(t){if(!(0,n.isAbsoluteUrl)(t))return!0;try{let e=(0,n.getLocationOrigin)(),i=new URL(t,e);return i.origin===e&&(0,r.hasBasePath)(i.pathname)}catch(t){return!1}}},2757:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{formatUrl:function(){return s},formatWithValidation:function(){return o},urlObjectKeys:function(){return a}});let n=i(6966)._(i(8859)),r=/https?|ftp|gopher|file/;function s(t){let{auth:e,hostname:i}=t,s=t.protocol||"",a=t.pathname||"",o=t.hash||"",l=t.query||"",u=!1;e=e?encodeURIComponent(e).replace(/%3A/i,":")+"@":"",t.host?u=e+t.host:i&&(u=e+(~i.indexOf(":")?"["+i+"]":i),t.port&&(u+=":"+t.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let h=t.search||l&&"?"+l||"";return s&&!s.endsWith(":")&&(s+=":"),t.slashes||(!s||r.test(s))&&!1!==u?(u="//"+(u||""),a&&"/"!==a[0]&&(a="/"+a)):u||(u=""),o&&"#"!==o[0]&&(o="#"+o),h&&"?"!==h[0]&&(h="?"+h),""+s+u+(a=a.replace(/[?#]/g,encodeURIComponent))+(h=h.replace("#","%23"))+o}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function o(t){return s(t)}},2885:(t,e,i)=>{i.d(e,{M:()=>r});var n=i(2115);function r(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},2925:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]])},3052:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3180:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"errorOnce",{enumerable:!0,get:function(){return i}});let i=t=>{}},3314:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("cpu",[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]])},4186:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4416:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4516:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4783:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},5196:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5684:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},5690:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},6408:(t,e,i)=>{let n;function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function a(t,e,i,n){if("function"==typeof e){let[r,a]=s(n);e=e(void 0!==i?i:t.custom,r,a)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,a]=s(n);e=e(void 0!==i?i:t.custom,r,a)}return e}function o(t,e,i){let n=t.getProps();return a(n,e,void 0!==i?i:n.custom,t)}function l(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>sk});let u=t=>t,h={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],c={value:null,addProjectionMetrics:null};function p(t,e){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,a=d.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,s=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){a.has(e)&&(h.schedule(e),t()),l++,e(o)}let h={schedule:(t,e=!1,s=!1)=>{let o=s&&r?i:n;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{n.delete(t),a.delete(t)},process:t=>{if(o=t,r){s=!0;return}r=!0,[i,n]=[n,i],i.forEach(u),e&&c.value&&c.value.frameloop[e].push(l),l=0,i.clear(),r=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{setup:o,read:l,resolveKeyframes:u,preUpdate:p,update:m,preRender:f,render:y,postRender:g}=a,v=()=>{let s=h.useManualTiming?r.timestamp:performance.now();i=!1,h.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,o.process(r),l.process(r),u.process(r),p.process(r),m.process(r),f.process(r),y.process(r),g.process(r),r.isProcessing=!1,i&&e&&(n=!1,t(v))},x=()=>{i=!0,n=!0,r.isProcessing||t(v)};return{schedule:d.reduce((t,e)=>{let n=a[e];return t[e]=(t,e=!1,r=!1)=>(i||x(),n.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<d.length;e++)a[d[e]].cancel(t)},state:r,steps:a}}let{schedule:m,cancel:f,state:y,steps:g}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],x=new Set(v),w=new Set(["width","height","top","left","right","bottom",...v]);function A(t,e){-1===t.indexOf(e)&&t.push(e)}function b(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class T{constructor(){this.subscriptions=[]}add(t){return A(this.subscriptions,t),()=>b(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function P(){n=void 0}let M={now:()=>(void 0===n&&M.set(y.isProcessing||h.useManualTiming?y.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(P)}},S=t=>!isNaN(parseFloat(t)),k={current:void 0};class E{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=M.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=M.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=S(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new T);let i=this.events[t].add(e);return"change"===t?()=>{i(),m.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return k.current&&k.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=M.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function V(t,e){return new E(t,e)}let C=t=>Array.isArray(t),D=t=>!!(t&&t.getVelocity);function R(t,e){let i=t.getValue("willChange");if(D(i)&&i.add)return i.add(e);if(!i&&h.WillChange){let i=new h.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let j=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),L="data-"+j("framerAppearId"),F=(t,e)=>i=>e(t(i)),O=(...t)=>t.reduce(F),B=(t,e,i)=>i>e?e:i<t?t:i,I=t=>1e3*t,U=t=>t/1e3,N={layout:0,mainThread:0,waapi:0},z=()=>{},W=()=>{},$=t=>e=>"string"==typeof e&&e.startsWith(t),_=$("--"),q=$("var(--"),H=t=>!!q(t)&&Y.test(t.split("/*")[0].trim()),Y=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,X={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},K={...X,transform:t=>B(0,1,t)},Z={...X,default:1},G=t=>Math.round(1e5*t)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>i=>!!("string"==typeof i&&J.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),te=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,s,a,o]=n.match(Q);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},ti=t=>B(0,255,t),tn={...X,transform:t=>Math.round(ti(t))},tr={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tn.transform(t)+", "+tn.transform(e)+", "+tn.transform(i)+", "+G(K.transform(n))+")"},ts={test:tt("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:tr.transform},ta=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),to=ta("deg"),tl=ta("%"),tu=ta("px"),th=ta("vh"),td=ta("vw"),tc={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},tp={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+tl.transform(G(e))+", "+tl.transform(G(i))+", "+G(K.transform(n))+")"},tm={test:t=>tr.test(t)||ts.test(t)||tp.test(t),parse:t=>tr.test(t)?tr.parse(t):tp.test(t)?tp.parse(t):ts.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tr.transform(t):tp.transform(t),getAnimatableNone:t=>{let e=tm.parse(t);return e.alpha=0,tm.transform(e)}},tf=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,ty="number",tg="color",tv=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tx(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,a=e.replace(tv,t=>(tm.test(t)?(n.color.push(s),r.push(tg),i.push(tm.parse(t))):t.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(t)):(n.number.push(s),r.push(ty),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:a,indexes:n,types:r}}function tw(t){return tx(t).values}function tA(t){let{split:e,types:i}=tx(t),n=e.length;return t=>{let r="";for(let s=0;s<n;s++)if(r+=e[s],void 0!==t[s]){let e=i[s];e===ty?r+=G(t[s]):e===tg?r+=tm.transform(t[s]):r+=t[s]}return r}}let tb=t=>"number"==typeof t?0:tm.test(t)?tm.getAnimatableNone(t):t,tT={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Q)?.length||0)+(t.match(tf)?.length||0)>0},parse:tw,createTransformer:tA,getAnimatableNone:function(t){let e=tw(t);return tA(t)(e.map(tb))}};function tP(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tM(t,e){return i=>i>0?e:t}let tS=(t,e,i)=>t+(e-t)*i,tk=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},tE=[ts,tr,tp],tV=t=>tE.find(e=>e.test(t));function tC(t){let e=tV(t);if(z(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tp&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,a=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,o=2*i-n;r=tP(o,n,t+1/3),s=tP(o,n,t),a=tP(o,n,t-1/3)}else r=s=a=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*a),alpha:n}}(i)),i}let tD=(t,e)=>{let i=tC(t),n=tC(e);if(!i||!n)return tM(t,e);let r={...i};return t=>(r.red=tk(i.red,n.red,t),r.green=tk(i.green,n.green,t),r.blue=tk(i.blue,n.blue,t),r.alpha=tS(i.alpha,n.alpha,t),tr.transform(r))},tR=new Set(["none","hidden"]);function tj(t,e){return i=>tS(t,e,i)}function tL(t){return"number"==typeof t?tj:"string"==typeof t?H(t)?tM:tm.test(t)?tD:tB:Array.isArray(t)?tF:"object"==typeof t?tm.test(t)?tD:tO:tM}function tF(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>tL(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function tO(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=tL(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let tB=(t,e)=>{let i=tT.createTransformer(e),n=tx(t),r=tx(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?tR.has(t)&&!r.values.length||tR.has(e)&&!n.values.length?function(t,e){return tR.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):O(tF(function(t,e){let i=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],a=t.indexes[s][n[s]],o=t.values[a]??0;i[r]=o,n[s]++}return i}(n,r),r.values),i):(z(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tM(t,e))};function tI(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tS(t,e,i):tL(t)(t,e)}let tU=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>m.update(e,t),stop:()=>f(e),now:()=>y.isProcessing?y.timestamp:M.now()}},tN=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=Math.round(1e4*t(e/(r-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function tz(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tW(t,e,i){var n,r;let s=Math.max(e-5,0);return n=i-t(s),(r=e-s)?1e3/r*n:0}let t$={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function t_(t,e){return t*Math.sqrt(1-e*e)}let tq=["duration","bounce"],tH=["stiffness","damping","mass"];function tY(t,e){return e.some(e=>void 0!==t[e])}function tX(t=t$.visualDuration,e=t$.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=n,a=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:t$.velocity,stiffness:t$.stiffness,damping:t$.damping,mass:t$.mass,isResolvedFromDuration:!1,...t};if(!tY(t,tH)&&tY(t,tq))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*B(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:t$.mass,stiffness:n,damping:r}}else{let i=function({duration:t=t$.duration,bounce:e=t$.bounce,velocity:i=t$.velocity,mass:n=t$.mass}){let r,s;z(t<=I(t$.maxDuration),"Spring duration must be 10 seconds or less");let a=1-e;a=B(t$.minDamping,t$.maxDamping,a),t=B(t$.minDuration,t$.maxDuration,U(t)),a<1?(r=e=>{let n=e*a,r=n*t;return .001-(n-i)/t_(e,a)*Math.exp(-r)},s=e=>{let n=e*a*t,s=Math.pow(a,2)*Math.pow(e,2)*t,o=Math.exp(-n),l=t_(Math.pow(e,2),a);return(n*i+i-s)*o*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let o=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=I(t),isNaN(o))return{stiffness:t$.stiffness,damping:t$.damping,duration:t};{let e=Math.pow(o,2)*n;return{stiffness:e,damping:2*a*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:t$.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-U(n.velocity||0)}),f=p||0,y=h/(2*Math.sqrt(u*d)),g=o-a,v=U(Math.sqrt(u/d)),x=5>Math.abs(g);if(r||(r=x?t$.restSpeed.granular:t$.restSpeed.default),s||(s=x?t$.restDelta.granular:t$.restDelta.default),y<1){let t=t_(v,y);i=e=>o-Math.exp(-y*v*e)*((f+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===y)i=t=>o-Math.exp(-v*t)*(g+(f+v*g)*t);else{let t=v*Math.sqrt(y*y-1);i=e=>{let i=Math.exp(-y*v*e),n=Math.min(t*e,300);return o-i*((f+y*v*g)*Math.sinh(n)+t*g*Math.cosh(n))/t}}let w={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let n=0===t?f:0;y<1&&(n=0===t?I(f):tW(i,t,e));let a=Math.abs(o-e)<=s;l.done=Math.abs(n)<=r&&a}return l.value=l.done?o:e,l},toString:()=>{let t=Math.min(tz(w),2e4),e=tN(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function tK({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:h}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==o&&t<o||void 0!==l&&t>l,y=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l,g=i*e,v=p+g,x=void 0===a?v:a(v);x!==v&&(g=x-p);let w=t=>-g*Math.exp(-t/n),A=t=>x+w(t),b=t=>{let e=w(t),i=A(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},T=t=>{f(m.value)&&(d=t,c=tX({keyframes:[m.value,y(m.value)],velocity:tW(A,t,m.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,b(t),T(t)),void 0!==d&&t>=d)?c.next(t-d):(e||b(t),m)}}}tX.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min(tz(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:U(r)}}(t,100,tX);return t.ease=e.ease,t.duration=I(e.duration),t.type="keyframes",t};let tZ=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tG(t,e,i,n){if(t===e&&i===n)return u;let r=e=>(function(t,e,i,n,r){let s,a,o=0;do(s=tZ(a=e+(i-e)/2,n,r)-t)>0?i=a:e=a;while(Math.abs(s)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:tZ(r(t),e,n)}let tQ=tG(.42,0,1,1),tJ=tG(0,0,.58,1),t0=tG(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t2=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t5=t=>e=>1-t(1-e),t4=tG(.33,1.53,.69,.99),t9=t5(t4),t6=t2(t9),t3=t=>(t*=2)<1?.5*t9(t):.5*(2-Math.pow(2,-10*(t-1))),t8=t=>1-Math.sin(Math.acos(t)),t7=t5(t8),et=t2(t8),ee=t=>Array.isArray(t)&&"number"==typeof t[0],ei={linear:u,easeIn:tQ,easeInOut:t0,easeOut:tJ,circIn:t8,circInOut:et,circOut:t7,backIn:t9,backInOut:t6,backOut:t4,anticipate:t3},en=t=>"string"==typeof t,er=t=>{if(ee(t)){W(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,r]=t;return tG(e,i,n,r)}return en(t)?(W(void 0!==ei[t],`Invalid easing type '${t}'`),ei[t]):t},es=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function ea({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var r;let s=t1(n)?n.map(er):er(n),a={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if(W(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let a=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let n=[],r=i||h.mix||tI,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=O(Array.isArray(e)?e[i]||u:e,s)),n.push(s)}return n}(e,n,r),l=o.length,d=i=>{if(a&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=es(t[n],t[n+1],i);return o[n](r)};return i?e=>d(B(t[0],t[s-1],e)):d}((r=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=es(0,e,n);t.push(tS(i,1,r))}}(e,t.length-1),e}(e),r.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(a.value=o(e),a.done=e>=t,a)}}let eo=t=>null!==t;function el(t,{repeat:e,repeatType:i="loop"},n,r=1){let s=t.filter(eo),a=r<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return a&&void 0!==n?n:s[a]}let eu={decay:tK,inertia:tK,tween:ea,keyframes:ea,spring:tX};function eh(t){"string"==typeof t.type&&(t.type=eu[t.type])}class ed{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ec=t=>t/100;class ep extends ed{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==M.now()&&this.tick(M.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},N.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;eh(t);let{type:e=ea,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=t,{keyframes:a}=t,o=e||ea;o!==ea&&"number"!=typeof a[0]&&(this.mixKeyframes=O(ec,tI(a[0],a[1])),a=[0,100]);let l=o({...t,keyframes:a});"mirror"===r&&(this.mirroredGenerator=o({...t,keyframes:[...a].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=tz(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?y<0:y>n;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=i;if(h){let t=Math.min(this.currentTime,n)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/a)):"mirror"===d&&(x=s)),v=B(0,1,i)*a}let w=g?{done:!1,value:u[0]}:x.next(v);r&&(w.value=r(w.value));let{done:A}=w;g||null===o||(A=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let b=null===this.holdTime&&("finished"===this.state||"running"===this.state&&A);return b&&p!==tK&&(w.value=el(u,this.options,f,this.speed)),m&&m(w.value),b&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return U(this.calculatedDuration)}get time(){return U(this.currentTime)}set time(t){t=I(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(M.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=U(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tU,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(M.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,N.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let em=t=>180*t/Math.PI,ef=t=>eg(em(Math.atan2(t[1],t[0]))),ey={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ef,rotateZ:ef,skewX:t=>em(Math.atan(t[1])),skewY:t=>em(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},eg=t=>((t%=360)<0&&(t+=360),t),ev=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ex=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ew={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ev,scaleY:ex,scale:t=>(ev(t)+ex(t))/2,rotateX:t=>eg(em(Math.atan2(t[6],t[5]))),rotateY:t=>eg(em(Math.atan2(-t[2],t[0]))),rotateZ:ef,rotate:ef,skewX:t=>em(Math.atan(t[4])),skewY:t=>em(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eA(t){return+!!t.includes("scale")}function eb(t,e){let i,n;if(!t||"none"===t)return eA(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=ew,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=ey,n=e}if(!n)return eA(e);let s=i[e],a=n[1].split(",").map(eP);return"function"==typeof s?s(a):a[s]}let eT=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eb(i,e)};function eP(t){return parseFloat(t.trim())}let eM=t=>t===X||t===tu,eS=new Set(["x","y","z"]),ek=v.filter(t=>!eS.has(t)),eE={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eb(e,"x"),y:(t,{transform:e})=>eb(e,"y")};eE.translateX=eE.x,eE.translateY=eE.y;let eV=new Set,eC=!1,eD=!1,eR=!1;function ej(){if(eD){let t=Array.from(eV).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ek.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eD=!1,eC=!1,eV.forEach(t=>t.complete(eR)),eV.clear()}function eL(){eV.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eD=!0)})}class eF{constructor(t,e,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(eV.add(this),eC||(eC=!0,m.read(eL),m.resolveKeyframes(ej))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let r=n?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eV.delete(this)}cancel(){"scheduled"===this.state&&(eV.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eO=t=>t.startsWith("--");function eB(t){let e;return()=>(void 0===e&&(e=t()),e)}let eI=eB(()=>void 0!==window.ScrollTimeline),eU={},eN=function(t,e){let i=eB(t);return()=>eU[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),ez=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,eW={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ez([0,.65,.55,1]),circOut:ez([.55,0,1,.45]),backIn:ez([.31,.01,.66,-.59]),backOut:ez([.33,1.53,.69,.99])};function e$(t){return"function"==typeof t&&"applyToOptions"in t}class e_ extends ed{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,W("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return e$(t)&&eN()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:a="loop",ease:o="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?eN()?tN(e,i):"ease-out":ee(e)?ez(e):Array.isArray(e)?e.map(e=>t(e,i)||eW.easeOut):eW[e]}(o,r);Array.isArray(d)&&(h.easing=d),c.value&&N.waapi++;let p={delay:n,duration:r,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:"reverse"===a?"alternate":"normal"};u&&(p.pseudoElement=u);let m=t.animate(h,p);return c.value&&m.finished.finally(()=>{N.waapi--}),m}(e,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=el(n,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eO(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return U(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return U(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=I(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eI())?(this.animation.timeline=t,u):e(this)}}let eq={anticipate:t3,backInOut:t6,circInOut:et};class eH extends e_{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eq&&(t.ease=eq[t.ease])}(t),eh(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let a=new ep({...s,autoplay:!1}),o=I(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let eY=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tT.test(t)||"0"===t)&&!t.startsWith("url("));var eX,eK,eZ=i(7351);let eG=new Set(["opacity","clipPath","filter","transform"]),eQ=eB(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eJ extends ed{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:a,name:o,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=M.now();let d={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,name:o,motionValue:l,element:u,...h},c=u?.KeyframeResolver||eF;this.keyframeResolver=new c(a,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:a,delay:o,isHandoff:l,onUpdate:d}=i;this.resolvedAt=M.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],a=eY(r,e),o=eY(s,e);return z(a===o,`You are trying to animate ${e} from "${r}" to "${s}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${s} via the \`style\` property.`),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||e$(i))&&n)}(t,r,s,a)&&((h.instantAnimations||!o)&&d?.(el(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let c={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},p=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:a}=t;if(!(0,eZ.s)(e?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return eQ()&&i&&eG.has(i)&&("transform"!==i||!l)&&!o&&!n&&"mirror"!==r&&0!==s&&"inertia"!==a}(c)?new eH({...c,element:c.motionValue.owner.current}):new ep(c);p.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eR=!0,eL(),ej(),eR=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e0=t=>null!==t,e1={type:"spring",stiffness:500,damping:25,restSpeed:10},e2=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e5={type:"keyframes",duration:.8},e4={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e9=(t,{keyframes:e})=>e.length>2?e5:x.has(t)?t.startsWith("scale")?e2(e[1]):e1:e4,e6=(t,e,i,n={},r,s)=>a=>{let o=l(n,t)||{},u=o.delay||n.delay||0,{elapsed:d=0}=n;d-=I(u);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...o,delay:-d,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:a,repeatDelay:o,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(o)&&Object.assign(c,e9(t,c)),c.duration&&(c.duration=I(c.duration)),c.repeatDelay&&(c.repeatDelay=I(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let p=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(p=!0)),(h.instantAnimations||h.skipAnimations)&&(p=!0,c.duration=0,c.delay=0),c.allowFlatten=!o.type&&!o.ease,p&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(e0),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return r[s]}(c.keyframes,o);if(void 0!==t)return void m.update(()=>{c.onUpdate(t),c.onComplete()})}return o.isSync?new ep(c):new eJ(c)};function e3(t,e,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:a,...u}=e;n&&(s=n);let h=[],d=r&&t.animationState&&t.animationState.getState()[r];for(let e in u){let n=t.getValue(e,t.latestValues[e]??null),r=u[e];if(void 0===r||d&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(d,e))continue;let a={delay:i,...l(s||{},e)},o=n.get();if(void 0!==o&&!n.isAnimating&&!Array.isArray(r)&&r===o&&!a.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){let i=t.props[L];if(i){let t=window.MotionHandoffAnimation(i,e,m);null!==t&&(a.startTime=t,c=!0)}}R(t,e),n.start(e6(e,n,r,t.shouldReduceMotion&&w.has(e)?{type:!1}:a,t,c));let p=n.animation;p&&h.push(p)}return a&&Promise.all(h).then(()=>{m.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=o(t,e)||{};for(let e in r={...r,...i}){var s;let i=C(s=r[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,V(i))}}(t,a)})}),h}function e8(t,e,i={}){let n=o(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(e3(t,n,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:a,staggerDirection:o}=r;return function(t,e,i=0,n=0,r=1,s){let a=[],o=(t.variantChildren.size-1)*n,l=1===r?(t=0)=>t*n:(t=0)=>o-t*n;return Array.from(t.variantChildren).sort(e7).forEach((t,n)=>{t.notify("AnimationStart",e),a.push(e8(t,e,{...s,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,s+n,a,o,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([s(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[s,a]:[a,s];return t().then(()=>e())}}function e7(t,e){return t.sortNodePosition(e)}function it(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function ie(t){return"string"==typeof t||Array.isArray(t)}let ii=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ir=["initial",...ii],is=ir.length,ia=[...ii].reverse(),io=ii.length;function il(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iu(){return{animate:il(!0),whileInView:il(),whileHover:il(),whileTap:il(),whileDrag:il(),whileFocus:il(),exit:il()}}class ih{constructor(t){this.isMounted=!1,this.node=t}update(){}}class id extends ih{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>e8(t,e,i)));else if("string"==typeof e)n=e8(t,e,i);else{let r="function"==typeof e?o(t,e,i.custom):e;n=Promise.all(e3(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iu(),n=!0,s=e=>(i,n)=>{let r=o(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...n}=r;i={...i,...n,...e}}return i};function a(a){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<is;t++){let n=ir[t],r=e.props[n];(ie(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},h=[],d=new Set,c={},p=1/0;for(let e=0;e<io;e++){var m,f;let o=ia[e],y=i[o],g=void 0!==l[o]?l[o]:u[o],v=ie(g),x=o===a?y.isActive:null;!1===x&&(p=e);let w=g===u[o]&&g!==l[o]&&v;if(w&&n&&t.manuallyAnimateOnMount&&(w=!1),y.protectedKeys={...c},!y.isActive&&null===x||!g&&!y.prevProp||r(g)||"boolean"==typeof g)continue;let A=(m=y.prevProp,"string"==typeof(f=g)?f!==m:!!Array.isArray(f)&&!it(f,m)),b=A||o===a&&y.isActive&&!w&&v||e>p&&v,T=!1,P=Array.isArray(g)?g:[g],M=P.reduce(s(o),{});!1===x&&(M={});let{prevResolvedValues:S={}}=y,k={...S,...M},E=e=>{b=!0,d.has(e)&&(T=!0,d.delete(e)),y.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in k){let e=M[t],i=S[t];if(c.hasOwnProperty(t))continue;let n=!1;(C(e)&&C(i)?it(e,i):e===i)?void 0!==e&&d.has(t)?E(t):y.protectedKeys[t]=!0:null!=e?E(t):d.add(t)}y.prevProp=g,y.prevResolvedValues=M,y.isActive&&(c={...c,...M}),n&&t.blockInitialAnimation&&(b=!1);let V=!(w&&A)||T;b&&V&&h.push(...P.map(t=>({animation:t,options:{type:o}})))}if(d.size){let e={};if("boolean"!=typeof l.initial){let i=o(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=n??null}),h.push({animation:e})}let y=!!h.length;return n&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(y=!1),n=!1,y?e(h):Promise.resolve()}return{animateChanges:a,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let r=a(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iu(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ic=0;class ip extends ih{constructor(){super(...arguments),this.id=ic++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let im={x:!1,y:!1};function iy(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let ig=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iv(t){return{point:{x:t.pageX,y:t.pageY}}}let ix=t=>e=>ig(e)&&t(e,iv(e));function iw(t,e,i,n){return iy(t,e,ix(i),n)}function iA({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function ib(t){return t.max-t.min}function iT(t,e,i,n=.5){t.origin=n,t.originPoint=tS(e.min,e.max,t.origin),t.scale=ib(i)/ib(e),t.translate=tS(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iP(t,e,i,n){iT(t.x,e.x,i.x,n?n.originX:void 0),iT(t.y,e.y,i.y,n?n.originY:void 0)}function iM(t,e,i){t.min=i.min+e.min,t.max=t.min+ib(e)}function iS(t,e,i){t.min=e.min-i.min,t.max=t.min+ib(e)}function ik(t,e,i){iS(t.x,e.x,i.x),iS(t.y,e.y,i.y)}let iE=()=>({translate:0,scale:1,origin:0,originPoint:0}),iV=()=>({x:iE(),y:iE()}),iC=()=>({min:0,max:0}),iD=()=>({x:iC(),y:iC()});function iR(t){return[t("x"),t("y")]}function ij(t){return void 0===t||1===t}function iL({scale:t,scaleX:e,scaleY:i}){return!ij(t)||!ij(e)||!ij(i)}function iF(t){return iL(t)||iO(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iO(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iB(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function iI(t,e=0,i=1,n,r){t.min=iB(t.min,e,i,n,r),t.max=iB(t.max,e,i,n,r)}function iU(t,{x:e,y:i}){iI(t.x,e.translate,e.scale,e.originPoint),iI(t.y,i.translate,i.scale,i.originPoint)}function iN(t,e){t.min=t.min+e,t.max=t.max+e}function iz(t,e,i,n,r=.5){let s=tS(t.min,t.max,r);iI(t,e,i,s,n)}function iW(t,e){iz(t.x,e.x,e.scaleX,e.scale,e.originX),iz(t.y,e.y,e.scaleY,e.scale,e.originY)}function i$(t,e){return iA(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let i_=({current:t})=>t?t.ownerDocument.defaultView:null;function iq(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iH=(t,e)=>Math.abs(t-e);class iY{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iZ(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iH(t.x,e.x)**2+iH(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=y;this.history.push({...n,timestamp:r});let{onStart:s,onMove:a}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iX(e,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iZ("pointercancel"===t.type?this.lastMoveEventInfo:iX(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!ig(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let s=iX(iv(t),this.transformPagePoint),{point:a}=s,{timestamp:o}=y;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=e;l&&l(t,iZ(s,this.history)),this.removeListeners=O(iw(this.contextWindow,"pointermove",this.handlePointerMove),iw(this.contextWindow,"pointerup",this.handlePointerUp),iw(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function iX(t,e){return e?{point:e(t.point)}:t}function iK(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iZ({point:t},e){return{point:t,delta:iK(t,iG(e)),offset:iK(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=iG(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>I(.1)));)i--;if(!n)return{x:0,y:0};let s=U(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let a={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function iG(t){return t[t.length-1]}function iQ(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iJ(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function i0(t,e,i){return{min:i1(t,e),max:i1(t,i)}}function i1(t,e){return"number"==typeof t?t:t[e]||0}let i2=new WeakMap;class i5{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iD(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new iY(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iv(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(im[t])return null;else return im[t]=!0,()=>{im[t]=!1};return im.x||im.y?null:(im.x=im.y=!0,()=>{im.x=im.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iR(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=ib(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&m.postRender(()=>r(t,e)),R(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iR(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:i_(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:r}=this.getProps();r&&m.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!i4(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?tS(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?tS(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&iq(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:iQ(t.x,i,r),y:iQ(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i0(t,"left","right"),y:i0(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iR(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iq(e))return!1;let n=e.current;W(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=i$(t,i),{scroll:r}=e;return r&&(iN(n.x,r.offset.x),iN(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),a=(t=r.layout.layoutBox,{x:iJ(t.x,s.x),y:iJ(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=iA(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(iR(a=>{if(!i4(a,e,this.currentDirection))return;let l=o&&o[a]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return R(this.visualElement,t),i.start(e6(t,i,0,e,this.visualElement,!1))}stopAnimation(){iR(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iR(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iR(e=>{let{drag:i}=this.getProps();if(!i4(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-tS(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iq(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iR(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=ib(t),r=ib(e);return r>n?i=es(e.min,e.max-n,t.min):n>r&&(i=es(t.min,t.max-r,e.min)),B(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iR(e=>{if(!i4(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(tS(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;i2.set(this.visualElement,this);let t=iw(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iq(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),m.read(e);let r=iy(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iR(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:a}}}function i4(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i9 extends ih{constructor(t){super(t),this.removeGroupControls=u,this.removeListeners=u,this.controls=new i5(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let i6=t=>(e,i)=>{t&&m.postRender(()=>t(e,i))};class i3 extends ih{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(t){this.session=new iY(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:i_(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:i6(t),onStart:i6(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&m.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=iw(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i8=i(5155);let{schedule:i7}=p(queueMicrotask,!1);var nt=i(2115),ne=i(2082),ni=i(869);let nn=(0,nt.createContext)({}),nr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ns(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let na={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tu.test(t))return t;else t=parseFloat(t);let i=ns(t,e.target.x),n=ns(t,e.target.y);return`${i}% ${n}%`}},no={};class nl extends nt.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;for(let t in nh)no[t]=nh[t],_(t)&&(no[t].isCSSVariable=!0);r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),nr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?s.promote():s.relegate()||m.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),i7.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nu(t){let[e,i]=(0,ne.xQ)(),n=(0,nt.useContext)(ni.L);return(0,i8.jsx)(nl,{...t,layoutGroup:n,switchLayoutGroup:(0,nt.useContext)(nn),isPresent:e,safeToRemove:i})}let nh={borderRadius:{...na,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:na,borderTopRightRadius:na,borderBottomLeftRadius:na,borderBottomRightRadius:na,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tT.parse(t);if(n.length>5)return t;let r=tT.createTransformer(t),s=+("number"!=typeof n[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;n[0+s]/=a,n[1+s]/=o;let l=tS(a,o,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}};var nd=i(6983);function nc(t){return(0,nd.G)(t)&&"ownerSVGElement"in t}let np=(t,e)=>t.depth-e.depth;class nm{constructor(){this.children=[],this.isDirty=!1}add(t){A(this.children,t),this.isDirty=!0}remove(t){b(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(np),this.isDirty=!1,this.children.forEach(t)}}function nf(t){return D(t)?t.get():t}let ny=["TopLeft","TopRight","BottomLeft","BottomRight"],ng=ny.length,nv=t=>"string"==typeof t?parseFloat(t):t,nx=t=>"number"==typeof t||tu.test(t);function nw(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nA=nT(0,.5,t7),nb=nT(.5,.95,u);function nT(t,e,i){return n=>n<t?0:n>e?1:i(es(t,e,n))}function nP(t,e){t.min=e.min,t.max=e.max}function nM(t,e){nP(t.x,e.x),nP(t.y,e.y)}function nS(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nk(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function nE(t,e,[i,n,r],s,a){!function(t,e=0,i=1,n=.5,r,s=t,a=t){if(tl.test(e)&&(e=parseFloat(e),e=tS(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=tS(s.min,s.max,n);t===s&&(o-=e),t.min=nk(t.min,e,i,o,r),t.max=nk(t.max,e,i,o,r)}(t,e[i],e[n],e[r],e.scale,s,a)}let nV=["x","scaleX","originX"],nC=["y","scaleY","originY"];function nD(t,e,i,n){nE(t.x,e,nV,i?i.x:void 0,n?n.x:void 0),nE(t.y,e,nC,i?i.y:void 0,n?n.y:void 0)}function nR(t){return 0===t.translate&&1===t.scale}function nj(t){return nR(t.x)&&nR(t.y)}function nL(t,e){return t.min===e.min&&t.max===e.max}function nF(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nO(t,e){return nF(t.x,e.x)&&nF(t.y,e.y)}function nB(t){return ib(t.x)/ib(t.y)}function nI(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nU{constructor(){this.members=[]}add(t){A(this.members,t),t.scheduleRender()}remove(t){if(b(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nN={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nz=["","X","Y","Z"],nW={visibility:"hidden"},n$=0;function n_(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function nq({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=n$++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,c.value&&(nN.nodes=nN.calculatedTargetDeltas=nN.calculatedProjections=0),this.nodes.forEach(nX),this.nodes.forEach(n1),this.nodes.forEach(n2),this.nodes.forEach(nK),c.addProjectionMetrics&&c.addProjectionMetrics(nN)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nm)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new T),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=nc(e)&&!(nc(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i,n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=M.now(),n=({timestamp:r})=>{let s=r-i;s>=250&&(f(n),t(s-e))};return m.setup(n,!0),()=>f(n)}(n,250),nr.hasAnimatedSinceResize&&(nr.hasAnimatedSinceResize=!1,this.nodes.forEach(n0))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||n8,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=r.getProps(),u=!this.targetLayout||!nO(this.targetLayout,n),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...l(s,"layout"),onPlay:a,onComplete:o};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||n0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n5),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[L];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",m,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nG);return}this.isUpdating||this.nodes.forEach(nQ),this.isUpdating=!1,this.nodes.forEach(nJ),this.nodes.forEach(nH),this.nodes.forEach(nY),this.clearAllSnapshots();let t=M.now();y.delta=B(0,1e3/60,t-y.timestamp),y.timestamp=t,y.isProcessing=!0,g.update.process(y),g.preRender.process(y),g.render.process(y),y.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,i7.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nZ),this.sharedNodes.forEach(n4)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||ib(this.snapshot.measuredBox.x)||ib(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iD(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nj(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||iF(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),re((e=n).x),re(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iD();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rn))){let{scroll:t}=this.root;t&&(iN(e.x,t.offset.x),iN(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iD();if(nM(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&nM(e,t),iN(e.x,r.offset.x),iN(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=iD();nM(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iW(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),iF(n.latestValues)&&iW(i,n.latestValues)}return iF(this.latestValues)&&iW(i,this.latestValues),i}removeTransform(t){let e=iD();nM(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iF(i.latestValues))continue;iL(i.latestValues)&&i.updateSnapshot();let n=iD();nM(n,i.measurePageBox()),nD(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return iF(this.latestValues)&&nD(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==y.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=y.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iD(),this.relativeTargetOrigin=iD(),ik(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nM(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iD(),this.targetWithTransforms=iD()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,a,o;this.forceRelativeParentToResolveTarget(),s=this.target,a=this.relativeTarget,o=this.relativeParent.target,iM(s.x,a.x,o.x),iM(s.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nM(this.target,this.layout.layoutBox),iU(this.target,this.targetDelta)):nM(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iD(),this.relativeTargetOrigin=iD(),ik(this.relativeTargetOrigin,this.target,t.target),nM(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}c.value&&nN.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iL(this.parent.latestValues)||iO(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===y.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;nM(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,n=!1){let r,s,a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){s=(r=i[o]).projectionDelta;let{visualElement:a}=r.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iW(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,iU(t,s)),n&&iF(r.latestValues)&&iW(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iD());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nS(this.prevProjectionDelta.x,this.projectionDelta.x),nS(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iP(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&nI(this.projectionDelta.x,this.prevProjectionDelta.x)&&nI(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),c.value&&nN.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iV(),this.projectionDelta=iV(),this.projectionDeltaWithTransform=iV()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},a=iV();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=iD(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(n3));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(n9(a.x,t.x,n),n9(a.y,t.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,y;ik(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,y=n,n6(p.x,m.x,f.x,y),n6(p.y,m.y,f.y,y),i&&(u=this.relativeTarget,c=i,nL(u.x,c.x)&&nL(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iD()),nM(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=tS(0,i.opacity??1,nA(n)),t.opacityExit=tS(e.opacity??1,0,nb(n))):s&&(t.opacity=tS(e.opacity??1,i.opacity??1,n));for(let r=0;r<ng;r++){let s=`border${ny[r]}Radius`,a=nw(e,s),o=nw(i,s);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||nx(a)===nx(o)?(t[s]=Math.max(tS(nv(a),nv(o),n),0),(tl.test(o)||tl.test(a))&&(t[s]+="%")):t[s]=o)}(e.rotate||i.rotate)&&(t.rotate=tS(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{nr.hasAnimatedSinceResize=!0,N.layout++,this.motionValue||(this.motionValue=V(0)),this.currentAnimation=function(t,e,i){let n=D(t)?t:V(t);return n.start(e6("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{N.layout--},onComplete:()=>{N.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&ri(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||iD();let e=ib(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=ib(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}nM(e,i),iW(e,r),iP(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nU),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&n_("z",t,n,this.animationValues);for(let e=0;e<nz.length;e++)n_(`rotate${nz[e]}`,t,n,this.animationValues),n_(`skew${nz[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return nW;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=nf(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=nf(t?.pointerEvents)||""),this.hasProjected&&!iF(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let r=n.animationValues||n.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,a=i?.z||0;if((r||s||a)&&(n=`translate3d(${r}px, ${s}px, ${a}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:a,skewY:o}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),a&&(n+=`skewX(${a}deg) `),o&&(n+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(n+=`scale(${o}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(e.transform=i(r,e.transform));let{x:s,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*a.origin}% 0`,n.animationValues?e.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,no){if(void 0===r[t])continue;let{correct:i,applyTo:s,isCSSVariable:a}=no[t],o="none"===e.transform?r[t]:i(r[t],n);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=o}else a?this.options.visualElement.renderState.vars[t]=o:e[t]=o}return this.options.layoutId&&(e.pointerEvents=n===this?nf(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(nG),this.root.sharedNodes.clear()}}}function nH(t){t.updateLayout()}function nY(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:r}=t.options,s=e.source!==t.layout.source;"size"===r?iR(t=>{let n=s?e.measuredBox[t]:e.layoutBox[t],r=ib(n);n.min=i[t].min,n.max=n.min+r}):ri(r,e.layoutBox,i)&&iR(n=>{let r=s?e.measuredBox[n]:e.layoutBox[n],a=ib(i[n]);r.max=r.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+a)});let a=iV();iP(a,i,e.layoutBox);let o=iV();s?iP(o,t.applyTransform(n,!0),e.measuredBox):iP(o,i,e.layoutBox);let l=!nj(a),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let a=iD();ik(a,e.layoutBox,r.layoutBox);let o=iD();ik(o,i,s.layoutBox),nO(a,o)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nX(t){c.value&&nN.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nK(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function nZ(t){t.clearSnapshot()}function nG(t){t.clearMeasurements()}function nQ(t){t.isLayoutDirty=!1}function nJ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function n0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function n1(t){t.resolveTargetDelta()}function n2(t){t.calcProjection()}function n5(t){t.resetSkewAndRotation()}function n4(t){t.removeLeadSnapshot()}function n9(t,e,i){t.translate=tS(e.translate,0,i),t.scale=tS(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function n6(t,e,i,n){t.min=tS(e.min,i.min,n),t.max=tS(e.max,i.max,n)}function n3(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let n8={duration:.45,ease:[.4,0,.1,1]},n7=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rt=n7("applewebkit/")&&!n7("chrome/")?Math.round:u;function re(t){t.min=rt(t.min),t.max=rt(t.max)}function ri(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nB(e)-nB(i)))}function rn(t){return t!==t.root&&t.scroll?.wasRoot}let rr=nq({attachResizeListener:(t,e)=>iy(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rs={current:void 0},ra=nq({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rs.current){let t=new rr({});t.mount(window),t.setOptions({layoutScroll:!0}),rs.current=t}return rs.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ro(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function rl(t){return!("touch"===t.pointerType||im.x||im.y)}function ru(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&m.postRender(()=>r(e,iv(e)))}class rh extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=ro(t,i),a=t=>{if(!rl(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{rl(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",a,r)}),s}(t,(t,e)=>(ru(this.node,e,"Start"),t=>ru(this.node,t,"End"))))}unmount(){}}class rd extends ih{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=O(iy(this.node.current,"focus",()=>this.onFocus()),iy(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rc=(t,e)=>!!e&&(t===e||rc(t,e.parentElement)),rp=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rm=new WeakSet;function rf(t){return e=>{"Enter"===e.key&&t(e)}}function ry(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let rg=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=rf(()=>{if(rm.has(i))return;ry(i,"down");let t=rf(()=>{ry(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>ry(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function rv(t){return ig(t)&&!(im.x||im.y)}function rx(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&m.postRender(()=>r(e,iv(e)))}class rw extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=ro(t,i),a=t=>{let n=t.currentTarget;if(!rv(t))return;rm.add(n);let s=e(n,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),rm.has(n)&&rm.delete(n),rv(t)&&"function"==typeof s&&s(t,{success:e})},o=t=>{a(t,n===window||n===document||i.useGlobalTarget||rc(n,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,r),(0,eZ.s)(t))&&(t.addEventListener("focus",t=>rg(t,r)),rp.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(rx(this.node,e,"Start"),(t,{success:e})=>rx(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rA=new WeakMap,rb=new WeakMap,rT=t=>{let e=rA.get(t.target);e&&e(t)},rP=t=>{t.forEach(rT)},rM={some:0,all:1};class rS extends ih{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rM[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;rb.has(i)||rb.set(i,{});let n=rb.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(rP,{root:t,...e})),n[r]}(e);return rA.set(t,i),n.observe(t),()=>{rA.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rk=(0,nt.createContext)({strict:!1});var rE=i(1508);let rV=(0,nt.createContext)({});function rC(t){return r(t.animate)||ir.some(e=>ie(t[e]))}function rD(t){return!!(rC(t)||t.variants)}function rR(t){return Array.isArray(t)?t.join(" "):t}var rj=i(8972);let rL={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rF={};for(let t in rL)rF[t]={isEnabled:e=>rL[t].some(t=>!!e[t])};let rO=Symbol.for("motionComponentSymbol");var rB=i(845),rI=i(7494);function rU(t,{layout:e,layoutId:i}){return x.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!no[t]||"opacity"===t)}let rN=(t,e)=>e&&"number"==typeof t?e.transform(t):t,rz={...X,transform:Math.round},rW={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:to,rotateX:to,rotateY:to,rotateZ:to,scale:Z,scaleX:Z,scaleY:Z,scaleZ:Z,skew:to,skewX:to,skewY:to,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:K,originX:tc,originY:tc,originZ:tu,zIndex:rz,fillOpacity:K,strokeOpacity:K,numOctaves:rz},r$={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},r_=v.length;function rq(t,e,i){let{style:n,vars:r,transformOrigin:s}=t,a=!1,o=!1;for(let t in e){let i=e[t];if(x.has(t)){a=!0;continue}if(_(t)){r[t]=i;continue}{let e=rN(i,rW[t]);t.startsWith("origin")?(o=!0,s[t]=e):n[t]=e}}if(!e.transform&&(a||i?n.transform=function(t,e,i){let n="",r=!0;for(let s=0;s<r_;s++){let a=v[s],o=t[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let t=rN(o,rW[a]);if(!l){r=!1;let e=r$[a]||a;n+=`${e}(${t}) `}i&&(e[a]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),o){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;n.transformOrigin=`${t} ${e} ${i}`}}let rH=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rY(t,e,i){for(let n in e)D(e[n])||rU(n,i)||(t[n]=e[n])}let rX={offset:"stroke-dashoffset",array:"stroke-dasharray"},rK={offset:"strokeDashoffset",array:"strokeDasharray"};function rZ(t,{attrX:e,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:a=0,...o},l,u,h){if(rq(t,o,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=h?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==n&&(d.scale=n),void 0!==r&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?rX:rK;t[s.offset]=tu.transform(-n);let a=tu.transform(e),o=tu.transform(i);t[s.array]=`${a} ${o}`}(d,r,s,a,!1)}let rG=()=>({...rH(),attrs:{}}),rQ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),rJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r0(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||rJ.has(t)}let r1=t=>!r0(t);try{!function(t){"function"==typeof t&&(r1=e=>e.startsWith("on")?!r0(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let r2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r5(t){if("string"!=typeof t||t.includes("-"));else if(r2.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var r4=i(2885);let r9=t=>(e,i)=>{let n=(0,nt.useContext)(rV),s=(0,nt.useContext)(rB.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,s){return{latestValues:function(t,e,i,n){let s={},o=n(t,{});for(let t in o)s[t]=nf(o[t]);let{initial:l,animate:u}=t,h=rC(t),d=rD(t);e&&d&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!r(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=a(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,n,s,t),renderState:e()}})(t,e,n,s);return i?o():(0,r4.M)(o)};function r6(t,e,i){let{style:n}=t,r={};for(let s in n)(D(n[s])||e.style&&D(e.style[s])||rU(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}let r3={useVisualState:r9({scrapeMotionValuesFromProps:r6,createRenderState:rH})};function r8(t,e,i){let n=r6(t,e,i);for(let i in t)(D(t[i])||D(e[i]))&&(n[-1!==v.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let r7={useVisualState:r9({scrapeMotionValuesFromProps:r8,createRenderState:rG})},st=t=>e=>e.test(t),se=[X,tu,tl,to,td,th,{test:t=>"auto"===t,parse:t=>t}],si=t=>se.find(st(t)),sn=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),sr=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ss=t=>/^0[^.\s]+$/u.test(t),sa=new Set(["brightness","contrast","saturate","opacity"]);function so(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(Q)||[];if(!n)return t;let r=i.replace(n,""),s=+!!sa.has(e);return n!==i&&(s*=100),e+"("+s+r+")"}let sl=/\b([a-z-]*)\(.*?\)/gu,su={...tT,getAnimatableNone:t=>{let e=t.match(sl);return e?e.map(so).join(" "):t}},sh={...rW,color:tm,backgroundColor:tm,outlineColor:tm,fill:tm,stroke:tm,borderColor:tm,borderTopColor:tm,borderRightColor:tm,borderBottomColor:tm,borderLeftColor:tm,filter:su,WebkitFilter:su},sd=t=>sh[t];function sc(t,e){let i=sd(t);return i!==su&&(i=tT),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let sp=new Set(["auto","none","0"]);class sm extends eF{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&H(n=n.trim())){let r=function t(e,i,n=1){W(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,s]=function(t){let e=sr.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${i??n}`,r]}(e);if(!r)return;let a=window.getComputedStyle(i).getPropertyValue(r);if(a){let t=a.trim();return sn(t)?parseFloat(t):t}return H(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!w.has(i)||2!==t.length)return;let[n,r]=t,s=si(n),a=si(r);if(s!==a)if(eM(s)&&eM(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eE[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||ss(n)))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!sp.has(e)&&tx(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=sc(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eE[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=eE[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let sf=[...se,tm,tT],sy=t=>sf.find(st(t)),sg={current:null},sv={current:!1},sx=new WeakMap,sw=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sA{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eF,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=M.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,m.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=s;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=rC(e),this.isVariantNode=rD(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==o[t]&&D(e)&&e.set(o[t],!1)}}mount(t){this.current=t,sx.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sv.current||function(){if(sv.current=!0,rj.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sg.current=t.matches;t.addListener(e),e()}else sg.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sg.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=x.has(t);n&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&m.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rF){let e=rF[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iD()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sw.length;e++){let i=sw[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(D(r))t.addValue(n,r);else if(D(s))t.addValue(n,V(r,{owner:t}));else if(s!==r)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,V(void 0!==e?e:r,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=V(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(sn(i)||ss(i))?i=parseFloat(i):!sy(i)&&tT.test(e)&&(i=sc(t,e)),this.setBaseTarget(t,D(i)?i.get():i)),D(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=a(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||D(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new T),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sb extends sA{constructor(){super(...arguments),this.KeyframeResolver=sm}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;D(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function sT(t,{style:e,vars:i},n,r){for(let s in Object.assign(t.style,e,r&&r.getProjectionStyles(n)),i)t.style.setProperty(s,i[s])}class sP extends sb{constructor(){super(...arguments),this.type="html",this.renderInstance=sT}readValueFromInstance(t,e){if(x.has(e))return this.projection?.isProjecting?eA(e):eT(t,e);{let i=window.getComputedStyle(t),n=(_(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i$(t,e)}build(t,e,i){rq(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return r6(t,e,i)}}let sM=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sS extends sb{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iD}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(x.has(e)){let t=sd(e);return t&&t.default||0}return e=sM.has(e)?e:j(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return r8(t,e,i)}build(t,e,i){rZ(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in sT(t,e,void 0,n),e.attrs)t.setAttribute(sM.has(i)?i:j(i),e.attrs[i])}mount(t){this.isSVGTag=rQ(t.tagName),super.mount(t)}}let sk=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((eX={animation:{Feature:id},exit:{Feature:ip},inView:{Feature:rS},tap:{Feature:rw},focus:{Feature:rd},hover:{Feature:rh},pan:{Feature:i3},drag:{Feature:i9,ProjectionNode:ra,MeasureLayout:nu},layout:{ProjectionNode:ra,MeasureLayout:nu}},eK=(t,e)=>r5(t)?new sS(e):new sP(e,{allowProjection:t!==nt.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:n,createVisualElement:r,useRender:s,useVisualState:a,Component:o}=t;function l(t,e){var i,n,l;let u,h={...(0,nt.useContext)(rE.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,nt.useContext)(ni.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:d}=h,c=function(t){let{initial:e,animate:i}=function(t,e){if(rC(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ie(e)?e:void 0,animate:ie(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,nt.useContext)(rV));return(0,nt.useMemo)(()=>({initial:e,animate:i}),[rR(e),rR(i)])}(t),p=a(t,d);if(!d&&rj.B){n=0,l=0,(0,nt.useContext)(rk).strict;let t=function(t){let{drag:e,layout:i}=rF;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(h);u=t.MeasureLayout,c.visualElement=function(t,e,i,n,r){let{visualElement:s}=(0,nt.useContext)(rV),a=(0,nt.useContext)(rk),o=(0,nt.useContext)(rB.t),l=(0,nt.useContext)(rE.Q).reducedMotion,u=(0,nt.useRef)(null);n=n||a.renderer,!u.current&&n&&(u.current=n(t,{visualState:e,parent:s,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let h=u.current,d=(0,nt.useContext)(nn);h&&!h.projection&&r&&("html"===h.type||"svg"===h.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!a||o&&iq(o),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,r,d);let c=(0,nt.useRef)(!1);(0,nt.useInsertionEffect)(()=>{h&&c.current&&h.update(i,o)});let p=i[L],m=(0,nt.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,rI.E)(()=>{h&&(c.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),i7.render(h.render),m.current&&h.animationState&&h.animationState.animateChanges())}),(0,nt.useEffect)(()=>{h&&(!m.current&&h.animationState&&h.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),h}(o,p,h,r,t.ProjectionNode)}return(0,i8.jsxs)(rV.Provider,{value:c,children:[u&&c.visualElement?(0,i8.jsx)(u,{visualElement:c.visualElement,...h}):null,s(o,t,(i=c.visualElement,(0,nt.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):iq(e)&&(e.current=t))},[i])),p,d,c.visualElement)]})}n&&function(t){for(let e in t)rF[e]={...rF[e],...t[e]}}(n),l.displayName="motion.".concat("string"==typeof o?o:"create(".concat(null!=(i=null!=(e=o.displayName)?e:o.name)?i:"",")"));let u=(0,nt.forwardRef)(l);return u[rO]=o,u}({...r5(t)?r7:r3,preloadedFeatures:eX,useRender:function(t=!1){return(e,i,n,{latestValues:r},s)=>{let a=(r5(e)?function(t,e,i,n){let r=(0,nt.useMemo)(()=>{let i=rG();return rZ(i,e,rQ(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};rY(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return rY(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,nt.useMemo)(()=>{let i=rH();return rq(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,r,s,e),o=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(r1(r)||!0===i&&r0(r)||!e&&!r0(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(i,"string"==typeof e,t),l=e!==nt.Fragment?{...o,...a,ref:n}:{},{children:u}=i,h=(0,nt.useMemo)(()=>D(u)?u.get():u,[u]);return(0,nt.createElement)(e,{...l,children:h})}}(e),createVisualElement:eK,Component:t})}))},6654:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return r}});let n=i(2115);function r(t,e){let i=(0,n.useRef)(null),r=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let t=i.current;t&&(i.current=null,t());let e=r.current;e&&(r.current=null,e())}else t&&(i.current=s(t,n)),e&&(r.current=s(e,n))},[t,e])}function s(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let i=t(e);return"function"==typeof i?i:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6785:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},6874:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{default:function(){return y},useLinkStatus:function(){return v}});let n=i(6966),r=i(5155),s=n._(i(2115)),a=i(2757),o=i(5227),l=i(9818),u=i(6654),h=i(9991),d=i(5929);i(3230);let c=i(4930),p=i(2664),m=i(6634);function f(t){return"string"==typeof t?t:(0,a.formatUrl)(t)}function y(t){let e,i,n,[a,y]=(0,s.useOptimistic)(c.IDLE_LINK_STATUS),v=(0,s.useRef)(null),{href:x,as:w,children:A,prefetch:b=null,passHref:T,replace:P,shallow:M,scroll:S,onClick:k,onMouseEnter:E,onTouchStart:V,legacyBehavior:C=!1,onNavigate:D,ref:R,unstable_dynamicOnHover:j,...L}=t;e=A,C&&("string"==typeof e||"number"==typeof e)&&(e=(0,r.jsx)("a",{children:e}));let F=s.default.useContext(o.AppRouterContext),O=!1!==b,B=null===b?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:I,as:U}=s.default.useMemo(()=>{let t=f(x);return{href:t,as:w?f(w):t}},[x,w]);C&&(i=s.default.Children.only(e));let N=C?i&&"object"==typeof i&&i.ref:R,z=s.default.useCallback(t=>(null!==F&&(v.current=(0,c.mountLinkInstance)(t,I,F,B,O,y)),()=>{v.current&&((0,c.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,c.unmountPrefetchableInstance)(t)}),[O,I,F,B,y]),W={ref:(0,u.useMergedRef)(z,N),onClick(t){C||"function"!=typeof k||k(t),C&&i.props&&"function"==typeof i.props.onClick&&i.props.onClick(t),F&&(t.defaultPrevented||function(t,e,i,n,r,a,o){let{nodeName:l}=t.currentTarget;if(!("A"===l.toUpperCase()&&function(t){let e=t.currentTarget.getAttribute("target");return e&&"_self"!==e||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&2===t.nativeEvent.which}(t)||t.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(e)){r&&(t.preventDefault(),location.replace(e));return}t.preventDefault(),s.default.startTransition(()=>{if(o){let t=!1;if(o({preventDefault:()=>{t=!0}}),t)return}(0,m.dispatchNavigateAction)(i||e,r?"replace":"push",null==a||a,n.current)})}}(t,I,U,v,P,S,D))},onMouseEnter(t){C||"function"!=typeof E||E(t),C&&i.props&&"function"==typeof i.props.onMouseEnter&&i.props.onMouseEnter(t),F&&O&&(0,c.onNavigationIntent)(t.currentTarget,!0===j)},onTouchStart:function(t){C||"function"!=typeof V||V(t),C&&i.props&&"function"==typeof i.props.onTouchStart&&i.props.onTouchStart(t),F&&O&&(0,c.onNavigationIntent)(t.currentTarget,!0===j)}};return(0,h.isAbsoluteUrl)(U)?W.href=U:C&&!T&&("a"!==i.type||"href"in i.props)||(W.href=(0,d.addBasePath)(U)),n=C?s.default.cloneElement(i,W):(0,r.jsx)("a",{...L,...W,children:e}),(0,r.jsx)(g.Provider,{value:a,children:n})}i(3180);let g=(0,s.createContext)(c.IDLE_LINK_STATUS),v=()=>(0,s.useContext)(g);("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6983:(t,e,i)=>{i.d(e,{G:()=>n});function n(t){return"object"==typeof t&&null!==t}},7351:(t,e,i)=>{i.d(e,{s:()=>r});var n=i(6983);function r(t){return(0,n.G)(t)&&"offsetHeight"in t}},7494:(t,e,i)=>{i.d(e,{E:()=>r});var n=i(2115);let r=i(8972).B?n.useLayoutEffect:n.useEffect},7580:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7951:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},8175:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},8564:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},8859:(t,e)=>{function i(t){let e={};for(let[i,n]of t.entries()){let t=e[i];void 0===t?e[i]=n:Array.isArray(t)?t.push(n):e[i]=[t,n]}return e}function n(t){return"string"==typeof t?t:("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function r(t){let e=new URLSearchParams;for(let[i,r]of Object.entries(t))if(Array.isArray(r))for(let t of r)e.append(i,n(t));else e.set(i,n(r));return e}function s(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];for(let e of i){for(let i of e.keys())t.delete(i);for(let[i,n]of e.entries())t.append(i,n)}return t}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{assign:function(){return s},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return r}})},8883:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},8972:(t,e,i)=>{i.d(e,{B:()=>n});let n="undefined"!=typeof window},9037:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},9074:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9376:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},9397:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},9420:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9881:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},9946:(t,e,i)=>{i.d(e,{A:()=>d});var n=i(2115);let r=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,i)=>i?i.toUpperCase():e.toLowerCase()),a=t=>{let e=s(t);return e.charAt(0).toUpperCase()+e.slice(1)},o=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim()},l=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,n.forwardRef)((t,e)=>{let{color:i="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:h="",children:d,iconNode:c,...p}=t;return(0,n.createElement)("svg",{ref:e,...u,width:r,height:r,stroke:i,strokeWidth:a?24*Number(s)/Number(r):s,className:o("lucide",h),...!d&&!l(p)&&{"aria-hidden":"true"},...p},[...c.map(t=>{let[e,i]=t;return(0,n.createElement)(e,i)}),...Array.isArray(d)?d:[d]])}),d=(t,e)=>{let i=(0,n.forwardRef)((i,s)=>{let{className:l,...u}=i;return(0,n.createElement)(h,{ref:s,iconNode:e,className:o("lucide-".concat(r(a(t))),"lucide-".concat(t),l),...u})});return i.displayName=a(t),i}},9964:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]])},9991:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return g},NormalizeError:function(){return f},PageNotFoundError:function(){return y},SP:function(){return c},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return o},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return h},stringifyError:function(){return x}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function n(t){let e,i=!1;return function(){for(var n=arguments.length,r=Array(n),s=0;s<n;s++)r[s]=arguments[s];return i||(i=!0,e=t(...r)),e}}let r=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=t=>r.test(t);function a(){let{protocol:t,hostname:e,port:i}=window.location;return t+"//"+e+(i?":"+i:"")}function o(){let{href:t}=window.location,e=a();return t.substring(e.length)}function l(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function u(t){return t.finished||t.headersSent}function h(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function d(t,e){let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await d(e.Component,e.ctx)}:{};let n=await t.getInitialProps(e);if(i&&u(i))return n;if(!n)throw Object.defineProperty(Error('"'+l(t)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let c="undefined"!=typeof performance,p=c&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class m extends Error{}class f extends Error{}class y extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class g extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(t){return JSON.stringify({message:t.message,stack:t.stack})}}}]);