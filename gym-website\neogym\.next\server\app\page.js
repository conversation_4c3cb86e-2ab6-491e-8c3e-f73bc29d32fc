(()=>{var e={};e.id=974,e.ids=[974],e.modules={195:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return o},urlObjectKeys:function(){return s}});let r=n(740)._(n(6715)),i=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:n}=e,a=e.protocol||"",s=e.pathname||"",o=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:n&&(u=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||i.test(a))&&!1!==u?(u="//"+(u||""),s&&"/"!==s[0]&&(s="/"+s)):u||(u=""),o&&"#"!==o[0]&&(o="#"+o),c&&"?"!==c[0]&&(c="?"+c),""+a+u+(s=s.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+o}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function o(e){return a(e)}},427:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>s.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>h,tree:()=>u});var r=n(5239),i=n(8088),a=n(8170),s=n.n(a),o=n(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);n.d(t,l);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,1204)),"C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,4431)),"C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\app\\page.tsx"],d={require:n,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},440:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>i});var r=n(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},554:(e,t)=>{"use strict";function n(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return n}})},593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{NavigationResultTag:function(){return d},PrefetchPriority:function(){return h},cancelPrefetchTask:function(){return l},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return s},navigate:function(){return i},prefetch:function(){return r},reschedulePrefetchTask:function(){return u},revalidateEntireCache:function(){return a},schedulePrefetchTask:function(){return o}});let n=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},r=n,i=n,a=n,s=n,o=n,l=n,u=n,c=n;var d=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),h=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},610:(e,t,n)=>{Promise.resolve().then(n.bind(n,4143)),Promise.resolve().then(n.bind(n,1136)),Promise.resolve().then(n.bind(n,5303)),Promise.resolve().then(n.bind(n,4759)),Promise.resolve().then(n.bind(n,8595)),Promise.resolve().then(n.bind(n,5755)),Promise.resolve().then(n.bind(n,743)),Promise.resolve().then(n.bind(n,9285))},642:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return u},getSelectedParams:function(){return function e(t,n){for(let r of(void 0===n&&(n={}),Object.values(t[1]))){let t=r[0],a=Array.isArray(t),s=a?t[1]:t;!s||s.startsWith(i.PAGE_SEGMENT_KEY)||(a&&("c"===t[2]||"oc"===t[2])?n[t[0]]=t[1].split("/"):a&&(n[t[0]]=t[1]),n=e(r,n))}return n}}});let r=n(2859),i=n(3913),a=n(4077),s=e=>"/"===e[0]?e.slice(1):e,o=e=>"string"==typeof e?"children"===e?"":e:e[1];function l(e){return e.reduce((e,t)=>""===(t=s(t))||(0,i.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let n=Array.isArray(e[0])?e[0][1]:e[0];if(n===i.DEFAULT_SEGMENT_KEY||r.INTERCEPTION_ROUTE_MARKERS.some(e=>n.startsWith(e)))return;if(n.startsWith(i.PAGE_SEGMENT_KEY))return"";let a=[o(n)],s=null!=(t=e[1])?t:{},c=s.children?u(s.children):void 0;if(void 0!==c)a.push(c);else for(let[e,t]of Object.entries(s)){if("children"===e)continue;let n=u(t);void 0!==n&&a.push(n)}return l(a)}function c(e,t){let n=function e(t,n){let[i,s]=t,[l,c]=n,d=o(i),h=o(l);if(r.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||h.startsWith(e)))return"";if(!(0,a.matchSegment)(i,l)){var p;return null!=(p=u(n))?p:""}for(let t in s)if(c[t]){let n=e(s[t],c[t]);if(null!==n)return o(l)+"/"+n}return null}(e,t);return null==n||"/"===n?n:l(n.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},660:(e,t)=>{"use strict";function n(e){let t=5381;for(let n=0;n<e.length;n++)t=(t<<5)+t+e.charCodeAt(n)|0;return t>>>0}function r(e){return n(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{djb2Hash:function(){return n},hexHash:function(){return r}})},743:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Store\\\\gym-website\\\\neogym\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Testimonials.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1136:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Store\\\\gym-website\\\\neogym\\\\src\\\\components\\\\sections\\\\About.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\About.tsx","default")},1204:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>h});var r=n(7413),i=n(4143),a=n(8595),s=n(1136),o=n(5303),l=n(9285),u=n(5755),c=n(743),d=n(4759);function h(){return(0,r.jsxs)(i.default,{children:[(0,r.jsx)(a.default,{}),(0,r.jsx)(s.default,{}),(0,r.jsx)(o.default,{}),(0,r.jsx)(l.default,{}),(0,r.jsx)(u.default,{}),(0,r.jsx)(c.default,{}),(0,r.jsx)(d.default,{})]})}},1279:(e,t,n)=>{"use strict";n.d(t,{t:()=>r});let r=(0,n(3210).createContext)(null)},1312:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(2688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},1437:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return s},isInterceptionRouteAppPath:function(){return a}});let r=n(4722),i=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function s(e){let t,n,a;for(let r of e.split("/"))if(n=i.find(e=>r.startsWith(e))){[t,a]=e.split(n,2);break}if(!t||!n||!a)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,r.normalizeAppPath)(t),n){case"(.)":a="/"===t?"/"+a:t+"/"+a;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let s=t.split("/");if(s.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});a=s.slice(0,-2).concat(a).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:a}}},1500:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,n,a,s,o,l,u){if(0===Object.keys(s[1]).length){n.head=l;return}for(let c in s[1]){let d,h=s[1][c],p=h[0],f=(0,r.createRouterCacheKey)(p),m=null!==o&&void 0!==o[2][c]?o[2][c]:null;if(a){let r=a.parallelRoutes.get(c);if(r){let a,s=(null==u?void 0:u.kind)==="auto"&&u.status===i.PrefetchCacheEntryStatus.reusable,o=new Map(r),d=o.get(f);a=null!==m?{lazyData:null,rsc:m[1],prefetchRsc:null,head:null,prefetchHead:null,loading:m[3],parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),navigatedAt:t}:s&&d?{lazyData:d.lazyData,rsc:d.rsc,prefetchRsc:d.prefetchRsc,head:d.head,prefetchHead:d.prefetchHead,parallelRoutes:new Map(d.parallelRoutes),loading:d.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),loading:null,navigatedAt:t},o.set(f,a),e(t,a,d,h,m||null,l,u),n.parallelRoutes.set(c,o);continue}}if(null!==m){let e=m[1],n=m[3];d={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else d={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let g=n.parallelRoutes.get(c);g?g.set(f,d):n.parallelRoutes.set(c,new Map([[f,d]])),e(t,d,void 0,h,m,l,u)}}}});let r=n(3123),i=n(9154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1550:(e,t)=>{"use strict";function n(e){let t=e.indexOf("#"),n=e.indexOf("?"),r=n>-1&&(t<0||n<t);return r||t>-1?{pathname:e.substring(0,r?n:t),query:r?e.substring(n,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return n}})},1658:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillMetadataSegment:function(){return h},normalizeMetadataPageToRoute:function(){return f},normalizeMetadataRoute:function(){return p}});let r=n(8304),i=function(e){return e&&e.__esModule?e:{default:e}}(n(8671)),a=n(6341),s=n(4396),o=n(660),l=n(4722),u=n(2958),c=n(5499);function d(e){let t=i.default.dirname(e);if(e.endsWith("/sitemap"))return"";let n="";return t.split("/").some(e=>(0,c.isGroupSegment)(e)||(0,c.isParallelRouteSegment)(e))&&(n=(0,o.djb2Hash)(t).toString(36).slice(0,6)),n}function h(e,t,n){let r=(0,l.normalizeAppPath)(e),o=(0,s.getNamedRouteRegex)(r,{prefixRouteKeys:!1}),c=(0,a.interpolateDynamicPath)(r,t,o),{name:h,ext:p}=i.default.parse(n),f=d(i.default.posix.join(e,h)),m=f?`-${f}`:"";return(0,u.normalizePathSep)(i.default.join(c,`${h}${m}${p}`))}function p(e){if(!(0,r.isMetadataPage)(e))return e;let t=e,n="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":n=d(e),!t.endsWith("/route")){let{dir:e,name:r,ext:a}=i.default.parse(t);t=i.default.posix.join(e,`${r}${n?`-${n}`:""}${a}`,"route")}return t}function f(e,t){let n=e.endsWith("/route"),r=n?e.slice(0,-6):e,i=r.endsWith("/sitemap")?".xml":"";return(t?`${r}/[__metadata_id__]`:`${r}${i}`)+(n?"/route":"")}},1794:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let r=n(9289),i=n(6736);function a(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,i.hasBasePath)(n.pathname)}catch(e){return!1}}},2030:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,n){let r=t[0],i=n[0];if(Array.isArray(r)&&Array.isArray(i)){if(r[0]!==i[0]||r[2]!==i[2])return!0}else if(r!==i)return!0;if(t[4])return!n[4];if(n[4])return!0;let a=Object.values(t[1])[0],s=Object.values(n[1])[0];return!a||!s||e(a,s)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2157:(e,t,n)=>{"use strict";n.d(t,{L:()=>r});let r=(0,n(3210).createContext)({})},2255:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let r=n(1550);function i(e,t){if("string"!=typeof e)return!1;let{pathname:n}=(0,r.parsePath)(e);return n===t||n.startsWith(t+"/")}},2308:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,n){let[r,i,,s]=t;for(let o in r.includes(a.PAGE_SEGMENT_KEY)&&"refresh"!==s&&(t[2]=n,t[3]="refresh"),i)e(i[o],n)}},refreshInactiveParallelSegments:function(){return s}});let r=n(6928),i=n(9008),a=n(3913);async function s(e){let t=new Set;await o({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function o(e){let{navigatedAt:t,state:n,updatedTree:a,updatedCache:s,includeNextUrl:l,fetchedSegments:u,rootTree:c=a,canonicalUrl:d}=e,[,h,p,f]=a,m=[];if(p&&p!==d&&"refresh"===f&&!u.has(p)){u.add(p);let e=(0,i.fetchServerResponse)(new URL(p,location.origin),{flightRouterState:[c[0],c[1],c[2],"refetch"],nextUrl:l?n.nextUrl:null}).then(e=>{let{flightData:n}=e;if("string"!=typeof n)for(let e of n)(0,r.applyFlightData)(t,s,s,e)});m.push(e)}for(let e in h){let r=o({navigatedAt:t,state:n,updatedTree:h[e],updatedCache:s,includeNextUrl:l,fetchedSegments:u,rootTree:c,canonicalUrl:d});m.push(r)}await Promise.all(m)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2437:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let r=n(5362);function i(e,t){let n=[],i=(0,r.pathToRegexp)(e,n,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),a=(0,r.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,n);return(e,r)=>{if("string"!=typeof e)return!1;let i=a(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of n)"number"==typeof e.name&&delete i.params[e.name];return{...r,...i.params}}}},2496:(e,t,n)=>{"use strict";n.d(t,{default:()=>E});var r=n(687),i=n(3210),a=n(5814),s=n.n(a),o=n(5583),l=n(8559),u=n(1312),c=n(2688);let d=(0,c.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),h=(0,c.A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);var p=n(8340);let f=(0,c.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),m=(0,c.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),g=()=>{let[e,t]=(0,i.useState)(!1),[n,a]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=()=>{a(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let c=[{name:"Home",href:"/",icon:o.A},{name:"About",href:"/about",icon:l.A},{name:"Classes",href:"/classes",icon:u.A},{name:"Trainers",href:"/trainers",icon:d},{name:"Membership",href:"/membership",icon:h},{name:"Contact",href:"/contact",icon:p.A}];return(0,r.jsx)("header",{className:`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${n?"glass backdrop-blur-xl":"bg-transparent"}`,children:(0,r.jsxs)("nav",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsxs)(s(),{href:"/",className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,r.jsx)("span",{className:"font-space text-xl font-bold text-white",children:"NeoGym"})]}),(0,r.jsx)("div",{className:"hidden md:flex items-center space-x-8",children:c.map(e=>(0,r.jsx)(s(),{href:e.href,className:"text-gray-300 hover:text-white transition-colors duration-200",children:e.name},e.name))}),(0,r.jsx)("div",{className:"hidden md:block",children:(0,r.jsx)("button",{className:"btn-primary",children:"Get Started"})}),(0,r.jsx)("button",{onClick:()=>t(!e),className:"md:hidden p-2 rounded-lg glass hover:bg-white/10 transition-colors duration-200",children:e?(0,r.jsx)(f,{className:"w-6 h-6 text-white"}):(0,r.jsx)(m,{className:"w-6 h-6 text-white"})})]}),e&&(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsxs)("div",{className:"glass rounded-lg mt-4 p-4 space-y-4",children:[c.map(e=>(0,r.jsx)(s(),{href:e.href,onClick:()=>t(!1),className:"block p-3 rounded-lg hover:bg-white/10 transition-colors duration-200 text-gray-300 hover:text-white",children:e.name},e.name)),(0,r.jsx)("div",{className:"pt-4 border-t border-white/10",children:(0,r.jsx)("button",{className:"btn-primary w-full",children:"Get Started"})})]})})]})})},y=(0,c.A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),v=(0,c.A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),x=(0,c.A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),b=(0,c.A)("youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]]);var w=n(7992),j=n(3931);let P=(0,c.A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),T=()=>{let e={company:[{name:"About NeoGym",href:"/about"},{name:"Our Mission",href:"/mission"},{name:"Careers",href:"/careers"},{name:"Press",href:"/press"}],services:[{name:"VR HIIT",href:"/classes/vr-hiit"},{name:"Gravity Yoga",href:"/classes/gravity-yoga"},{name:"AI Coaching",href:"/classes/ai-coaching"},{name:"Biometric Tracking",href:"/services/biometric"}],support:[{name:"Help Center",href:"/help"},{name:"Contact Us",href:"/contact"},{name:"Privacy Policy",href:"/privacy"},{name:"Terms of Service",href:"/terms"}]};return(0,r.jsx)("footer",{className:"relative bg-black/50 backdrop-blur-xl border-t border-white/10",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2",children:[(0,r.jsxs)(s(),{href:"/",className:"flex items-center space-x-3 mb-6",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center glow-blue",children:(0,r.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("span",{className:"font-orbitron text-2xl font-bold text-gradient-neon",children:["NEO",(0,r.jsx)("span",{className:"text-neon-green",children:"GYM"})]})]}),(0,r.jsx)("p",{className:"text-gray-400 mb-6 max-w-md font-exo",children:"The future of fitness starts now. Experience cutting-edge technology, AI-powered training, and immersive workouts that transform your body and mind."}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(w.A,{className:"w-5 h-5 text-neon-blue"}),(0,r.jsx)("span",{className:"text-gray-400",children:"2077 Future Street, Neo City, NC 12345"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(p.A,{className:"w-5 h-5 text-neon-blue"}),(0,r.jsx)("span",{className:"text-gray-400",children:"+1 (555) NEO-GYMS"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(j.A,{className:"w-5 h-5 text-neon-blue"}),(0,r.jsx)("span",{className:"text-gray-400",children:"<EMAIL>"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-orbitron text-lg font-semibold text-white mb-6",children:"Company"}),(0,r.jsx)("ul",{className:"space-y-3",children:e.company.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(s(),{href:e.href,className:"text-gray-400 hover:text-neon-blue transition-colors duration-300 font-exo",children:e.name})},e.name))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-orbitron text-lg font-semibold text-white mb-6",children:"Services"}),(0,r.jsx)("ul",{className:"space-y-3",children:e.services.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(s(),{href:e.href,className:"text-gray-400 hover:text-neon-blue transition-colors duration-300 font-exo",children:e.name})},e.name))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-orbitron text-lg font-semibold text-white mb-6",children:"Support"}),(0,r.jsx)("ul",{className:"space-y-3",children:e.support.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(s(),{href:e.href,className:"text-gray-400 hover:text-neon-blue transition-colors duration-300 font-exo",children:e.name})},e.name))})]})]}),(0,r.jsx)("div",{className:"border-t border-white/10 mt-12 pt-8",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-6 mb-6 md:mb-0",children:[(0,r.jsx)("span",{className:"font-orbitron text-white font-medium",children:"Follow the Future:"}),[{name:"Facebook",icon:y,href:"#"},{name:"Twitter",icon:v,href:"#"},{name:"Instagram",icon:x,href:"#"},{name:"YouTube",icon:b,href:"#"}].map(e=>{let t=e.icon;return(0,r.jsx)(s(),{href:e.href,className:"p-2 rounded-lg glass hover:glow-blue transition-all duration-300 group",children:(0,r.jsx)(t,{className:"w-5 h-5 text-gray-400 group-hover:text-neon-blue transition-colors duration-300"})},e.name)})]}),(0,r.jsxs)("button",{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"flex items-center space-x-2 px-4 py-2 glass rounded-lg hover:glow-blue transition-all duration-300 group",children:[(0,r.jsx)(P,{className:"w-4 h-4 text-neon-blue group-hover:animate-bounce"}),(0,r.jsx)("span",{className:"font-rajdhani text-white",children:"Back to Top"})]})]})}),(0,r.jsx)("div",{className:"border-t border-white/10 mt-8 pt-8 text-center",children:(0,r.jsx)("p",{className:"text-gray-400 font-exo",children:"\xa9 2024 NeoGym. All rights reserved. The future of fitness is here."})})]})})},E=({children:e})=>(0,r.jsxs)("div",{className:"min-h-screen bg-black",children:[(0,r.jsx)(g,{}),(0,r.jsx)("main",{children:e}),(0,r.jsx)(T,{})]})},2582:(e,t,n)=>{"use strict";n.d(t,{Q:()=>r});let r=(0,n(3210).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},2688:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});var r=n(3210);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),s=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:a="",children:s,iconNode:c,...d},h)=>(0,r.createElement)("svg",{ref:h,...u,width:t,height:t,stroke:e,strokeWidth:i?24*Number(n)/Number(t):n,className:o("lucide",a),...!s&&!l(d)&&{"aria-hidden":"true"},...d},[...c.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(s)?s:[s]])),d=(e,t)=>{let n=(0,r.forwardRef)(({className:n,...a},l)=>(0,r.createElement)(c,{ref:l,iconNode:t,className:o(`lucide-${i(s(e))}`,`lucide-${e}`,n),...a}));return n.displayName=s(e),n}},2708:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},2743:(e,t,n)=>{"use strict";n.d(t,{E:()=>i});var r=n(3210);let i=n(7044).B?r.useLayoutEffect:r.useEffect},2785:(e,t)=>{"use strict";function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[n,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(n,r(e));else t.set(n,r(i));return t}function a(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},2789:(e,t,n)=>{"use strict";n.d(t,{M:()=>i});var r=n(3210);function i(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},2958:(e,t)=>{"use strict";function n(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return n}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3038:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let r=n(3210);function i(e,t){let n=(0,r.useRef)(null),i=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(n.current=a(e,r)),t&&(i.current=a(t,r))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let n=/[|\\{}()[\]^$+*?.-]/,r=/[|\\{}()[\]^$+*?.-]/g;function i(e){return n.test(e)?e.replace(r,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3406:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{IDLE_LINK_STATUS:function(){return u},PENDING_LINK_STATUS:function(){return l},mountFormInstance:function(){return v},mountLinkInstance:function(){return y},onLinkVisibilityChanged:function(){return b},onNavigationIntent:function(){return w},pingVisibleLinks:function(){return P},setLinkForCurrentNavigation:function(){return c},unmountLinkForCurrentNavigation:function(){return d},unmountPrefetchableInstance:function(){return x}}),n(3690);let r=n(9752),i=n(9154),a=n(593),s=n(3210),o=null,l={pending:!0},u={pending:!1};function c(e){(0,s.startTransition)(()=>{null==o||o.setOptimisticLinkStatus(u),null==e||e.setOptimisticLinkStatus(l),o=e})}function d(e){o===e&&(o=null)}let h="function"==typeof WeakMap?new WeakMap:new Map,p=new Set,f="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;b(t.target,e)}},{rootMargin:"200px"}):null;function m(e,t){void 0!==h.get(e)&&x(e),h.set(e,t),null!==f&&f.observe(e)}function g(e){try{return(0,r.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function y(e,t,n,r,i,a){if(i){let i=g(t);if(null!==i){let t={router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:a};return m(e,t),t}}return{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:a}}function v(e,t,n,r){let i=g(t);null!==i&&m(e,{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:null})}function x(e){let t=h.get(e);if(void 0!==t){h.delete(e),p.delete(t);let n=t.prefetchTask;null!==n&&(0,a.cancelPrefetchTask)(n)}null!==f&&f.unobserve(e)}function b(e,t){let n=h.get(e);void 0!==n&&(n.isVisible=t,t?p.add(n):p.delete(n),j(n))}function w(e,t){let n=h.get(e);void 0!==n&&void 0!==n&&(n.wasHoveredOrTouched=!0,j(n))}function j(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,a.cancelPrefetchTask)(t);return}}function P(e,t){let n=(0,a.getCurrentCacheVersion)();for(let r of p){let s=r.prefetchTask;if(null!==s&&r.cacheVersion===n&&s.key.nextUrl===e&&s.treeAtTimeOfPrefetch===t)continue;null!==s&&(0,a.cancelPrefetchTask)(s);let o=(0,a.createCacheKey)(r.prefetchHref,e),l=r.wasHoveredOrTouched?a.PrefetchPriority.Intent:a.PrefetchPriority.Default;r.prefetchTask=(0,a.schedulePrefetchTask)(o,t,r.kind===i.PrefetchKind.FULL,l),r.cacheVersion=(0,a.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3690:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createMutableActionQueue:function(){return f},dispatchNavigateAction:function(){return y},dispatchTraverseAction:function(){return v},getCurrentAppRouterState:function(){return m},publicAppRouterInstance:function(){return x}});let r=n(9154),i=n(8830),a=n(3210),s=n(1992);n(593);let o=n(9129),l=n(6127),u=n(9752),c=n(5076),d=n(3406);function h(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?p({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:r.ACTION_REFRESH,origin:window.location.origin},t)))}async function p(e){let{actionQueue:t,action:n,setState:r}=e,i=t.state;t.pending=n;let a=n.payload,o=t.action(i,a);function l(e){n.discarded||(t.state=e,h(t,r),n.resolve(e))}(0,s.isThenable)(o)?o.then(l,e=>{h(t,r),n.reject(e)}):l(o)}function f(e,t){let n={state:e,dispatch:(e,t)=>(function(e,t,n){let i={resolve:n,reject:()=>{}};if(t.type!==r.ACTION_RESTORE){let e=new Promise((e,t)=>{i={resolve:e,reject:t}});(0,a.startTransition)(()=>{n(e)})}let s={payload:t,next:null,resolve:i.resolve,reject:i.reject};null===e.pending?(e.last=s,p({actionQueue:e,action:s,setState:n})):t.type===r.ACTION_NAVIGATE||t.type===r.ACTION_RESTORE?(e.pending.discarded=!0,s.next=e.pending.next,e.pending.payload.type===r.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),p({actionQueue:e,action:s,setState:n})):(null!==e.last&&(e.last.next=s),e.last=s)})(n,e,t),action:async(e,t)=>(0,i.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return n}function m(){return null}function g(){return null}function y(e,t,n,i){let a=new URL((0,l.addBasePath)(e),location.href);(0,d.setLinkForCurrentNavigation)(i);(0,o.dispatchAppRouterAction)({type:r.ACTION_NAVIGATE,url:a,isExternalUrl:(0,u.isExternalURL)(a),locationSearch:location.search,shouldScroll:n,navigateType:t,allowAliasing:!0})}function v(e,t){(0,o.dispatchAppRouterAction)({type:r.ACTION_RESTORE,url:new URL(e),tree:t})}let x={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let n=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),i=(0,u.createPrefetchURL)(e);if(null!==i){var a;(0,c.prefetchReducer)(n.state,{type:r.ACTION_PREFETCH,url:i,kind:null!=(a=null==t?void 0:t.kind)?a:r.PrefetchKind.FULL})}},replace:(e,t)=>{(0,a.startTransition)(()=>{var n;y(e,"replace",null==(n=null==t?void 0:t.scroll)||n,null)})},push:(e,t)=>{(0,a.startTransition)(()=>{var n;y(e,"push",null==(n=null==t?void 0:t.scroll)||n,null)})},refresh:()=>{(0,a.startTransition)(()=>{(0,o.dispatchAppRouterAction)({type:r.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3736:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),n(4827);let r=n(2785);function i(e,t,n){void 0===n&&(n=!0);let i=new URL("http://n"),a=t?new URL(t,i):e.startsWith(".")?new URL("http://n"):i,{pathname:s,searchParams:o,search:l,hash:u,href:c,origin:d}=new URL(e,a);if(d!==i.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:s,query:n?(0,r.searchParamsToUrlQuery)(o):void 0,search:l,hash:u,href:c.slice(d.length)}}},3818:(e,t,n)=>{Promise.resolve().then(n.bind(n,2496)),Promise.resolve().then(n.bind(n,7967)),Promise.resolve().then(n.bind(n,4734)),Promise.resolve().then(n.bind(n,4975)),Promise.resolve().then(n.bind(n,5344)),Promise.resolve().then(n.bind(n,6156)),Promise.resolve().then(n.bind(n,5300)),Promise.resolve().then(n.bind(n,6145))},3873:e=>{"use strict";e.exports=require("path")},3898:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillCacheWithNewSubTreeData:function(){return l},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return u}});let r=n(4400),i=n(1500),a=n(3123),s=n(3913);function o(e,t,n,o,l,u){let{segmentPath:c,seedData:d,tree:h,head:p}=o,f=t,m=n;for(let t=0;t<c.length;t+=2){let n=c[t],o=c[t+1],g=t===c.length-2,y=(0,a.createRouterCacheKey)(o),v=m.parallelRoutes.get(n);if(!v)continue;let x=f.parallelRoutes.get(n);x&&x!==v||(x=new Map(v),f.parallelRoutes.set(n,x));let b=v.get(y),w=x.get(y);if(g){if(d&&(!w||!w.lazyData||w===b)){let t=d[0],n=d[1],a=d[3];w={lazyData:null,rsc:u||t!==s.PAGE_SEGMENT_KEY?n:null,prefetchRsc:null,head:null,prefetchHead:null,loading:a,parallelRoutes:u&&b?new Map(b.parallelRoutes):new Map,navigatedAt:e},b&&u&&(0,r.invalidateCacheByRouterState)(w,b,h),u&&(0,i.fillLazyItemsTillLeafWithHead)(e,w,b,h,d,p,l),x.set(y,w)}continue}w&&b&&(w===b&&(w={lazyData:w.lazyData,rsc:w.rsc,prefetchRsc:w.prefetchRsc,head:w.head,prefetchHead:w.prefetchHead,parallelRoutes:new Map(w.parallelRoutes),loading:w.loading},x.set(y,w)),f=w,m=b)}}function l(e,t,n,r,i){o(e,t,n,r,i,!0)}function u(e,t,n,r,i){o(e,t,n,r,i,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3931:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(2688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},4143:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Store\\\\gym-website\\\\neogym\\\\src\\\\components\\\\layout\\\\Layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\layout\\Layout.tsx","default")},4330:()=>{},4396:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return d},parseParameter:function(){return l}});let r=n(6143),i=n(1437),a=n(3293),s=n(2887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(o);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let n=e.startsWith("...");return n&&(e=e.slice(3)),{key:e,repeat:n,optional:t}}function c(e,t,n){let r={},l=1,c=[];for(let d of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),s=d.match(o);if(e&&s&&s[2]){let{key:t,optional:n,repeat:i}=u(s[2]);r[t]={pos:l++,repeat:i,optional:n},c.push("/"+(0,a.escapeStringRegexp)(e)+"([^/]+?)")}else if(s&&s[2]){let{key:e,repeat:t,optional:i}=u(s[2]);r[e]={pos:l++,repeat:t,optional:i},n&&s[1]&&c.push("/"+(0,a.escapeStringRegexp)(s[1]));let o=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";n&&s[1]&&(o=o.substring(1)),c.push(o)}else c.push("/"+(0,a.escapeStringRegexp)(d));t&&s&&s[3]&&c.push((0,a.escapeStringRegexp)(s[3]))}return{parameterizedRoute:c.join(""),groups:r}}function d(e,t){let{includeSuffix:n=!1,includePrefix:r=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:a,groups:s}=c(e,n,r),o=a;return i||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:s}}function h(e){let t,{interceptionMarker:n,getSafeRouteKey:r,segment:i,routeKeys:s,keyPrefix:o,backreferenceDuplicateKeys:l}=e,{key:c,optional:d,repeat:h}=u(i),p=c.replace(/\W/g,"");o&&(p=""+o+p);let f=!1;(0===p.length||p.length>30)&&(f=!0),isNaN(parseInt(p.slice(0,1)))||(f=!0),f&&(p=r());let m=p in s;o?s[p]=""+o+c:s[p]=c;let g=n?(0,a.escapeStringRegexp)(n):"";return t=m&&l?"\\k<"+p+">":h?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",d?"(?:/"+g+t+")?":"/"+g+t}function p(e,t,n,l,u){let c,d=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},f=[];for(let c of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),s=c.match(o);if(e&&s&&s[2])f.push(h({getSafeRouteKey:d,interceptionMarker:s[1],segment:s[2],routeKeys:p,keyPrefix:t?r.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(s&&s[2]){l&&s[1]&&f.push("/"+(0,a.escapeStringRegexp)(s[1]));let e=h({getSafeRouteKey:d,segment:s[2],routeKeys:p,keyPrefix:t?r.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&s[1]&&(e=e.substring(1)),f.push(e)}else f.push("/"+(0,a.escapeStringRegexp)(c));n&&s&&s[3]&&f.push((0,a.escapeStringRegexp)(s[3]))}return{namedParameterizedRoute:f.join(""),routeKeys:p}}function f(e,t){var n,r,i;let a=p(e,t.prefixRouteKeys,null!=(n=t.includeSuffix)&&n,null!=(r=t.includePrefix)&&r,null!=(i=t.backreferenceDuplicateKeys)&&i),s=a.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(s+="(?:/)?"),{...d(e,t),namedRegex:"^"+s+"$",routeKeys:a.routeKeys}}function m(e,t){let{parameterizedRoute:n}=c(e,!1,!1),{catchAll:r=!0}=t;if("/"===n)return{namedRegex:"^/"+(r?".*":"")+"$"};let{namedParameterizedRoute:i}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(r?"(?:(/.*)?)":"")+"$"}}},4397:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return i}});let r=n(3123);function i(e,t){return function e(t,n,i){if(0===Object.keys(n).length)return[t,i];let a=Object.keys(n).filter(e=>"children"!==e);for(let s of("children"in n&&a.unshift("children"),a)){let[a,o]=n[s],l=t.parallelRoutes.get(s);if(!l)continue;let u=(0,r.createRouterCacheKey)(a),c=l.get(u);if(!c)continue;let d=e(c,o,i+"/"+u);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4398:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(2688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},4400:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return i}});let r=n(3123);function i(e,t,n){for(let i in n[1]){let a=n[1][i][0],s=(0,r.createRouterCacheKey)(a),o=t.parallelRoutes.get(i);if(o){let t=new Map(o);t.delete(s),e.parallelRoutes.set(i,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4431:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>u,metadata:()=>l});var r=n(7413),i=n(2376),a=n.n(i),s=n(8726),o=n.n(s);n(1135);let l={title:"Create Next App",description:"Generated by create next app"};function u({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${a().variable} ${o().variable} antialiased`,children:e})})}},4479:(e,t,n)=>{"use strict";function r(e){return"object"==typeof e&&null!==e}n.d(t,{G:()=>r})},4642:(e,t)=>{"use strict";function n(e){let t=parseInt(e.slice(0,2),16),n=t>>1&63,r=Array(6);for(let e=0;e<6;e++){let t=n>>5-e&1;r[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:r,hasRestArgs:1==(1&t)}}function r(e,t){let n=Array(e.length);for(let r=0;r<e.length;r++)(r<6&&t.usedArgs[r]||r>=6&&t.hasRestArgs)&&(n[r]=e[r]);return n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{extractInfoFromServerReferenceId:function(){return n},omitUnusedArgs:function(){return r}})},4669:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,6444,23)),Promise.resolve().then(n.t.bind(n,6042,23)),Promise.resolve().then(n.t.bind(n,8170,23)),Promise.resolve().then(n.t.bind(n,9477,23)),Promise.resolve().then(n.t.bind(n,9345,23)),Promise.resolve().then(n.t.bind(n,2089,23)),Promise.resolve().then(n.t.bind(n,6577,23)),Promise.resolve().then(n.t.bind(n,1307,23))},4674:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return a}});let r=n(4949),i=n(1550),a=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:n,hash:a}=(0,i.parsePath)(e);return""+(0,r.removeTrailingSlash)(t)+n+a};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4722:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{normalizeAppPath:function(){return a},normalizeRscURL:function(){return s}});let r=n(5531),i=n(5499);function a(e){return(0,r.ensureLeadingSlash)(e.split("/").reduce((e,t,n,r)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&n===r.length-1?e:e+"/"+t,""))}function s(e){return e.replace(/\.rsc($|\?)/,"$1")}},4734:(e,t,n)=>{"use strict";n.d(t,{default:()=>h});var r=n(687),i=n(3210),a=n(6001);let s=(0,n(2688).A)("headset",[["path",{d:"M3 11h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-5Zm0 0a9 9 0 1 1 18 0m0 0v5a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3Z",key:"12oyoe"}],["path",{d:"M21 16v2a4 4 0 0 1-4 4h-5",key:"1x7m43"}]]);var o=n(8947),l=n(8200),u=n(5583),c=n(1312),d=n(8730);let h=()=>{let[e,t]=(0,i.useState)("all"),n=[{id:1,title:"VR HIIT Infinity",category:"vr",description:"High-intensity interval training in immersive virtual environments. Battle through alien landscapes while burning calories.",duration:"45 min",intensity:"High",participants:"8-12",icon:s,image:"/api/placeholder/400/300",features:["Virtual Reality","Heart Rate Monitoring","Calorie Tracking"],color:"blue"},{id:2,title:"Gravity Yoga Flow",category:"biometric",description:"Anti-gravity yoga sessions with real-time posture analysis and breathing optimization through advanced sensors.",duration:"60 min",intensity:"Medium",participants:"6-10",icon:o.A,image:"/api/placeholder/400/300",features:["Anti-Gravity","Posture Analysis","Breathing Optimization"],color:"purple"},{id:3,title:"AI Strength Mastery",category:"ai",description:"Personalized strength training with AI form correction and adaptive resistance based on your performance.",duration:"50 min",intensity:"High",participants:"1-4",icon:l.A,image:"/api/placeholder/400/300",features:["AI Form Correction","Adaptive Resistance","Progress Tracking"],color:"green"},{id:4,title:"Neural Sync Cardio",category:"biometric",description:"Cardio workouts synchronized with your brainwaves for optimal performance and mental clarity.",duration:"40 min",intensity:"Medium",participants:"5-8",icon:u.A,image:"/api/placeholder/400/300",features:["Brainwave Sync","Mental Clarity","Cardio Optimization"],color:"cyan"},{id:5,title:"Hologram Boxing",category:"vr",description:"Box against holographic opponents with real-time technique analysis and impact measurement.",duration:"35 min",intensity:"High",participants:"4-6",icon:o.A,image:"/api/placeholder/400/300",features:["Holographic Opponents","Technique Analysis","Impact Measurement"],color:"blue"},{id:6,title:"Collective Mind Fitness",category:"group",description:"Group workouts where participants' biometrics are synchronized for enhanced team performance.",duration:"55 min",intensity:"Medium",participants:"12-20",icon:c.A,image:"/api/placeholder/400/300",features:["Team Sync","Group Biometrics","Collective Goals"],color:"purple"}],h="all"===e?n:n.filter(t=>t.category===e);return(0,r.jsxs)("section",{className:"py-24 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-purple-900/10 via-transparent to-blue-900/10"}),(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,r.jsxs)("div",{className:"inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8",children:[(0,r.jsx)(s,{className:"w-5 h-5 text-purple-400"}),(0,r.jsx)("span",{className:"font-orbitron text-sm font-medium text-white",children:"FUTURISTIC CLASSES"})]}),(0,r.jsxs)("h2",{className:"font-orbitron text-4xl md:text-6xl font-bold mb-8",children:[(0,r.jsx)("span",{className:"text-white",children:"NEXT-GEN "}),(0,r.jsx)("span",{className:"bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent",children:"TRAINING"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent",children:"PROGRAMS"})]}),(0,r.jsx)("p",{className:"font-exo text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed",children:"Experience revolutionary fitness classes that blend cutting-edge technology with proven training methodologies for unprecedented results."})]}),(0,r.jsx)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"flex flex-wrap justify-center gap-4 mb-16",children:[{id:"all",name:"All Classes"},{id:"vr",name:"VR Training"},{id:"ai",name:"AI Coaching"},{id:"biometric",name:"Biometric"},{id:"group",name:"Group Sessions"}].map(n=>(0,r.jsx)("button",{onClick:()=>t(n.id),className:`px-6 py-3 rounded-lg font-orbitron font-medium transition-all duration-300 ${e===n.id?"bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg":"glass text-gray-300 hover:text-white hover:bg-white/10"}`,children:n.name},n.id))}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:h.map((e,t)=>{let n=e.icon;return(0,r.jsx)(a.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},layout:!0,children:(0,r.jsxs)("div",{className:"glass rounded-2xl overflow-hidden h-full hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300",children:[(0,r.jsxs)("div",{className:"relative h-48 bg-gradient-to-br from-slate-800 to-slate-900 flex items-center justify-center",children:[(0,r.jsx)(n,{className:"w-16 h-16 text-cyan-400 opacity-50"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"font-orbitron text-xl font-bold text-white",children:e.title}),(0,r.jsx)(n,{className:"w-6 h-6 text-cyan-400"})]}),(0,r.jsx)("p",{className:"font-exo text-gray-400 mb-6 leading-relaxed",children:e.description}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 text-cyan-400 mx-auto mb-1"}),(0,r.jsx)("span",{className:"font-rajdhani text-sm text-gray-400",children:e.duration})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 text-purple-400 mx-auto mb-1"}),(0,r.jsx)("span",{className:"font-rajdhani text-sm text-gray-400",children:e.intensity})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 text-green-400 mx-auto mb-1"}),(0,r.jsx)("span",{className:"font-rajdhani text-sm text-gray-400",children:e.participants})]})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:e.features.map(e=>(0,r.jsx)("span",{className:"px-3 py-1 bg-white/10 rounded-full text-xs font-rajdhani text-gray-300",children:e},e))}),(0,r.jsx)("button",{className:"w-full px-6 py-3 bg-transparent border-2 border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black rounded-lg font-orbitron font-semibold transition-all duration-300",children:"Book Session"})]})]})},e.id)})})]})]})}},4759:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Store\\\\gym-website\\\\neogym\\\\src\\\\components\\\\sections\\\\Contact.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Contact.tsx","default")},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return o},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return x}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,i=Array(r),a=0;a<r;a++)i[a]=arguments[a];return n||(n=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function s(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function o(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&u(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let h="undefined"!=typeof performance,p=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class f extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(e){return JSON.stringify({message:e.message,stack:e.stack})}},4949:(e,t)=>{"use strict";function n(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return n}})},4975:(e,t,n)=>{"use strict";n.d(t,{default:()=>h});var r=n(687),i=n(3210),a=n(6001),s=n(7992),o=n(8340),l=n(3931),u=n(8730),c=n(5583);let d=(0,n(2688).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),h=()=>{let[e,t]=(0,i.useState)({name:"",email:"",phone:"",interest:"",message:""}),[n,h]=(0,i.useState)(!1),p=n=>{t({...e,[n.target.name]:n.target.value})},f=async n=>{n.preventDefault(),h(!0),await new Promise(e=>setTimeout(e,2e3)),console.log("Form submitted:",e),h(!1),t({name:"",email:"",phone:"",interest:"",message:""})},m=[{icon:s.A,title:"Location",details:["2077 Future Street","Neo City, NC 12345"],color:"blue"},{icon:o.A,title:"Phone",details:["+1 (555) NEO-GYMS","+****************"],color:"purple"},{icon:l.A,title:"Email",details:["<EMAIL>","<EMAIL>"],color:"green"},{icon:u.A,title:"Hours",details:["24/7 AI Access","Human Support: 6AM-10PM"],color:"cyan"}];return(0,r.jsxs)("section",{className:"py-24 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-blue-900/10 via-transparent to-purple-900/10"}),(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,r.jsxs)("div",{className:"inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8",children:[(0,r.jsx)(c.A,{className:"w-5 h-5 text-neon-blue"}),(0,r.jsx)("span",{className:"font-orbitron text-sm font-medium text-white",children:"GET IN TOUCH"})]}),(0,r.jsxs)("h2",{className:"font-orbitron text-4xl md:text-6xl font-bold mb-8",children:[(0,r.jsx)("span",{className:"text-white",children:"START YOUR "}),(0,r.jsx)("span",{className:"text-gradient-neon",children:"FUTURE"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"text-gradient-cyber",children:"FITNESS JOURNEY"})]}),(0,r.jsx)("p",{className:"font-exo text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed",children:"Ready to experience the next evolution of fitness? Contact us to schedule your personalized tour and free trial session."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-7xl mx-auto",children:[(0,r.jsx)(a.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:(0,r.jsxs)("div",{className:"glass rounded-2xl p-8 hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300",children:[(0,r.jsx)("h3",{className:"font-orbitron text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent mb-8",children:"Send us a Message"}),(0,r.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block font-orbitron text-sm font-medium text-white mb-2",children:"Name *"}),(0,r.jsx)("input",{type:"text",name:"name",value:e.name,onChange:p,required:!0,className:"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",placeholder:"Your full name"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block font-orbitron text-sm font-medium text-white mb-2",children:"Email *"}),(0,r.jsx)("input",{type:"email",name:"email",value:e.email,onChange:p,required:!0,className:"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",placeholder:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block font-orbitron text-sm font-medium text-white mb-2",children:"Phone"}),(0,r.jsx)("input",{type:"tel",name:"phone",value:e.phone,onChange:p,className:"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",placeholder:"(*************"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block font-orbitron text-sm font-medium text-white mb-2",children:"Interest"}),(0,r.jsxs)("select",{name:"interest",value:e.interest,onChange:p,className:"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",children:[(0,r.jsx)("option",{value:"",children:"Select your interest"}),(0,r.jsx)("option",{value:"membership",children:"Membership Plans"}),(0,r.jsx)("option",{value:"trial",children:"Free Trial"}),(0,r.jsx)("option",{value:"corporate",children:"Corporate Programs"}),(0,r.jsx)("option",{value:"personal",children:"Personal Training"}),(0,r.jsx)("option",{value:"other",children:"Other"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block font-orbitron text-sm font-medium text-white mb-2",children:"Message"}),(0,r.jsx)("textarea",{name:"message",value:e.message,onChange:p,rows:5,className:"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 resize-none",placeholder:"Tell us about your fitness goals and how we can help..."})]}),(0,r.jsx)("button",{type:"submit",className:"w-full px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg font-orbitron font-bold text-white text-lg hover:from-blue-500 hover:to-purple-500 transition-all duration-300 shadow-lg hover:shadow-blue-500/50 disabled:opacity-50",disabled:n,children:n?(0,r.jsxs)("span",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,r.jsx)("span",{children:"Sending..."})]}):(0,r.jsxs)("span",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)(d,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Send Message"})]})})]})]})}),(0,r.jsxs)(a.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-8",children:[m.map((e,t)=>{let n=e.icon;return(0,r.jsx)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},children:(0,r.jsx)("div",{className:"glass rounded-2xl p-6 hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg",children:(0,r.jsx)(n,{className:"w-6 h-6 text-cyan-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-orbitron text-lg font-bold text-white mb-2",children:e.title}),e.details.map((e,t)=>(0,r.jsx)("p",{className:"font-exo text-gray-300",children:e},t))]})]})})},e.title)}),(0,r.jsxs)("div",{className:"glass rounded-2xl p-6 hover:shadow-lg hover:shadow-purple-500/20 transition-all duration-300",children:[(0,r.jsx)("h4",{className:"font-orbitron text-lg font-bold text-white mb-4",children:"Find Us"}),(0,r.jsx)("div",{className:"h-48 bg-gradient-to-br from-slate-800 to-slate-900 rounded-lg flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(s.A,{className:"w-12 h-12 text-purple-400 mx-auto mb-2"}),(0,r.jsx)("p",{className:"font-exo text-gray-400",children:"Interactive Map"}),(0,r.jsx)("p",{className:"font-exo text-sm text-gray-500",children:"Coming Soon"})]})})]})]})]})]})]})}},5076:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{prefetchQueue:function(){return a},prefetchReducer:function(){return s}});let r=n(5144),i=n(5334),a=new r.PromiseQueue(5),s=function(e,t){(0,i.prunePrefetchCache)(e.prefetchCache);let{url:n}=t;return(0,i.getOrCreatePrefetchCacheEntry)({url:n,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5144:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let r=n(6312),i=n(9656);var a=i._("_maxConcurrency"),s=i._("_runningCount"),o=i._("_queue"),l=i._("_processNext");class u{enqueue(e){let t,n,i=new Promise((e,r)=>{t=e,n=r}),a=async()=>{try{r._(this,s)[s]++;let n=await e();t(n)}catch(e){n(e)}finally{r._(this,s)[s]--,r._(this,l)[l]()}};return r._(this,o)[o].push({promiseFn:i,task:a}),r._(this,l)[l](),i}bump(e){let t=r._(this,o)[o].findIndex(t=>t.promiseFn===e);if(t>-1){let e=r._(this,o)[o].splice(t,1)[0];r._(this,o)[o].unshift(e),r._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:c}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),Object.defineProperty(this,o,{writable:!0,value:void 0}),r._(this,a)[a]=e,r._(this,s)[s]=0,r._(this,o)[o]=[]}}function c(e){if(void 0===e&&(e=!1),(r._(this,s)[s]<r._(this,a)[a]||e)&&r._(this,o)[o].length>0){var t;null==(t=r._(this,o)[o].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5232:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{handleExternalUrl:function(){return x},navigateReducer:function(){return function e(t,n){let{url:w,isExternalUrl:j,navigateType:P,shouldScroll:T,allowAliasing:E}=n,R={},{hash:A}=w,N=(0,i.createHrefFromUrl)(w),S="push"===P;if((0,g.prunePrefetchCache)(t.prefetchCache),R.preserveCustomHistoryState=!1,R.pendingPush=S,j)return x(t,R,w.toString(),S);if(document.getElementById("__next-page-redirect"))return x(t,R,N,S);let _=(0,g.getOrCreatePrefetchCacheEntry)({url:w,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:E}),{treeAtTimeOfPrefetch:M,data:C}=_;return h.prefetchQueue.bump(C),C.then(h=>{let{flightData:g,canonicalUrl:j,postponed:P}=h,E=Date.now(),C=!1;if(_.lastUsedTime||(_.lastUsedTime=E,C=!0),_.aliased){let r=(0,v.handleAliasedPrefetchEntry)(E,t,g,w,R);return!1===r?e(t,{...n,allowAliasing:!1}):r}if("string"==typeof g)return x(t,R,g,S);let O=j?(0,i.createHrefFromUrl)(j):N;if(A&&t.canonicalUrl.split("#",1)[0]===O.split("#",1)[0])return R.onlyHashChange=!0,R.canonicalUrl=O,R.shouldScroll=T,R.hashFragment=A,R.scrollableSegments=[],(0,c.handleMutable)(t,R);let k=t.tree,D=t.cache,I=[];for(let e of g){let{pathToSegment:n,seedData:i,head:c,isHeadPartial:h,isRootRender:g}=e,v=e.tree,j=["",...n],T=(0,s.applyRouterStatePatchToTree)(j,k,v,N);if(null===T&&(T=(0,s.applyRouterStatePatchToTree)(j,M,v,N)),null!==T){if(i&&g&&P){let e=(0,m.startPPRNavigation)(E,D,k,v,i,c,h,!1,I);if(null!==e){if(null===e.route)return x(t,R,N,S);T=e.route;let n=e.node;null!==n&&(R.cache=n);let i=e.dynamicRequestTree;if(null!==i){let n=(0,r.fetchServerResponse)(w,{flightRouterState:i,nextUrl:t.nextUrl});(0,m.listenForDynamicRequest)(e,n)}}else T=v}else{if((0,l.isNavigatingToNewRootLayout)(k,T))return x(t,R,N,S);let r=(0,p.createEmptyCacheNode)(),i=!1;for(let t of(_.status!==u.PrefetchCacheEntryStatus.stale||C?i=(0,d.applyFlightData)(E,D,r,e,_):(i=function(e,t,n,r){let i=!1;for(let a of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),b(r).map(e=>[...n,...e])))(0,y.clearCacheNodeDataForSegmentPath)(e,t,a),i=!0;return i}(r,D,n,v),_.lastUsedTime=E),(0,o.shouldHardNavigate)(j,k)?(r.rsc=D.rsc,r.prefetchRsc=D.prefetchRsc,(0,a.invalidateCacheBelowFlightSegmentPath)(r,D,n),R.cache=r):i&&(R.cache=r,D=r),b(v))){let e=[...n,...t];e[e.length-1]!==f.DEFAULT_SEGMENT_KEY&&I.push(e)}}k=T}}return R.patchedTree=k,R.canonicalUrl=O,R.scrollableSegments=I,R.hashFragment=A,R.shouldScroll=T,(0,c.handleMutable)(t,R)},()=>t)}}});let r=n(9008),i=n(7391),a=n(8468),s=n(6770),o=n(5951),l=n(2030),u=n(9154),c=n(9435),d=n(6928),h=n(5076),p=n(9752),f=n(3913),m=n(5956),g=n(5334),y=n(7464),v=n(9707);function x(e,t,n,r){return t.mpaNavigation=!0,t.canonicalUrl=n,t.pendingPush=r,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function b(e){let t=[],[n,r]=e;if(0===Object.keys(r).length)return[[n]];for(let[e,i]of Object.entries(r))for(let r of b(i))""===n?t.push([e,...r]):t.push([n,e,...r]);return t}n(593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5300:(e,t,n)=>{"use strict";n.d(t,{default:()=>R});var r=n(687),i=n(3210),a=n(6001),s=n(2157),o=n(2789),l=n(2743),u=n(1279),c=n(8171),d=n(2582);class h extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,c.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p({children:e,isPresent:t,anchorX:n}){let a=(0,i.useId)(),s=(0,i.useRef)(null),o=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,i.useContext)(d.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:r,top:i,left:u,right:c}=o.current;if(t||!s.current||!e||!r)return;let d="left"===n?`left: ${u}`:`right: ${c}`;s.current.dataset.motionPopId=a;let h=document.createElement("style");return l&&(h.nonce=l),document.head.appendChild(h),h.sheet&&h.sheet.insertRule(`
          [data-motion-pop-id="${a}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            ${d}px !important;
            top: ${i}px !important;
          }
        `),()=>{document.head.contains(h)&&document.head.removeChild(h)}},[t]),(0,r.jsx)(h,{isPresent:t,childRef:s,sizeRef:o,children:i.cloneElement(e,{ref:s})})}let f=({children:e,initial:t,isPresent:n,onExitComplete:a,custom:s,presenceAffectsLayout:l,mode:c,anchorX:d})=>{let h=(0,o.M)(m),f=(0,i.useId)(),g=!0,y=(0,i.useMemo)(()=>(g=!1,{id:f,initial:t,isPresent:n,custom:s,onExitComplete:e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;a&&a()},register:e=>(h.set(e,!1),()=>h.delete(e))}),[n,h,a]);return l&&g&&(y={...y}),(0,i.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[n]),i.useEffect(()=>{n||h.size||!a||a()},[n]),"popLayout"===c&&(e=(0,r.jsx)(p,{isPresent:n,anchorX:d,children:e})),(0,r.jsx)(u.t.Provider,{value:y,children:e})};function m(){return new Map}var g=n(6044);let y=e=>e.key||"";function v(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let x=({children:e,custom:t,initial:n=!0,onExitComplete:a,presenceAffectsLayout:u=!0,mode:c="sync",propagate:d=!1,anchorX:h="left"})=>{let[p,m]=(0,g.xQ)(d),x=(0,i.useMemo)(()=>v(e),[e]),b=d&&!p?[]:x.map(y),w=(0,i.useRef)(!0),j=(0,i.useRef)(x),P=(0,o.M)(()=>new Map),[T,E]=(0,i.useState)(x),[R,A]=(0,i.useState)(x);(0,l.E)(()=>{w.current=!1,j.current=x;for(let e=0;e<R.length;e++){let t=y(R[e]);b.includes(t)?P.delete(t):!0!==P.get(t)&&P.set(t,!1)}},[R,b.length,b.join("-")]);let N=[];if(x!==T){let e=[...x];for(let t=0;t<R.length;t++){let n=R[t],r=y(n);b.includes(r)||(e.splice(t,0,n),N.push(n))}return"wait"===c&&N.length&&(e=N),A(v(e)),E(x),null}let{forceRender:S}=(0,i.useContext)(s.L);return(0,r.jsx)(r.Fragment,{children:R.map(e=>{let i=y(e),s=(!d||!!p)&&(x===R||b.includes(i));return(0,r.jsx)(f,{isPresent:s,initial:(!w.current||!!n)&&void 0,custom:t,presenceAffectsLayout:u,mode:c,onExitComplete:s?void 0:()=>{if(!P.has(i))return;P.set(i,!0);let e=!0;P.forEach(t=>{t||(e=!1)}),e&&(S?.(),A(j.current),d&&m?.(),a&&a())},anchorX:h,children:e},i)})})};var b=n(4398),w=n(2688);let j=(0,w.A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]]),P=(0,w.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),T=(0,w.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),E=(0,w.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),R=()=>{let[e,t]=(0,i.useState)(0),n=[{id:1,name:"Sarah Chen",title:"Biotech Engineer",location:"Neo Tokyo",rating:5,text:"NeoGym's AI trainer ARIA-7 completely transformed my approach to fitness. The neural feedback technology helped me achieve a 40% strength increase in just 3 months. It's like having a personal trainer from the future!",avatar:"/api/placeholder/80/80",achievement:"40% Strength Increase",timeframe:"3 months"},{id:2,name:"Marcus Rodriguez",title:"VR Developer",location:"Cyber City",rating:5,text:"The VR HIIT sessions with NOVA-X are absolutely incredible. I've never enjoyed cardio this much! The immersive environments make you forget you're even working out. Lost 25 pounds and gained so much endurance.",avatar:"/api/placeholder/80/80",achievement:"25 lbs Weight Loss",timeframe:"4 months"},{id:3,name:"Dr. Aisha Patel",title:"Neuroscientist",location:"Future Labs",rating:5,text:"As a scientist, I was skeptical about biometric yoga, but ZENITH-9's mind-body synchronization protocols are revolutionary. My stress levels dropped by 60% and my flexibility improved dramatically.",avatar:"/api/placeholder/80/80",achievement:"60% Stress Reduction",timeframe:"2 months"},{id:4,name:"Alex Thompson",title:"Professional Athlete",location:"Elite Sports Complex",rating:5,text:"TITAN-5's HIIT optimization pushed my performance to levels I never thought possible. The real-time adaptation to my limits helped me break multiple personal records. This is the future of athletic training.",avatar:"/api/placeholder/80/80",achievement:"Multiple PRs",timeframe:"6 weeks"},{id:5,name:"Luna Kim",title:"Tech Entrepreneur",location:"Innovation District",rating:5,text:"The holographic personal training sessions are mind-blowing. Having a dedicated AI coach that learns and adapts to my schedule and preferences has made consistency effortless. Best investment I've ever made.",avatar:"/api/placeholder/80/80",achievement:"Perfect Consistency",timeframe:"5 months"}],s=()=>{t(e=>(e+1)%n.length)};return(0,i.useEffect)(()=>{let e=setInterval(s,5e3);return()=>clearInterval(e)},[s]),(0,r.jsxs)("section",{className:"py-24 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-cyan-900/10 via-transparent to-blue-900/10"}),(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,r.jsxs)("div",{className:"inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8",children:[(0,r.jsx)(b.A,{className:"w-5 h-5 text-neon-cyan"}),(0,r.jsx)("span",{className:"font-orbitron text-sm font-medium text-white",children:"MEMBER TESTIMONIALS"})]}),(0,r.jsxs)("h2",{className:"font-orbitron text-4xl md:text-6xl font-bold mb-8",children:[(0,r.jsx)("span",{className:"text-white",children:"VOICES FROM "}),(0,r.jsx)("span",{className:"text-gradient-neon",children:"THE"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"text-gradient-cyber",children:"FUTURE"})]}),(0,r.jsx)("p",{className:"font-exo text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed",children:"Hear from our members who have experienced the transformative power of next-generation fitness technology."})]}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto mb-16",children:(0,r.jsx)(x,{mode:"wait",children:(0,r.jsx)(a.P.div,{initial:{opacity:0,x:100},animate:{opacity:1,x:0},exit:{opacity:0,x:-100},transition:{duration:.5},children:(0,r.jsxs)("div",{className:"glass rounded-2xl p-12 text-center relative hover:shadow-lg hover:shadow-cyan-500/20 transition-all duration-300",children:[(0,r.jsx)("div",{className:"absolute top-8 left-8",children:(0,r.jsx)(j,{className:"w-12 h-12 text-cyan-400 opacity-30"})}),(0,r.jsx)("div",{className:"flex justify-center mb-6",children:[...Array(n[e].rating)].map((e,t)=>(0,r.jsx)(b.A,{className:"w-6 h-6 text-yellow-400 fill-current"},t))}),(0,r.jsxs)("blockquote",{className:"text-xl text-gray-300 leading-relaxed mb-8 italic",children:["“",n[e].text,"”"]}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-6",children:[(0,r.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-slate-700 to-slate-800 rounded-full flex items-center justify-center",children:(0,r.jsx)(P,{className:"w-10 h-10 text-neon-cyan"})}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("h4",{className:"font-orbitron text-xl font-bold text-white",children:n[e].name}),(0,r.jsx)("p",{className:"font-rajdhani text-neon-cyan",children:n[e].title}),(0,r.jsx)("p",{className:"font-exo text-sm text-gray-400",children:n[e].location})]}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsxs)("div",{className:"bg-gradient-to-r from-green-500/20 to-blue-500/20 border border-green-500/30 rounded-lg px-4 py-2",children:[(0,r.jsx)("p",{className:"font-orbitron text-sm font-bold text-green-400",children:n[e].achievement}),(0,r.jsxs)("p",{className:"font-exo text-xs text-gray-400",children:["in ",n[e].timeframe]})]})})]})]})},e)})}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-8 mb-12",children:[(0,r.jsx)("button",{onClick:()=>{t(e=>(e-1+n.length)%n.length)},className:"p-3 glass rounded-full hover:bg-white/10 transition-colors duration-300 group",children:(0,r.jsx)(T,{className:"w-6 h-6 text-white group-hover:text-neon-cyan transition-colors duration-300"})}),(0,r.jsx)("div",{className:"flex space-x-3",children:n.map((n,i)=>(0,r.jsx)("button",{onClick:()=>t(i),className:`w-3 h-3 rounded-full transition-all duration-300 ${i===e?"bg-neon-cyan shadow-[0_0_10px_#00ffff]":"bg-white/30 hover:bg-white/50"}`},i))}),(0,r.jsx)("button",{onClick:s,className:"p-3 glass rounded-full hover:bg-white/10 transition-colors duration-300 group",children:(0,r.jsx)(E,{className:"w-6 h-6 text-white group-hover:text-neon-cyan transition-colors duration-300"})})]}),(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"font-orbitron text-3xl font-bold text-gradient-neon mb-2",children:"4.9"}),(0,r.jsx)("div",{className:"font-exo text-gray-400",children:"Average Rating"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"font-orbitron text-3xl font-bold text-gradient-cyber mb-2",children:"10K+"}),(0,r.jsx)("div",{className:"font-exo text-gray-400",children:"Happy Members"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"font-orbitron text-3xl font-bold text-gradient-neon mb-2",children:"95%"}),(0,r.jsx)("div",{className:"font-exo text-gray-400",children:"Goal Achievement"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"font-orbitron text-3xl font-bold text-gradient-cyber mb-2",children:"24/7"}),(0,r.jsx)("div",{className:"font-exo text-gray-400",children:"AI Support"})]})]})]})]})}},5303:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Store\\\\gym-website\\\\neogym\\\\src\\\\components\\\\sections\\\\Classes.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Classes.tsx","default")},5334:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DYNAMIC_STALETIME_MS:function(){return h},STATIC_STALETIME_MS:function(){return p},createSeededPrefetchCacheEntry:function(){return u},getOrCreatePrefetchCacheEntry:function(){return l},prunePrefetchCache:function(){return d}});let r=n(9008),i=n(9154),a=n(5076);function s(e,t,n){let r=e.pathname;return(t&&(r+=e.search),n)?""+n+"%"+r:r}function o(e,t,n){return s(e,t===i.PrefetchKind.FULL,n)}function l(e){let{url:t,nextUrl:n,tree:r,prefetchCache:a,kind:o,allowAliasing:l=!0}=e,u=function(e,t,n,r,a){for(let o of(void 0===t&&(t=i.PrefetchKind.TEMPORARY),[n,null])){let n=s(e,!0,o),l=s(e,!1,o),u=e.search?n:l,c=r.get(u);if(c&&a){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let d=r.get(l);if(a&&e.search&&t!==i.PrefetchKind.FULL&&d&&!d.key.includes("%"))return{...d,aliased:!0}}if(t!==i.PrefetchKind.FULL&&a){for(let t of r.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,o,n,a,l);return u?(u.status=f(u),u.kind!==i.PrefetchKind.FULL&&o===i.PrefetchKind.FULL&&u.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:r,url:t,nextUrl:n,prefetchCache:a,kind:null!=o?o:i.PrefetchKind.TEMPORARY})}),o&&u.kind===i.PrefetchKind.TEMPORARY&&(u.kind=o),u):c({tree:r,url:t,nextUrl:n,prefetchCache:a,kind:o||i.PrefetchKind.TEMPORARY})}function u(e){let{nextUrl:t,tree:n,prefetchCache:r,url:a,data:s,kind:l}=e,u=s.couldBeIntercepted?o(a,l,t):o(a,l),c={treeAtTimeOfPrefetch:n,data:Promise.resolve(s),kind:l,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:u,status:i.PrefetchCacheEntryStatus.fresh,url:a};return r.set(u,c),c}function c(e){let{url:t,kind:n,tree:s,nextUrl:l,prefetchCache:u}=e,c=o(t,n),d=a.prefetchQueue.enqueue(()=>(0,r.fetchServerResponse)(t,{flightRouterState:s,nextUrl:l,prefetchKind:n}).then(e=>{let n;if(e.couldBeIntercepted&&(n=function(e){let{url:t,nextUrl:n,prefetchCache:r,existingCacheKey:i}=e,a=r.get(i);if(!a)return;let s=o(t,a.kind,n);return r.set(s,{...a,key:s}),r.delete(i),s}({url:t,existingCacheKey:c,nextUrl:l,prefetchCache:u})),e.prerendered){let t=u.get(null!=n?n:c);t&&(t.kind=i.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),h={treeAtTimeOfPrefetch:s,data:d,kind:n,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:i.PrefetchCacheEntryStatus.fresh,url:t};return u.set(c,h),h}function d(e){for(let[t,n]of e)f(n)===i.PrefetchCacheEntryStatus.expired&&e.delete(t)}let h=1e3*Number("0"),p=1e3*Number("300");function f(e){let{kind:t,prefetchTime:n,lastUsedTime:r,staleTime:a}=e;return -1!==a?Date.now()<n+a?i.PrefetchCacheEntryStatus.fresh:i.PrefetchCacheEntryStatus.stale:Date.now()<(null!=r?r:n)+h?r?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.fresh:t===i.PrefetchKind.AUTO&&Date.now()<n+p?i.PrefetchCacheEntryStatus.stale:t===i.PrefetchKind.FULL&&Date.now()<n+p?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5344:(e,t,n)=>{"use strict";n.d(t,{default:()=>l});var r=n(687),i=n(5583),a=n(2688);let s=(0,a.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),o=(0,a.A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),l=()=>(0,r.jsxs)("section",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 to-black",children:[(0,r.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,r.jsxs)("div",{className:"inline-flex items-center space-x-2 px-4 py-2 glass rounded-full mb-8",children:[(0,r.jsx)(i.A,{className:"w-4 h-4 text-blue-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-300",children:"Next Generation Fitness"})]}),(0,r.jsxs)("h1",{className:"font-space text-5xl md:text-7xl font-bold mb-6",children:[(0,r.jsx)("span",{className:"text-white",children:"Transform Your"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent",children:"Fitness Journey"})]}),(0,r.jsx)("p",{className:"text-xl text-gray-400 mb-12 max-w-2xl mx-auto",children:"Experience the future of fitness with AI-powered training, virtual reality workouts, and personalized coaching."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-4 mb-16",children:[(0,r.jsxs)("button",{className:"btn-primary flex items-center space-x-2",children:[(0,r.jsx)("span",{children:"Get Started"}),(0,r.jsx)(s,{className:"w-4 h-4"})]}),(0,r.jsxs)("button",{className:"btn-secondary flex items-center space-x-2",children:[(0,r.jsx)(o,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Watch Demo"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:"10,000+"}),(0,r.jsx)("div",{className:"text-gray-400",children:"Active Members"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:"50+"}),(0,r.jsx)("div",{className:"text-gray-400",children:"AI Trainers"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:"24/7"}),(0,r.jsx)("div",{className:"text-gray-400",children:"Access"})]})]})]})}),(0,r.jsx)("div",{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsx)("div",{className:"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center",children:(0,r.jsx)("div",{className:"w-1 h-3 bg-white rounded-full mt-2 animate-bounce"})}),(0,r.jsx)("span",{className:"text-xs text-gray-400",children:"Scroll to explore"})]})})]})},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var n=function(e){for(var t=[],n=0;n<e.length;){var r=e[n];if("*"===r||"+"===r||"?"===r){t.push({type:"MODIFIER",index:n,value:e[n++]});continue}if("\\"===r){t.push({type:"ESCAPED_CHAR",index:n++,value:e[n++]});continue}if("{"===r){t.push({type:"OPEN",index:n,value:e[n++]});continue}if("}"===r){t.push({type:"CLOSE",index:n,value:e[n++]});continue}if(":"===r){for(var i="",a=n+1;a<e.length;){var s=e.charCodeAt(a);if(s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||95===s){i+=e[a++];continue}break}if(!i)throw TypeError("Missing parameter name at "+n);t.push({type:"NAME",index:n,value:i}),n=a;continue}if("("===r){var o=1,l="",a=n+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;){if("\\"===e[a]){l+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--o){a++;break}}else if("("===e[a]&&(o++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at "+a);l+=e[a++]}if(o)throw TypeError("Unbalanced pattern at "+n);if(!l)throw TypeError("Missing pattern at "+n);t.push({type:"PATTERN",index:n,value:l}),n=a;continue}t.push({type:"CHAR",index:n,value:e[n++]})}return t.push({type:"END",index:n,value:""}),t}(e),r=t.prefixes,a=void 0===r?"./":r,s="[^"+i(t.delimiter||"/#?")+"]+?",o=[],l=0,u=0,c="",d=function(e){if(u<n.length&&n[u].type===e)return n[u++].value},h=function(e){var t=d(e);if(void 0!==t)return t;var r=n[u];throw TypeError("Unexpected "+r.type+" at "+r.index+", expected "+e)},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};u<n.length;){var f=d("CHAR"),m=d("NAME"),g=d("PATTERN");if(m||g){var y=f||"";-1===a.indexOf(y)&&(c+=y,y=""),c&&(o.push(c),c=""),o.push({name:m||l++,prefix:y,suffix:"",pattern:g||s,modifier:d("MODIFIER")||""});continue}var v=f||d("ESCAPED_CHAR");if(v){c+=v;continue}if(c&&(o.push(c),c=""),d("OPEN")){var y=p(),x=d("NAME")||"",b=d("PATTERN")||"",w=p();h("CLOSE"),o.push({name:x||(b?l++:""),pattern:x&&!b?s:b,prefix:y,suffix:w,modifier:d("MODIFIER")||""});continue}h("END")}return o}function n(e,t){void 0===t&&(t={});var n=a(t),r=t.encode,i=void 0===r?function(e){return e}:r,s=t.validate,o=void 0===s||s,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",n)});return function(t){for(var n="",r=0;r<e.length;r++){var a=e[r];if("string"==typeof a){n+=a;continue}var s=t?t[a.name]:void 0,u="?"===a.modifier||"*"===a.modifier,c="*"===a.modifier||"+"===a.modifier;if(Array.isArray(s)){if(!c)throw TypeError('Expected "'+a.name+'" to not repeat, but got an array');if(0===s.length){if(u)continue;throw TypeError('Expected "'+a.name+'" to not be empty')}for(var d=0;d<s.length;d++){var h=i(s[d],a);if(o&&!l[r].test(h))throw TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+h+'"');n+=a.prefix+h+a.suffix}continue}if("string"==typeof s||"number"==typeof s){var h=i(String(s),a);if(o&&!l[r].test(h))throw TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+h+'"');n+=a.prefix+h+a.suffix;continue}if(!u){var p=c?"an array":"a string";throw TypeError('Expected "'+a.name+'" to be '+p)}}return n}}function r(e,t,n){void 0===n&&(n={});var r=n.decode,i=void 0===r?function(e){return e}:r;return function(n){var r=e.exec(n);if(!r)return!1;for(var a=r[0],s=r.index,o=Object.create(null),l=1;l<r.length;l++)!function(e){if(void 0!==r[e]){var n=t[e-1];"*"===n.modifier||"+"===n.modifier?o[n.name]=r[e].split(n.prefix+n.suffix).map(function(e){return i(e,n)}):o[n.name]=i(r[e],n)}}(l);return{path:a,index:s,params:o}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function s(e,t,n){void 0===n&&(n={});for(var r=n.strict,s=void 0!==r&&r,o=n.start,l=n.end,u=n.encode,c=void 0===u?function(e){return e}:u,d="["+i(n.endsWith||"")+"]|$",h="["+i(n.delimiter||"/#?")+"]",p=void 0===o||o?"^":"",f=0;f<e.length;f++){var m=e[f];if("string"==typeof m)p+=i(c(m));else{var g=i(c(m.prefix)),y=i(c(m.suffix));if(m.pattern)if(t&&t.push(m),g||y)if("+"===m.modifier||"*"===m.modifier){var v="*"===m.modifier?"?":"";p+="(?:"+g+"((?:"+m.pattern+")(?:"+y+g+"(?:"+m.pattern+"))*)"+y+")"+v}else p+="(?:"+g+"("+m.pattern+")"+y+")"+m.modifier;else p+="("+m.pattern+")"+m.modifier;else p+="(?:"+g+y+")"+m.modifier}}if(void 0===l||l)s||(p+=h+"?"),p+=n.endsWith?"(?="+d+")":"$";else{var x=e[e.length-1],b="string"==typeof x?h.indexOf(x[x.length-1])>-1:void 0===x;s||(p+="(?:"+h+"(?="+d+"))?"),b||(p+="(?="+h+"|"+d+")")}return new RegExp(p,a(n))}function o(t,n,r){if(t instanceof RegExp){if(!n)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var l=0;l<i.length;l++)n.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,n,r).source}).join("|")+")",a(r)):s(e(t,r),n,r)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,r){return n(e(t,r),r)},t.tokensToFunction=n,t.match=function(e,t){var n=[];return r(o(e,n,t),n,t)},t.regexpToFunction=r,t.tokensToRegexp=s,t.pathToRegexp=o})(),e.exports=t})()},5416:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return r.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return a},getBotType:function(){return l},isBot:function(){return o}});let r=n(5796),i=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,a=r.HTML_LIMITED_BOT_UA_RE.source;function s(e){return r.HTML_LIMITED_BOT_UA_RE.test(e)}function o(e){return i.test(e)||s(e)}function l(e){return i.test(e)?"dom":s(e)?"html":void 0}},5526:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{compileNonPath:function(){return c},matchHas:function(){return u},parseDestination:function(){return d},prepareDestination:function(){return h}});let r=n(5362),i=n(3293),a=n(6759),s=n(1437),o=n(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,n,r){void 0===n&&(n=[]),void 0===r&&(r=[]);let i={},a=n=>{let r,a=n.key;switch(n.type){case"header":a=a.toLowerCase(),r=e.headers[a];break;case"cookie":r="cookies"in e?e.cookies[n.key]:(0,o.getCookieParser)(e.headers)()[n.key];break;case"query":r=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};r=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!n.value&&r)return i[function(e){let t="";for(let n=0;n<e.length;n++){let r=e.charCodeAt(n);(r>64&&r<91||r>96&&r<123)&&(t+=e[n])}return t}(a)]=r,!0;if(r){let e=RegExp("^"+n.value+"$"),t=Array.isArray(r)?r.slice(-1)[0].match(e):r.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===n.type&&t[0]&&(i.host=t[0])),!0}return!1};return!(!n.every(e=>a(e))||r.some(e=>a(e)))&&i}function c(e,t){if(!e.includes(":"))return e;for(let n of Object.keys(t))e.includes(":"+n)&&(e=e.replace(RegExp(":"+n+"\\*","g"),":"+n+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+n+"\\?","g"),":"+n+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+n+"\\+","g"),":"+n+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+n+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+n));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,r.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let n of Object.keys({...e.params,...e.query}))n&&(t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(n),"g"),"__ESC_COLON_"+n));let n=(0,a.parseUrl)(t),r=n.pathname;r&&(r=l(r));let s=n.href;s&&(s=l(s));let o=n.hostname;o&&(o=l(o));let u=n.hash;return u&&(u=l(u)),{...n,pathname:r,hostname:o,href:s,hash:u}}function h(e){let t,n,i=Object.assign({},e.query),a=d(e),{hostname:o,query:u}=a,h=a.pathname;a.hash&&(h=""+h+a.hash);let p=[],f=[];for(let e of((0,r.pathToRegexp)(h,f),f))p.push(e.name);if(o){let e=[];for(let t of((0,r.pathToRegexp)(o,e),e))p.push(t.name)}let m=(0,r.compile)(h,{validate:!1});for(let[n,i]of(o&&(t=(0,r.compile)(o,{validate:!1})),Object.entries(u)))Array.isArray(i)?u[n]=i.map(t=>c(l(t),e.params)):"string"==typeof i&&(u[n]=c(l(i),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>p.includes(e)))for(let t of g)t in u||(u[t]=e.params[t]);if((0,s.isInterceptionRouteAppPath)(h))for(let t of h.split("/")){let n=s.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(n){"(..)(..)"===n?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=n;break}}try{let[r,i]=(n=m(e.params)).split("#",2);t&&(a.hostname=t(e.params)),a.pathname=r,a.hash=(i?"#":"")+(i||""),delete a.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return a.query={...i,...a.query},{newUrl:n,destQuery:u,parsedDestination:a}}},5531:(e,t)=>{"use strict";function n(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return n}})},5583:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(2688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},5755:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Store\\\\gym-website\\\\neogym\\\\src\\\\components\\\\sections\\\\Membership.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Membership.tsx","default")},5796:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return n}});let n=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},5814:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},useLinkStatus:function(){return v}});let r=n(740),i=n(687),a=r._(n(3210)),s=n(195),o=n(2142),l=n(9154),u=n(3038),c=n(9289),d=n(6127);n(148);let h=n(3406),p=n(1794),f=n(3690);function m(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}function g(e){let t,n,r,[s,g]=(0,a.useOptimistic)(h.IDLE_LINK_STATUS),v=(0,a.useRef)(null),{href:x,as:b,children:w,prefetch:j=null,passHref:P,replace:T,shallow:E,scroll:R,onClick:A,onMouseEnter:N,onTouchStart:S,legacyBehavior:_=!1,onNavigate:M,ref:C,unstable_dynamicOnHover:O,...k}=e;t=w,_&&("string"==typeof t||"number"==typeof t)&&(t=(0,i.jsx)("a",{children:t}));let D=a.default.useContext(o.AppRouterContext),I=!1!==j,L=null===j?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:U,as:V}=a.default.useMemo(()=>{let e=m(x);return{href:e,as:b?m(b):e}},[x,b]);_&&(n=a.default.Children.only(t));let F=_?n&&"object"==typeof n&&n.ref:C,B=a.default.useCallback(e=>(null!==D&&(v.current=(0,h.mountLinkInstance)(e,U,D,L,I,g)),()=>{v.current&&((0,h.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,h.unmountPrefetchableInstance)(e)}),[I,U,D,L,g]),H={ref:(0,u.useMergedRef)(B,F),onClick(e){_||"function"!=typeof A||A(e),_&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),D&&(e.defaultPrevented||function(e,t,n,r,i,s,o){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){i&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),a.default.startTransition(()=>{if(o){let e=!1;if(o({preventDefault:()=>{e=!0}}),e)return}(0,f.dispatchNavigateAction)(n||t,i?"replace":"push",null==s||s,r.current)})}}(e,U,V,v,T,R,M))},onMouseEnter(e){_||"function"!=typeof N||N(e),_&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),D&&I&&(0,h.onNavigationIntent)(e.currentTarget,!0===O)},onTouchStart:function(e){_||"function"!=typeof S||S(e),_&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),D&&I&&(0,h.onNavigationIntent)(e.currentTarget,!0===O)}};return(0,c.isAbsoluteUrl)(V)?H.href=V:_&&!P&&("a"!==n.type||"href"in n.props)||(H.href=(0,d.addBasePath)(V)),r=_?a.default.cloneElement(n,H):(0,i.jsx)("a",{...k,...H,children:t}),(0,i.jsx)(y.Provider,{value:s,children:r})}n(2708);let y=(0,a.createContext)(h.IDLE_LINK_STATUS),v=()=>(0,a.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5942:(e,t,n)=>{"use strict";function r(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return r}}),n(6736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5951:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,n){let[a,s]=n,[o,l]=t;return(0,i.matchSegment)(o,a)?!(t.length<=2)&&e((0,r.getNextFlightSegmentPath)(t),s[l]):!!Array.isArray(o)}}});let r=n(4007),i=n(4077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5956:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{abortTask:function(){return f},listenForDynamicRequest:function(){return p},startPPRNavigation:function(){return u},updateCacheNodeOnPopstateRestoration:function(){return function e(t,n){let r=n[1],i=t.parallelRoutes,s=new Map(i);for(let t in r){let n=r[t],o=n[0],l=(0,a.createRouterCacheKey)(o),u=i.get(t);if(void 0!==u){let r=u.get(l);if(void 0!==r){let i=e(r,n),a=new Map(u);a.set(l,i),s.set(t,a)}}}let o=t.rsc,l=y(o)&&"pending"===o.status;return{lazyData:null,rsc:o,head:t.head,prefetchHead:l?t.prefetchHead:[null,null],prefetchRsc:l?t.prefetchRsc:null,loading:t.loading,parallelRoutes:s,navigatedAt:t.navigatedAt}}}});let r=n(3913),i=n(4077),a=n(3123),s=n(2030),o=n(5334),l={route:null,node:null,dynamicRequestTree:null,children:null};function u(e,t,n,s,o,u,h,p,f){return function e(t,n,s,o,u,h,p,f,m,g,y){let v=s[1],x=o[1],b=null!==h?h[2]:null;u||!0===o[4]&&(u=!0);let w=n.parallelRoutes,j=new Map(w),P={},T=null,E=!1,R={};for(let n in x){let s,o=x[n],d=v[n],h=w.get(n),A=null!==b?b[n]:null,N=o[0],S=g.concat([n,N]),_=(0,a.createRouterCacheKey)(N),M=void 0!==d?d[0]:void 0,C=void 0!==h?h.get(_):void 0;if(null!==(s=N===r.DEFAULT_SEGMENT_KEY?void 0!==d?{route:d,node:null,dynamicRequestTree:null,children:null}:c(t,d,o,C,u,void 0!==A?A:null,p,f,S,y):m&&0===Object.keys(o[1]).length?c(t,d,o,C,u,void 0!==A?A:null,p,f,S,y):void 0!==d&&void 0!==M&&(0,i.matchSegment)(N,M)&&void 0!==C&&void 0!==d?e(t,C,d,o,u,A,p,f,m,S,y):c(t,d,o,C,u,void 0!==A?A:null,p,f,S,y))){if(null===s.route)return l;null===T&&(T=new Map),T.set(n,s);let e=s.node;if(null!==e){let t=new Map(h);t.set(_,e),j.set(n,t)}let t=s.route;P[n]=t;let r=s.dynamicRequestTree;null!==r?(E=!0,R[n]=r):R[n]=t}else P[n]=o,R[n]=o}if(null===T)return null;let A={lazyData:null,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,loading:n.loading,parallelRoutes:j,navigatedAt:t};return{route:d(o,P),node:A,dynamicRequestTree:E?d(o,R):null,children:T}}(e,t,n,s,!1,o,u,h,p,[],f)}function c(e,t,n,r,i,u,c,p,f,m){return!i&&(void 0===t||(0,s.isNavigatingToNewRootLayout)(t,n))?l:function e(t,n,r,i,s,l,u,c){let p,f,m,g,y=n[1],v=0===Object.keys(y).length;if(void 0!==r&&r.navigatedAt+o.DYNAMIC_STALETIME_MS>t)p=r.rsc,f=r.loading,m=r.head,g=r.navigatedAt;else if(null===i)return h(t,n,null,s,l,u,c);else if(p=i[1],f=i[3],m=v?s:null,g=t,i[4]||l&&v)return h(t,n,i,s,l,u,c);let x=null!==i?i[2]:null,b=new Map,w=void 0!==r?r.parallelRoutes:null,j=new Map(w),P={},T=!1;if(v)c.push(u);else for(let n in y){let r=y[n],i=null!==x?x[n]:null,o=null!==w?w.get(n):void 0,d=r[0],h=u.concat([n,d]),p=(0,a.createRouterCacheKey)(d),f=e(t,r,void 0!==o?o.get(p):void 0,i,s,l,h,c);b.set(n,f);let m=f.dynamicRequestTree;null!==m?(T=!0,P[n]=m):P[n]=r;let g=f.node;if(null!==g){let e=new Map;e.set(p,g),j.set(n,e)}}return{route:n,node:{lazyData:null,rsc:p,prefetchRsc:null,head:m,prefetchHead:null,loading:f,parallelRoutes:j,navigatedAt:g},dynamicRequestTree:T?d(n,P):null,children:b}}(e,n,r,u,c,p,f,m)}function d(e,t){let n=[e[0],t];return 2 in e&&(n[2]=e[2]),3 in e&&(n[3]=e[3]),4 in e&&(n[4]=e[4]),n}function h(e,t,n,r,i,s,o){let l=d(t,t[1]);return l[3]="refetch",{route:t,node:function e(t,n,r,i,s,o,l){let u=n[1],c=null!==r?r[2]:null,d=new Map;for(let n in u){let r=u[n],h=null!==c?c[n]:null,p=r[0],f=o.concat([n,p]),m=(0,a.createRouterCacheKey)(p),g=e(t,r,void 0===h?null:h,i,s,f,l),y=new Map;y.set(m,g),d.set(n,y)}let h=0===d.size;h&&l.push(o);let p=null!==r?r[1]:null,f=null!==r?r[3]:null;return{lazyData:null,parallelRoutes:d,prefetchRsc:void 0!==p?p:null,prefetchHead:h?i:[null,null],loading:void 0!==f?f:null,rsc:v(),head:h?v():null,navigatedAt:t}}(e,t,n,r,i,s,o),dynamicRequestTree:l,children:null}}function p(e,t){t.then(t=>{let{flightData:n}=t;if("string"!=typeof n){for(let t of n){let{segmentPath:n,tree:r,seedData:s,head:o}=t;s&&function(e,t,n,r,s){let o=e;for(let e=0;e<t.length;e+=2){let n=t[e],r=t[e+1],a=o.children;if(null!==a){let e=a.get(n);if(void 0!==e){let t=e.route[0];if((0,i.matchSegment)(r,t)){o=e;continue}}}return}!function e(t,n,r,s){if(null===t.dynamicRequestTree)return;let o=t.children,l=t.node;if(null===o){null!==l&&(function e(t,n,r,s,o){let l=n[1],u=r[1],c=s[2],d=t.parallelRoutes;for(let t in l){let n=l[t],r=u[t],s=c[t],h=d.get(t),p=n[0],f=(0,a.createRouterCacheKey)(p),g=void 0!==h?h.get(f):void 0;void 0!==g&&(void 0!==r&&(0,i.matchSegment)(p,r[0])&&null!=s?e(g,n,r,s,o):m(n,g,null))}let h=t.rsc,p=s[1];null===h?t.rsc=p:y(h)&&h.resolve(p);let f=t.head;y(f)&&f.resolve(o)}(l,t.route,n,r,s),t.dynamicRequestTree=null);return}let u=n[1],c=r[2];for(let t in n){let n=u[t],r=c[t],a=o.get(t);if(void 0!==a){let t=a.route[0];if((0,i.matchSegment)(n[0],t)&&null!=r)return e(a,n,r,s)}}}(o,n,r,s)}(e,n,r,s,o)}f(e,null)}},t=>{f(e,t)})}function f(e,t){let n=e.node;if(null===n)return;let r=e.children;if(null===r)m(e.route,n,t);else for(let e of r.values())f(e,t);e.dynamicRequestTree=null}function m(e,t,n){let r=e[1],i=t.parallelRoutes;for(let e in r){let t=r[e],s=i.get(e);if(void 0===s)continue;let o=t[0],l=(0,a.createRouterCacheKey)(o),u=s.get(l);void 0!==u&&m(t,u,n)}let s=t.rsc;y(s)&&(null===n?s.resolve(null):s.reject(n));let o=t.head;y(o)&&o.resolve(null)}let g=Symbol();function y(e){return e&&e.tag===g}function v(){let e,t,n=new Promise((n,r)=>{e=n,t=r});return n.status="pending",n.resolve=t=>{"pending"===n.status&&(n.status="fulfilled",n.value=t,e(t))},n.reject=e=>{"pending"===n.status&&(n.status="rejected",n.reason=e,t(e))},n.tag=g,n}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6001:(e,t,n)=>{"use strict";let r;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function a(e){let t=[{},{}];return e?.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function s(e,t,n,r){if("function"==typeof t){let[i,s]=a(r);t=t(void 0!==n?n:e.custom,i,s)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,s]=a(r);t=t(void 0!==n?n:e.custom,i,s)}return t}function o(e,t,n){let r=e.getProps();return s(r,t,void 0!==n?n:r.custom,e)}function l(e,t){return e?.[t]??e?.default??e}n.d(t,{P:()=>aA});let u=e=>e,c={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],h={value:null,addProjectionMetrics:null};function p(e,t){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},a=()=>n=!0,s=d.reduce((e,n)=>(e[n]=function(e,t){let n=new Set,r=new Set,i=!1,a=!1,s=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){s.has(t)&&(c.schedule(t),e()),l++,t(o)}let c={schedule:(e,t=!1,a=!1)=>{let o=a&&i?n:r;return t&&s.add(e),o.has(e)||o.add(e),e},cancel:e=>{r.delete(e),s.delete(e)},process:e=>{if(o=e,i){a=!0;return}i=!0,[n,r]=[r,n],n.forEach(u),t&&h.value&&h.value.frameloop[t].push(l),l=0,n.clear(),i=!1,a&&(a=!1,c.process(e))}};return c}(a,t?n:void 0),e),{}),{setup:o,read:l,resolveKeyframes:u,preUpdate:p,update:f,preRender:m,render:g,postRender:y}=s,v=()=>{let a=c.useManualTiming?i.timestamp:performance.now();n=!1,c.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(a-i.timestamp,40),1)),i.timestamp=a,i.isProcessing=!0,o.process(i),l.process(i),u.process(i),p.process(i),f.process(i),m.process(i),g.process(i),y.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(v))},x=()=>{n=!0,r=!0,i.isProcessing||e(v)};return{schedule:d.reduce((e,t)=>{let r=s[t];return e[t]=(e,t=!1,i=!1)=>(n||x(),r.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<d.length;t++)s[d[t]].cancel(e)},state:i,steps:s}}let{schedule:f,cancel:m,state:g,steps:y}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],x=new Set(v),b=new Set(["width","height","top","left","right","bottom",...v]);function w(e,t){-1===e.indexOf(t)&&e.push(t)}function j(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}class P{constructor(){this.subscriptions=[]}add(e){return w(this.subscriptions,e),()=>j(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function T(){r=void 0}let E={now:()=>(void 0===r&&E.set(g.isProcessing||c.useManualTiming?g.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(T)}},R=e=>!isNaN(parseFloat(e)),A={current:void 0};class N{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=E.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=E.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=R(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new P);let n=this.events[e].add(t);return"change"===e?()=>{n(),f.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return A.current&&A.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=E.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function S(e,t){return new N(e,t)}let _=e=>Array.isArray(e),M=e=>!!(e&&e.getVelocity);function C(e,t){let n=e.getValue("willChange");if(M(n)&&n.add)return n.add(t);if(!n&&c.WillChange){let n=new c.WillChange("auto");e.addValue("willChange",n),n.add(t)}}let O=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),k="data-"+O("framerAppearId"),D=(e,t)=>n=>t(e(n)),I=(...e)=>e.reduce(D),L=(e,t,n)=>n>t?t:n<e?e:n,U=e=>1e3*e,V=e=>e/1e3,F={layout:0,mainThread:0,waapi:0},B=()=>{},H=()=>{},z=e=>t=>"string"==typeof t&&t.startsWith(e),$=z("--"),W=z("var(--"),K=e=>!!W(e)&&q.test(e.split("/*")[0].trim()),q=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,G={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},Y={...G,transform:e=>L(0,1,e)},X={...G,default:1},Z=e=>Math.round(1e5*e)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ee=(e,t)=>n=>!!("string"==typeof n&&J.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),et=(e,t,n)=>r=>{if("string"!=typeof r)return r;let[i,a,s,o]=r.match(Q);return{[e]:parseFloat(i),[t]:parseFloat(a),[n]:parseFloat(s),alpha:void 0!==o?parseFloat(o):1}},en=e=>L(0,255,e),er={...G,transform:e=>Math.round(en(e))},ei={test:ee("rgb","red"),parse:et("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+er.transform(e)+", "+er.transform(t)+", "+er.transform(n)+", "+Z(Y.transform(r))+")"},ea={test:ee("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:ei.transform},es=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),eo=es("deg"),el=es("%"),eu=es("px"),ec=es("vh"),ed=es("vw"),eh={...el,parse:e=>el.parse(e)/100,transform:e=>el.transform(100*e)},ep={test:ee("hsl","hue"),parse:et("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+el.transform(Z(t))+", "+el.transform(Z(n))+", "+Z(Y.transform(r))+")"},ef={test:e=>ei.test(e)||ea.test(e)||ep.test(e),parse:e=>ei.test(e)?ei.parse(e):ep.test(e)?ep.parse(e):ea.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?ei.transform(e):ep.transform(e),getAnimatableNone:e=>{let t=ef.parse(e);return t.alpha=0,ef.transform(t)}},em=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eg="number",ey="color",ev=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ex(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],a=0,s=t.replace(ev,e=>(ef.test(e)?(r.color.push(a),i.push(ey),n.push(ef.parse(e))):e.startsWith("var(")?(r.var.push(a),i.push("var"),n.push(e)):(r.number.push(a),i.push(eg),n.push(parseFloat(e))),++a,"${}")).split("${}");return{values:n,split:s,indexes:r,types:i}}function eb(e){return ex(e).values}function ew(e){let{split:t,types:n}=ex(e),r=t.length;return e=>{let i="";for(let a=0;a<r;a++)if(i+=t[a],void 0!==e[a]){let t=n[a];t===eg?i+=Z(e[a]):t===ey?i+=ef.transform(e[a]):i+=e[a]}return i}}let ej=e=>"number"==typeof e?0:ef.test(e)?ef.getAnimatableNone(e):e,eP={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(Q)?.length||0)+(e.match(em)?.length||0)>0},parse:eb,createTransformer:ew,getAnimatableNone:function(e){let t=eb(e);return ew(e)(t.map(ej))}};function eT(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function eE(e,t){return n=>n>0?t:e}let eR=(e,t,n)=>e+(t-e)*n,eA=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},eN=[ea,ei,ep],eS=e=>eN.find(t=>t.test(e));function e_(e){let t=eS(e);if(B(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let n=t.parse(e);return t===ep&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,a=0,s=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,o=2*n-r;i=eT(o,r,e+1/3),a=eT(o,r,e),s=eT(o,r,e-1/3)}else i=a=s=n;return{red:Math.round(255*i),green:Math.round(255*a),blue:Math.round(255*s),alpha:r}}(n)),n}let eM=(e,t)=>{let n=e_(e),r=e_(t);if(!n||!r)return eE(e,t);let i={...n};return e=>(i.red=eA(n.red,r.red,e),i.green=eA(n.green,r.green,e),i.blue=eA(n.blue,r.blue,e),i.alpha=eR(n.alpha,r.alpha,e),ei.transform(i))},eC=new Set(["none","hidden"]);function eO(e,t){return n=>eR(e,t,n)}function ek(e){return"number"==typeof e?eO:"string"==typeof e?K(e)?eE:ef.test(e)?eM:eL:Array.isArray(e)?eD:"object"==typeof e?ef.test(e)?eM:eI:eE}function eD(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>ek(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function eI(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=ek(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let eL=(e,t)=>{let n=eP.createTransformer(t),r=ex(e),i=ex(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?eC.has(e)&&!i.values.length||eC.has(t)&&!r.values.length?function(e,t){return eC.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):I(eD(function(e,t){let n=[],r={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let a=t.types[i],s=e.indexes[a][r[a]],o=e.values[s]??0;n[i]=o,r[a]++}return n}(r,i),i.values),n):(B(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eE(e,t))};function eU(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?eR(e,t,n):ek(e)(e,t)}let eV=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>f.update(t,e),stop:()=>m(t),now:()=>g.isProcessing?g.timestamp:E.now()}},eF=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=Math.round(1e4*e(t/(i-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function eB(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function eH(e,t,n){var r,i;let a=Math.max(t-5,0);return r=n-e(a),(i=t-a)?1e3/i*r:0}let ez={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function e$(e,t){return e*Math.sqrt(1-t*t)}let eW=["duration","bounce"],eK=["stiffness","damping","mass"];function eq(e,t){return t.some(t=>void 0!==e[t])}function eG(e=ez.visualDuration,t=ez.bounce){let n,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:a}=r,s=r.keyframes[0],o=r.keyframes[r.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:c,mass:d,duration:h,velocity:p,isResolvedFromDuration:f}=function(e){let t={velocity:ez.velocity,stiffness:ez.stiffness,damping:ez.damping,mass:ez.mass,isResolvedFromDuration:!1,...e};if(!eq(e,eK)&&eq(e,eW))if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*L(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:ez.mass,stiffness:r,damping:i}}else{let n=function({duration:e=ez.duration,bounce:t=ez.bounce,velocity:n=ez.velocity,mass:r=ez.mass}){let i,a;B(e<=U(ez.maxDuration),"Spring duration must be 10 seconds or less");let s=1-t;s=L(ez.minDamping,ez.maxDamping,s),e=L(ez.minDuration,ez.maxDuration,V(e)),s<1?(i=t=>{let r=t*s,i=r*e;return .001-(r-n)/e$(t,s)*Math.exp(-i)},a=t=>{let r=t*s*e,a=Math.pow(s,2)*Math.pow(t,2)*e,o=Math.exp(-r),l=e$(Math.pow(t,2),s);return(r*n+n-a)*o*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),a=t=>e*e*(n-t)*Math.exp(-t*e));let o=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,a,5/e);if(e=U(e),isNaN(o))return{stiffness:ez.stiffness,damping:ez.damping,duration:e};{let t=Math.pow(o,2)*r;return{stiffness:t,damping:2*s*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:ez.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-V(r.velocity||0)}),m=p||0,g=c/(2*Math.sqrt(u*d)),y=o-s,v=V(Math.sqrt(u/d)),x=5>Math.abs(y);if(i||(i=x?ez.restSpeed.granular:ez.restSpeed.default),a||(a=x?ez.restDelta.granular:ez.restDelta.default),g<1){let e=e$(v,g);n=t=>o-Math.exp(-g*v*t)*((m+g*v*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===g)n=e=>o-Math.exp(-v*e)*(y+(m+v*y)*e);else{let e=v*Math.sqrt(g*g-1);n=t=>{let n=Math.exp(-g*v*t),r=Math.min(e*t,300);return o-n*((m+g*v*y)*Math.sinh(r)+e*y*Math.cosh(r))/e}}let b={calculatedDuration:f&&h||null,next:e=>{let t=n(e);if(f)l.done=e>=h;else{let r=0===e?m:0;g<1&&(r=0===e?U(m):eH(n,e,t));let s=Math.abs(o-t)<=a;l.done=Math.abs(r)<=i&&s}return l.value=l.done?o:t,l},toString:()=>{let e=Math.min(eB(b),2e4),t=eF(t=>b.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return b}function eY({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:a=500,modifyTarget:s,min:o,max:l,restDelta:u=.5,restSpeed:c}){let d,h,p=e[0],f={done:!1,value:p},m=e=>void 0!==o&&e<o||void 0!==l&&e>l,g=e=>void 0===o?l:void 0===l||Math.abs(o-e)<Math.abs(l-e)?o:l,y=n*t,v=p+y,x=void 0===s?v:s(v);x!==v&&(y=x-p);let b=e=>-y*Math.exp(-e/r),w=e=>x+b(e),j=e=>{let t=b(e),n=w(e);f.done=Math.abs(t)<=u,f.value=f.done?x:n},P=e=>{m(f.value)&&(d=e,h=eG({keyframes:[f.value,g(f.value)],velocity:eH(w,e,f.value),damping:i,stiffness:a,restDelta:u,restSpeed:c}))};return P(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,j(e),P(e)),void 0!==d&&e>=d)?h.next(e-d):(t||j(e),f)}}}eG.applyToOptions=e=>{let t=function(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min(eB(r),2e4);return{type:"keyframes",ease:e=>r.next(i*e).value/t,duration:V(i)}}(e,100,eG);return e.ease=t.ease,e.duration=U(t.duration),e.type="keyframes",e};let eX=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function eZ(e,t,n,r){if(e===t&&n===r)return u;let i=t=>(function(e,t,n,r,i){let a,s,o=0;do(a=eX(s=t+(n-t)/2,r,i)-e)>0?n=s:t=s;while(Math.abs(a)>1e-7&&++o<12);return s})(t,0,1,e,n);return e=>0===e||1===e?e:eX(i(e),t,r)}let eQ=eZ(.42,0,1,1),eJ=eZ(0,0,.58,1),e0=eZ(.42,0,.58,1),e1=e=>Array.isArray(e)&&"number"!=typeof e[0],e2=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e3=e=>t=>1-e(1-t),e4=eZ(.33,1.53,.69,.99),e5=e3(e4),e6=e2(e5),e8=e=>(e*=2)<1?.5*e5(e):.5*(2-Math.pow(2,-10*(e-1))),e9=e=>1-Math.sin(Math.acos(e)),e7=e3(e9),te=e2(e9),tt=e=>Array.isArray(e)&&"number"==typeof e[0],tn={linear:u,easeIn:eQ,easeInOut:e0,easeOut:eJ,circIn:e9,circInOut:te,circOut:e7,backIn:e5,backInOut:e6,backOut:e4,anticipate:e8},tr=e=>"string"==typeof e,ti=e=>{if(tt(e)){H(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,n,r,i]=e;return eZ(t,n,r,i)}return tr(e)?(H(void 0!==tn[e],`Invalid easing type '${e}'`),tn[e]):e},ta=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r};function ts({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){var i;let a=e1(r)?r.map(ti):ti(r),s={done:!1,value:t[0]},o=function(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let a=e.length;if(H(a===t.length,"Both input and output ranges must be the same length"),1===a)return()=>t[0];if(2===a&&t[0]===t[1])return()=>t[1];let s=e[0]===e[1];e[0]>e[a-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,n){let r=[],i=n||c.mix||eU,a=e.length-1;for(let n=0;n<a;n++){let a=i(e[n],e[n+1]);t&&(a=I(Array.isArray(t)?t[n]||u:t,a)),r.push(a)}return r}(t,r,i),l=o.length,d=n=>{if(s&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=ta(e[r],e[r+1],n);return o[r](i)};return n?t=>d(L(e[0],e[a-1],t)):d}((i=n&&n.length===t.length?n:function(e){let t=[0];return!function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=ta(0,t,r);e.push(eR(n,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(a)?a:t.map(()=>a||e0).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=o(t),s.done=t>=e,s)}}let to=e=>null!==e;function tl(e,{repeat:t,repeatType:n="loop"},r,i=1){let a=e.filter(to),s=i<0||t&&"loop"!==n&&t%2==1?0:a.length-1;return s&&void 0!==r?r:a[s]}let tu={decay:eY,inertia:eY,tween:ts,keyframes:ts,spring:eG};function tc(e){"string"==typeof e.type&&(e.type=tu[e.type])}class td{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let th=e=>e/100;class tp extends td{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==E.now()&&this.tick(E.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},F.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tc(e);let{type:t=ts,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:a=0}=e,{keyframes:s}=e,o=t||ts;o!==ts&&"number"!=typeof s[0]&&(this.mixKeyframes=I(th,eU(s[0],s[1])),s=[0,100]);let l=o({...e,keyframes:s});"mirror"===i&&(this.mirroredGenerator=o({...e,keyframes:[...s].reverse(),velocity:-a})),null===l.calculatedDuration&&(l.calculatedDuration=eB(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:a,resolvedDuration:s,calculatedDuration:o}=this;if(null===this.startTime)return n.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:h,type:p,onUpdate:f,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,x=n;if(c){let e=Math.min(this.currentTime,r)/s,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,c+1))%2&&("reverse"===d?(n=1-n,h&&(n-=h/s)):"mirror"===d&&(x=a)),v=L(0,1,n)*s}let b=y?{done:!1,value:u[0]}:x.next(v);i&&(b.value=i(b.value));let{done:w}=b;y||null===o||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let j=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return j&&p!==eY&&(b.value=tl(u,this.options,m,this.speed)),f&&f(b.value),j&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return V(this.calculatedDuration)}get time(){return V(this.currentTime)}set time(e){e=U(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(E.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=V(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eV,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=t??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(E.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,F.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tf=e=>180*e/Math.PI,tm=e=>ty(tf(Math.atan2(e[1],e[0]))),tg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tm,rotateZ:tm,skewX:e=>tf(Math.atan(e[1])),skewY:e=>tf(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},ty=e=>((e%=360)<0&&(e+=360),e),tv=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tx=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tb={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tv,scaleY:tx,scale:e=>(tv(e)+tx(e))/2,rotateX:e=>ty(tf(Math.atan2(e[6],e[5]))),rotateY:e=>ty(tf(Math.atan2(-e[2],e[0]))),rotateZ:tm,rotate:tm,skewX:e=>tf(Math.atan(e[4])),skewY:e=>tf(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tw(e){return+!!e.includes("scale")}function tj(e,t){let n,r;if(!e||"none"===e)return tw(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)n=tb,r=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=tg,r=t}if(!r)return tw(t);let a=n[t],s=r[1].split(",").map(tT);return"function"==typeof a?a(s):s[a]}let tP=(e,t)=>{let{transform:n="none"}=getComputedStyle(e);return tj(n,t)};function tT(e){return parseFloat(e.trim())}let tE=e=>e===G||e===eu,tR=new Set(["x","y","z"]),tA=v.filter(e=>!tR.has(e)),tN={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tj(t,"x"),y:(e,{transform:t})=>tj(t,"y")};tN.translateX=tN.x,tN.translateY=tN.y;let tS=new Set,t_=!1,tM=!1,tC=!1;function tO(){if(tM){let e=Array.from(tS).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return tA.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{e.getValue(t)?.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tM=!1,t_=!1,tS.forEach(e=>e.complete(tC)),tS.clear()}function tk(){tS.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tM=!0)})}class tD{constructor(e,t,n,r,i,a=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=a}scheduleResolve(){this.state="scheduled",this.isAsync?(tS.add(this),t_||(t_=!0,f.read(tk),f.resolveKeyframes(tO))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(null===e[0]){let i=r?.get(),a=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,a);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=a),r&&void 0===i&&r.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tS.delete(this)}cancel(){"scheduled"===this.state&&(tS.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tI=e=>e.startsWith("--");function tL(e){let t;return()=>(void 0===t&&(t=e()),t)}let tU=tL(()=>void 0!==window.ScrollTimeline),tV={},tF=function(e,t){let n=tL(e);return()=>tV[t]??n()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tB=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,tH={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tB([0,.65,.55,1]),circOut:tB([.55,0,1,.45]),backIn:tB([.31,.01,.66,-.59]),backOut:tB([.33,1.53,.69,.99])};function tz(e){return"function"==typeof e&&"applyToOptions"in e}class t$ extends td{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:a=!1,finalKeyframe:s,onComplete:o}=e;this.isPseudoElement=!!i,this.allowFlatten=a,this.options=e,H("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return tz(e)&&tF()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,n,{delay:r=0,duration:i=300,repeat:a=0,repeatType:s="loop",ease:o="easeOut",times:l}={},u){let c={[t]:n};l&&(c.offset=l);let d=function e(t,n){if(t)return"function"==typeof t?tF()?eF(t,n):"ease-out":tt(t)?tB(t):Array.isArray(t)?t.map(t=>e(t,n)||tH.easeOut):tH[t]}(o,i);Array.isArray(d)&&(c.easing=d),h.value&&F.waapi++;let p={delay:r,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:a+1,direction:"reverse"===s?"alternate":"normal"};u&&(p.pseudoElement=u);let f=e.animate(c,p);return h.value&&f.finished.finally(()=>{F.waapi--}),f}(t,n,r,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=tl(r,this.options,s,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,n){tI(t)?e.style.setProperty(t,n):e.style[t]=n}(t,n,e),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return V(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return V(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=U(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tU())?(this.animation.timeline=e,u):t(this)}}let tW={anticipate:e8,backInOut:e6,circInOut:te};class tK extends t${constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tW&&(e.ease=tW[e.ease])}(e),tc(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:n,onComplete:r,element:i,...a}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let s=new tp({...a,autoplay:!1}),o=U(this.finishedTime??this.time);t.setWithVelocity(s.sample(o-10).value,s.sample(o).value,10),s.stop()}}let tq=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eP.test(e)||"0"===e)&&!e.startsWith("url("));var tG,tY,tX=n(8171);let tZ=new Set(["opacity","clipPath","filter","transform"]),tQ=tL(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tJ extends td{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:a="loop",keyframes:s,name:o,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=E.now();let d={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:a,name:o,motionValue:l,element:u,...c},h=u?.KeyframeResolver||tD;this.keyframeResolver=new h(s,(e,t,n)=>this.onKeyframesResolved(e,t,d,!n),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;let{name:i,type:a,velocity:s,delay:o,isHandoff:l,onUpdate:d}=n;this.resolvedAt=E.now(),!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let a=e[e.length-1],s=tq(i,t),o=tq(a,t);return B(s===o,`You are trying to animate ${t} from "${i}" to "${a}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${a} via the \`style\` property.`),!!s&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||tz(n))&&r)}(e,i,a,s)&&((c.instantAnimations||!o)&&d?.(tl(e,n,t)),e[0]=e[e.length-1],n.duration=0,n.repeat=0);let h={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...n,keyframes:e},p=!l&&function(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:a,type:s}=e;if(!(0,tX.s)(t?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=t.owner.getProps();return tQ()&&n&&tZ.has(n)&&("transform"!==n||!l)&&!o&&!r&&"mirror"!==i&&0!==a&&"inertia"!==s}(h)?new tK({...h,element:h.motionValue.owner.current}):new tp(h);p.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tC=!0,tk(),tO(),tC=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t0=e=>null!==e,t1={type:"spring",stiffness:500,damping:25,restSpeed:10},t2=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t3={type:"keyframes",duration:.8},t4={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t5=(e,{keyframes:t})=>t.length>2?t3:x.has(e)?e.startsWith("scale")?t2(t[1]):t1:t4,t6=(e,t,n,r={},i,a)=>s=>{let o=l(r,e)||{},u=o.delay||r.delay||0,{elapsed:d=0}=r;d-=U(u);let h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-d,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{s(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:a?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:a,repeatType:s,repeatDelay:o,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(o)&&Object.assign(h,t5(e,h)),h.duration&&(h.duration=U(h.duration)),h.repeatDelay&&(h.repeatDelay=U(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let p=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(p=!0)),(c.instantAnimations||c.skipAnimations)&&(p=!0,h.duration=0,h.delay=0),h.allowFlatten=!o.type&&!o.ease,p&&!a&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(t0),a=t&&"loop"!==n&&t%2==1?0:i.length-1;return i[a]}(h.keyframes,o);if(void 0!==e)return void f.update(()=>{h.onUpdate(e),h.onComplete()})}return o.isSync?new tp(h):new tJ(h)};function t8(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:a=e.getDefaultTransition(),transitionEnd:s,...u}=t;r&&(a=r);let c=[],d=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let r=e.getValue(t,e.latestValues[t]??null),i=u[t];if(void 0===i||d&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(d,t))continue;let s={delay:n,...l(a||{},t)},o=r.get();if(void 0!==o&&!r.isAnimating&&!Array.isArray(i)&&i===o&&!s.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let n=e.props[k];if(n){let e=window.MotionHandoffAnimation(n,t,f);null!==e&&(s.startTime=e,h=!0)}}C(e,t),r.start(t6(t,r,i,e.shouldReduceMotion&&b.has(t)?{type:!1}:s,e,h));let p=r.animation;p&&c.push(p)}return s&&Promise.all(c).then(()=>{f.update(()=>{s&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=o(e,t)||{};for(let t in i={...i,...n}){var a;let n=_(a=i[t])?a[a.length-1]||0:a;e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,S(n))}}(e,s)})}),c}function t9(e,t,n={}){let r=o(e,t,"exit"===n.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);let a=r?()=>Promise.all(t8(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:a=0,staggerChildren:s,staggerDirection:o}=i;return function(e,t,n=0,r=0,i=1,a){let s=[],o=(e.variantChildren.size-1)*r,l=1===i?(e=0)=>e*r:(e=0)=>o-e*r;return Array.from(e.variantChildren).sort(t7).forEach((e,r)=>{e.notify("AnimationStart",t),s.push(t9(e,t,{...a,delay:n+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(s)}(e,t,a+r,s,o,n)}:()=>Promise.resolve(),{when:l}=i;if(!l)return Promise.all([a(),s(n.delay)]);{let[e,t]="beforeChildren"===l?[a,s]:[s,a];return e().then(()=>t())}}function t7(e,t){return e.sortNodePosition(t)}function ne(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function nt(e){return"string"==typeof e||Array.isArray(e)}let nn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],nr=["initial",...nn],ni=nr.length,na=[...nn].reverse(),ns=nn.length;function no(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nl(){return{animate:no(!0),whileInView:no(),whileHover:no(),whileTap:no(),whileDrag:no(),whileFocus:no(),exit:no()}}class nu{constructor(e){this.isMounted=!1,this.node=e}update(){}}class nc extends nu{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>t9(e,t,n)));else if("string"==typeof t)r=t9(e,t,n);else{let i="function"==typeof t?o(e,t,n.custom):t;r=Promise.all(t8(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=nl(),r=!0,a=t=>(n,r)=>{let i=o(e,r,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...r}=i;n={...n,...r,...t}}return n};function s(s){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<ni;e++){let r=nr[e],i=t.props[r];(nt(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},c=[],d=new Set,h={},p=1/0;for(let t=0;t<ns;t++){var f,m;let o=na[t],g=n[o],y=void 0!==l[o]?l[o]:u[o],v=nt(y),x=o===s?g.isActive:null;!1===x&&(p=t);let b=y===u[o]&&y!==l[o]&&v;if(b&&r&&e.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...h},!g.isActive&&null===x||!y&&!g.prevProp||i(y)||"boolean"==typeof y)continue;let w=(f=g.prevProp,"string"==typeof(m=y)?m!==f:!!Array.isArray(m)&&!ne(m,f)),j=w||o===s&&g.isActive&&!b&&v||t>p&&v,P=!1,T=Array.isArray(y)?y:[y],E=T.reduce(a(o),{});!1===x&&(E={});let{prevResolvedValues:R={}}=g,A={...R,...E},N=t=>{j=!0,d.has(t)&&(P=!0,d.delete(t)),g.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in A){let t=E[e],n=R[e];if(h.hasOwnProperty(e))continue;let r=!1;(_(t)&&_(n)?ne(t,n):t===n)?void 0!==t&&d.has(e)?N(e):g.protectedKeys[e]=!0:null!=t?N(e):d.add(e)}g.prevProp=y,g.prevResolvedValues=E,g.isActive&&(h={...h,...E}),r&&e.blockInitialAnimation&&(j=!1);let S=!(b&&w)||P;j&&S&&c.push(...T.map(e=>({animation:e,options:{type:o}})))}if(d.size){let t={};if("boolean"!=typeof l.initial){let n=o(e,Array.isArray(l.initial)?l.initial[0]:l.initial);n&&n.transition&&(t.transition=n.transition)}d.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=r??null}),c.push({animation:t})}let g=!!c.length;return r&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(g=!1),r=!1,g?t(c):Promise.resolve()}return{animateChanges:s,setActive:function(t,r){if(n[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),n[t].isActive=r;let i=s(t);for(let e in n)n[e].protectedKeys={};return i},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=nl(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let nd=0;class nh extends nu{constructor(){super(...arguments),this.id=nd++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let np={x:!1,y:!1};function nf(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}let nm=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function ng(e){return{point:{x:e.pageX,y:e.pageY}}}let ny=e=>t=>nm(t)&&e(t,ng(t));function nv(e,t,n,r){return nf(e,t,ny(n),r)}function nx({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function nb(e){return e.max-e.min}function nw(e,t,n,r=.5){e.origin=r,e.originPoint=eR(t.min,t.max,e.origin),e.scale=nb(n)/nb(t),e.translate=eR(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function nj(e,t,n,r){nw(e.x,t.x,n.x,r?r.originX:void 0),nw(e.y,t.y,n.y,r?r.originY:void 0)}function nP(e,t,n){e.min=n.min+t.min,e.max=e.min+nb(t)}function nT(e,t,n){e.min=t.min-n.min,e.max=e.min+nb(t)}function nE(e,t,n){nT(e.x,t.x,n.x),nT(e.y,t.y,n.y)}let nR=()=>({translate:0,scale:1,origin:0,originPoint:0}),nA=()=>({x:nR(),y:nR()}),nN=()=>({min:0,max:0}),nS=()=>({x:nN(),y:nN()});function n_(e){return[e("x"),e("y")]}function nM(e){return void 0===e||1===e}function nC({scale:e,scaleX:t,scaleY:n}){return!nM(e)||!nM(t)||!nM(n)}function nO(e){return nC(e)||nk(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function nk(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function nD(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function nI(e,t=0,n=1,r,i){e.min=nD(e.min,t,n,r,i),e.max=nD(e.max,t,n,r,i)}function nL(e,{x:t,y:n}){nI(e.x,t.translate,t.scale,t.originPoint),nI(e.y,n.translate,n.scale,n.originPoint)}function nU(e,t){e.min=e.min+t,e.max=e.max+t}function nV(e,t,n,r,i=.5){let a=eR(e.min,e.max,i);nI(e,t,n,a,r)}function nF(e,t){nV(e.x,t.x,t.scaleX,t.scale,t.originX),nV(e.y,t.y,t.scaleY,t.scale,t.originY)}function nB(e,t){return nx(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let nH=({current:e})=>e?e.ownerDocument.defaultView:null;function nz(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let n$=(e,t)=>Math.abs(e-t);class nW{constructor(e,t,{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=nG(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(n$(e.x,t.x)**2+n$(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=g;this.history.push({...r,timestamp:i});let{onStart:a,onMove:s}=this.handlers;t||(a&&a(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=nK(t,this.transformPagePoint),f.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=nG("pointercancel"===e.type?this.lastMoveEventInfo:nK(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,a),r&&r(e,a)},!nm(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;let a=nK(ng(e),this.transformPagePoint),{point:s}=a,{timestamp:o}=g;this.history=[{...s,timestamp:o}];let{onSessionStart:l}=t;l&&l(e,nG(a,this.history)),this.removeListeners=I(nv(this.contextWindow,"pointermove",this.handlePointerMove),nv(this.contextWindow,"pointerup",this.handlePointerUp),nv(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function nK(e,t){return t?{point:t(e.point)}:e}function nq(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nG({point:e},t){return{point:e,delta:nq(e,nY(t)),offset:nq(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=nY(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>U(.1)));)n--;if(!r)return{x:0,y:0};let a=V(i.timestamp-r.timestamp);if(0===a)return{x:0,y:0};let s={x:(i.x-r.x)/a,y:(i.y-r.y)/a};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,.1)}}function nY(e){return e[e.length-1]}function nX(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function nZ(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function nQ(e,t,n){return{min:nJ(e,t),max:nJ(e,n)}}function nJ(e,t){return"number"==typeof e?e:e[t]||0}let n0=new WeakMap;class n1{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=nS(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new nW(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(ng(e).point)},onStart:(e,t)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(np[e])return null;else return np[e]=!0,()=>{np[e]=!1};return np.x||np.y?null:(np.x=np.y=!0,()=>{np.x=np.y=!1})}(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),n_(e=>{let t=this.getAxisMotionValue(e).get()||0;if(el.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=nb(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&f.postRender(()=>i(e,t)),C(this.visualElement,"transform");let{animationState:a}=this.visualElement;a&&a.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:a}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:s}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),a&&a(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>n_(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:nH(this.visualElement)})}stop(e,t){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:i}=this.getProps();i&&f.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!n2(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),a=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(a=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?eR(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?eR(n,e,r.max):Math.min(e,n)),e}(a,this.constraints[e],this.elastic[e])),i.set(a)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&nz(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:nX(e.x,n,i),y:nX(e.y,t,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:nQ(e,"left","right"),y:nQ(e,"top","bottom")}}(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&n_(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!nz(t))return!1;let r=t.current;H(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let a=function(e,t,n){let r=nB(e,n),{scroll:i}=t;return i&&(nU(r.x,i.offset.x),nU(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),s=(e=i.layout.layoutBox,{x:nZ(e.x,a.x),y:nZ(e.y,a.y)});if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=nx(e))}return s}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:a,onDragTransitionEnd:s}=this.getProps(),o=this.constraints||{};return Promise.all(n_(s=>{if(!n2(s,t,this.currentDirection))return;let l=o&&o[s]||{};a&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[s]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return C(this.visualElement,e),n.start(t6(e,n,0,t,this.visualElement,!1))}stopAnimation(){n_(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){n_(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){n_(t=>{let{drag:n}=this.getProps();if(!n2(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:a}=r.layout.layoutBox[t];i.set(e[t]-eR(n,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!nz(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};n_(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=nb(e),i=nb(t);return i>r?n=ta(t.min,t.max-r,e.min):r>i&&(n=ta(e.min,e.max-i,t.min)),L(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),n_(t=>{if(!n2(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:a}=this.constraints[t];n.set(eR(i,a,r[t]))})}addListeners(){if(!this.visualElement.current)return;n0.set(this.visualElement,this);let e=nv(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();nz(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),f.read(t);let i=nf(window,"resize",()=>this.scalePositionWithinConstraints()),a=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(n_(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),a&&a()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:a=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:a,dragMomentum:s}}}function n2(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class n3 extends nu{constructor(e){super(e),this.removeGroupControls=u,this.removeListeners=u,this.controls=new n1(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let n4=e=>(t,n)=>{e&&f.postRender(()=>e(t,n))};class n5 extends nu{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(e){this.session=new nW(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nH(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:n4(e),onStart:n4(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&f.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=nv(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var n6=n(687);let{schedule:n8}=p(queueMicrotask,!1);var n9=n(3210),n7=n(6044),re=n(2157);let rt=(0,n9.createContext)({}),rn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rr(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ri={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eu.test(e))return e;else e=parseFloat(e);let n=rr(e,t.target.x),r=rr(e,t.target.y);return`${n}% ${r}%`}},ra={};class rs extends n9.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;for(let e in rl)ra[e]=rl[e],$(e)&&(ra[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),rn.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:a}=n;return a&&(a.isPresent=i,r||e.layoutDependency!==t||void 0===t||e.isPresent!==i?a.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?a.promote():a.relegate()||f.postRender(()=>{let e=a.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),n8.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function ro(e){let[t,n]=(0,n7.xQ)(),r=(0,n9.useContext)(re.L);return(0,n6.jsx)(rs,{...e,layoutGroup:r,switchLayoutGroup:(0,n9.useContext)(rt),isPresent:t,safeToRemove:n})}let rl={borderRadius:{...ri,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ri,borderTopRightRadius:ri,borderBottomLeftRadius:ri,borderBottomRightRadius:ri,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=eP.parse(e);if(r.length>5)return e;let i=eP.createTransformer(e),a=+("number"!=typeof r[0]),s=n.x.scale*t.x,o=n.y.scale*t.y;r[0+a]/=s,r[1+a]/=o;let l=eR(s,o,.5);return"number"==typeof r[2+a]&&(r[2+a]/=l),"number"==typeof r[3+a]&&(r[3+a]/=l),i(r)}}};var ru=n(4479);function rc(e){return(0,ru.G)(e)&&"ownerSVGElement"in e}let rd=(e,t)=>e.depth-t.depth;class rh{constructor(){this.children=[],this.isDirty=!1}add(e){w(this.children,e),this.isDirty=!0}remove(e){j(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rd),this.isDirty=!1,this.children.forEach(e)}}function rp(e){return M(e)?e.get():e}let rf=["TopLeft","TopRight","BottomLeft","BottomRight"],rm=rf.length,rg=e=>"string"==typeof e?parseFloat(e):e,ry=e=>"number"==typeof e||eu.test(e);function rv(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rx=rw(0,.5,e7),rb=rw(.5,.95,u);function rw(e,t,n){return r=>r<e?0:r>t?1:n(ta(e,t,r))}function rj(e,t){e.min=t.min,e.max=t.max}function rP(e,t){rj(e.x,t.x),rj(e.y,t.y)}function rT(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rE(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function rR(e,t,[n,r,i],a,s){!function(e,t=0,n=1,r=.5,i,a=e,s=e){if(el.test(t)&&(t=parseFloat(t),t=eR(s.min,s.max,t/100)-s.min),"number"!=typeof t)return;let o=eR(a.min,a.max,r);e===a&&(o-=t),e.min=rE(e.min,t,n,o,i),e.max=rE(e.max,t,n,o,i)}(e,t[n],t[r],t[i],t.scale,a,s)}let rA=["x","scaleX","originX"],rN=["y","scaleY","originY"];function rS(e,t,n,r){rR(e.x,t,rA,n?n.x:void 0,r?r.x:void 0),rR(e.y,t,rN,n?n.y:void 0,r?r.y:void 0)}function r_(e){return 0===e.translate&&1===e.scale}function rM(e){return r_(e.x)&&r_(e.y)}function rC(e,t){return e.min===t.min&&e.max===t.max}function rO(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rk(e,t){return rO(e.x,t.x)&&rO(e.y,t.y)}function rD(e){return nb(e.x)/nb(e.y)}function rI(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rL{constructor(){this.members=[]}add(e){w(this.members,e),e.scheduleRender()}remove(e){if(j(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rU={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rV=["","X","Y","Z"],rF={visibility:"hidden"},rB=0;function rH(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function rz({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=t?.()){this.id=rB++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,h.value&&(rU.nodes=rU.calculatedTargetDeltas=rU.calculatedProjections=0),this.nodes.forEach(rK),this.nodes.forEach(rJ),this.nodes.forEach(r0),this.nodes.forEach(rq),h.addProjectionMetrics&&h.addProjectionMetrics(rU)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rh)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new P),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=rc(t)&&!(rc(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:n,layout:r,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||n)&&(this.isLayoutDirty=!0),e){let n,r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=E.now(),r=({timestamp:i})=>{let a=i-n;a>=250&&(m(r),e(a-t))};return f.setup(r,!0),()=>m(r)}(r,250),rn.hasAnimatedSinceResize&&(rn.hasAnimatedSinceResize=!1,this.nodes.forEach(rQ))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&i&&(n||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let a=this.options.transition||i.getDefaultTransition()||r6,{onLayoutAnimationStart:s,onLayoutAnimationComplete:o}=i.getProps(),u=!this.targetLayout||!rk(this.targetLayout,r),c=!t&&n;if(this.options.layoutRoot||this.resumeFrom||c||t&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(a,"layout"),onPlay:s,onComplete:o};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,c)}else t||rQ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r1),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[k];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",f,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rY);return}this.isUpdating||this.nodes.forEach(rX),this.isUpdating=!1,this.nodes.forEach(rZ),this.nodes.forEach(r$),this.nodes.forEach(rW),this.clearAllSnapshots();let e=E.now();g.delta=L(0,1e3/60,e-g.timestamp),g.timestamp=e,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,n8.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rG),this.sharedNodes.forEach(r2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,f.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){f.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||nb(this.snapshot.measuredBox.x)||nb(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=nS(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rM(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,a=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||nO(this.latestValues)||a)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),r7((t=r).x),r7(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return nS();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(it))){let{scroll:e}=this.root;e&&(nU(t.x,e.offset.x),nU(t.y,e.offset.y))}return t}removeElementScroll(e){let t=nS();if(rP(t,e),this.scroll?.wasRoot)return t;for(let n=0;n<this.path.length;n++){let r=this.path[n],{scroll:i,options:a}=r;r!==this.root&&i&&a.layoutScroll&&(i.wasRoot&&rP(t,e),nU(t.x,i.offset.x),nU(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let n=nS();rP(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&nF(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),nO(r.latestValues)&&nF(n,r.latestValues)}return nO(this.latestValues)&&nF(n,this.latestValues),n}removeTransform(e){let t=nS();rP(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!nO(n.latestValues))continue;nC(n.latestValues)&&n.updateSnapshot();let r=nS();rP(r,n.measurePageBox()),rS(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return nO(this.latestValues)&&rS(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let n=!!this.resumingFrom||this!==t;if(!(e||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:i}=this.options;if(this.layout&&(r||i)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nS(),this.relativeTargetOrigin=nS(),nE(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rP(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=nS(),this.targetWithTransforms=nS()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var a,s,o;this.forceRelativeParentToResolveTarget(),a=this.target,s=this.relativeTarget,o=this.relativeParent.target,nP(a.x,s.x,o.x),nP(a.y,s.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rP(this.target,this.layout.layoutBox),nL(this.target,this.targetDelta)):rP(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nS(),this.relativeTargetOrigin=nS(),nE(this.relativeTargetOrigin,this.target,e.target),rP(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}h.value&&rU.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||nC(this.parent.latestValues)||nk(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===g.timestamp&&(n=!1),n)return;let{layout:r,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||i))return;rP(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,s=this.treeScale.y;!function(e,t,n,r=!1){let i,a,s=n.length;if(s){t.x=t.y=1;for(let o=0;o<s;o++){a=(i=n[o]).projectionDelta;let{visualElement:s}=i.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&nF(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,nL(e,a)),r&&nO(i.latestValues)&&nF(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=nS());let{target:o}=e;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rT(this.prevProjectionDelta.x,this.projectionDelta.x),rT(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nj(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===a&&this.treeScale.y===s&&rI(this.projectionDelta.x,this.prevProjectionDelta.x)&&rI(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),h.value&&rU.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=nA(),this.projectionDelta=nA(),this.projectionDeltaWithTransform=nA()}setAnimationOrigin(e,t=!1){let n,r=this.snapshot,i=r?r.latestValues:{},a={...this.latestValues},s=nA();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=nS(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(r5));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(r3(s.x,e.x,r),r3(s.y,e.y,r),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,p,f,m,g;nE(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,f=this.relativeTargetOrigin,m=o,g=r,r4(p.x,f.x,m.x,g),r4(p.y,f.y,m.y,g),n&&(u=this.relativeTarget,h=n,rC(u.x,h.x)&&rC(u.y,h.y))&&(this.isProjectionDirty=!1),n||(n=nS()),rP(n,this.relativeTarget)}l&&(this.animationValues=a,function(e,t,n,r,i,a){i?(e.opacity=eR(0,n.opacity??1,rx(r)),e.opacityExit=eR(t.opacity??1,0,rb(r))):a&&(e.opacity=eR(t.opacity??1,n.opacity??1,r));for(let i=0;i<rm;i++){let a=`border${rf[i]}Radius`,s=rv(t,a),o=rv(n,a);(void 0!==s||void 0!==o)&&(s||(s=0),o||(o=0),0===s||0===o||ry(s)===ry(o)?(e[a]=Math.max(eR(rg(s),rg(o),r),0),(el.test(o)||el.test(s))&&(e[a]+="%")):e[a]=o)}(t.rotate||n.rotate)&&(e.rotate=eR(t.rotate||0,n.rotate||0,r))}(a,i,this.latestValues,r,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=f.update(()=>{rn.hasAnimatedSinceResize=!0,F.layout++,this.motionValue||(this.motionValue=S(0)),this.currentAnimation=function(e,t,n){let r=M(e)?e:S(e);return r.start(t6("",r,t,n)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{F.layout--},onComplete:()=>{F.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&ie(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||nS();let t=nb(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=nb(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}rP(t,n),nF(t,i),nj(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rL),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&rH("z",e,r,this.animationValues);for(let t=0;t<rV.length;t++)rH(`rotate${rV[t]}`,e,r,this.animationValues),rH(`skew${rV[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return rF;let t={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=rp(e?.pointerEvents)||"",t.transform=n?n(this.latestValues,""):"none",t;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=rp(e?.pointerEvents)||""),this.hasProjected&&!nO(this.latestValues)&&(t.transform=n?n({},""):"none",this.hasProjected=!1),t}let i=r.animationValues||r.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,n){let r="",i=e.x.translate/t.x,a=e.y.translate/t.y,s=n?.z||0;if((i||a||s)&&(r=`translate3d(${i}px, ${a}px, ${s}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:a,skewX:s,skewY:o}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),a&&(r+=`rotateY(${a}deg) `),s&&(r+=`skewX(${s}deg) `),o&&(r+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(r+=`scale(${o}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i),n&&(t.transform=n(i,t.transform));let{x:a,y:s}=this.projectionDelta;for(let e in t.transformOrigin=`${100*a.origin}% ${100*s.origin}% 0`,r.animationValues?t.opacity=r===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:t.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,ra){if(void 0===i[e])continue;let{correct:n,applyTo:a,isCSSVariable:s}=ra[e],o="none"===t.transform?i[e]:n(i[e],r);if(a){let e=a.length;for(let n=0;n<e;n++)t[a[n]]=o}else s?this.options.visualElement.renderState.vars[e]=o:t[e]=o}return this.options.layoutId&&(t.pointerEvents=r===this?rp(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(rY),this.root.sharedNodes.clear()}}}function r$(e){e.updateLayout()}function rW(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:r}=e.layout,{animationType:i}=e.options,a=t.source!==e.layout.source;"size"===i?n_(e=>{let r=a?t.measuredBox[e]:t.layoutBox[e],i=nb(r);r.min=n[e].min,r.max=r.min+i}):ie(i,t.layoutBox,n)&&n_(r=>{let i=a?t.measuredBox[r]:t.layoutBox[r],s=nb(n[r]);i.max=i.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+s)});let s=nA();nj(s,n,t.layoutBox);let o=nA();a?nj(o,e.applyTransform(r,!0),t.measuredBox):nj(o,n,t.layoutBox);let l=!rM(s),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:a}=r;if(i&&a){let s=nS();nE(s,t.layoutBox,i.layoutBox);let o=nS();nE(o,n,a.layoutBox),rk(s,o)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=s,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:o,layoutDelta:s,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rK(e){h.value&&rU.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function rq(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rG(e){e.clearSnapshot()}function rY(e){e.clearMeasurements()}function rX(e){e.isLayoutDirty=!1}function rZ(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rQ(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rJ(e){e.resolveTargetDelta()}function r0(e){e.calcProjection()}function r1(e){e.resetSkewAndRotation()}function r2(e){e.removeLeadSnapshot()}function r3(e,t,n){e.translate=eR(t.translate,0,n),e.scale=eR(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function r4(e,t,n,r){e.min=eR(t.min,n.min,r),e.max=eR(t.max,n.max,r)}function r5(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let r6={duration:.45,ease:[.4,0,.1,1]},r8=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),r9=r8("applewebkit/")&&!r8("chrome/")?Math.round:u;function r7(e){e.min=r9(e.min),e.max=r9(e.max)}function ie(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rD(t)-rD(n)))}function it(e){return e!==e.root&&e.scroll?.wasRoot}let ir=rz({attachResizeListener:(e,t)=>nf(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ii={current:void 0},ia=rz({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ii.current){let e=new ir({});e.mount(window),e.setOptions({layoutScroll:!0}),ii.current=e}return ii.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function is(e,t){let n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,n=(void 0)??t.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function io(e){return!("touch"===e.pointerType||np.x||np.y)}function il(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&f.postRender(()=>i(t,ng(t)))}class iu extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,a]=is(e,n),s=e=>{if(!io(e))return;let{target:n}=e,r=t(n,e);if("function"!=typeof r||!n)return;let a=e=>{io(e)&&(r(e),n.removeEventListener("pointerleave",a))};n.addEventListener("pointerleave",a,i)};return r.forEach(e=>{e.addEventListener("pointerenter",s,i)}),a}(e,(e,t)=>(il(this.node,t,"Start"),e=>il(this.node,e,"End"))))}unmount(){}}class ic extends nu{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=I(nf(this.node.current,"focus",()=>this.onFocus()),nf(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let id=(e,t)=>!!t&&(e===t||id(e,t.parentElement)),ih=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ip=new WeakSet;function im(e){return t=>{"Enter"===t.key&&e(t)}}function ig(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let iy=(e,t)=>{let n=e.currentTarget;if(!n)return;let r=im(()=>{if(ip.has(n))return;ig(n,"down");let e=im(()=>{ig(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>ig(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function iv(e){return nm(e)&&!(np.x||np.y)}function ix(e,t,n){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&f.postRender(()=>i(t,ng(t)))}class ib extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,a]=is(e,n),s=e=>{let r=e.currentTarget;if(!iv(e))return;ip.add(r);let a=t(r,e),s=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),ip.has(r)&&ip.delete(r),iv(e)&&"function"==typeof a&&a(e,{success:t})},o=e=>{s(e,r===window||r===document||n.useGlobalTarget||id(r,e.target))},l=e=>{s(e,!1)};window.addEventListener("pointerup",o,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{((n.useGlobalTarget?window:e).addEventListener("pointerdown",s,i),(0,tX.s)(e))&&(e.addEventListener("focus",e=>iy(e,i)),ih.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),a}(e,(e,t)=>(ix(this.node,t,"Start"),(e,{success:t})=>ix(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let iw=new WeakMap,ij=new WeakMap,iP=e=>{let t=iw.get(e.target);t&&t(e)},iT=e=>{e.forEach(iP)},iE={some:0,all:1};class iR extends nu{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,a={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:iE[r]};return function(e,t,n){let r=function({root:e,...t}){let n=e||document;ij.has(n)||ij.set(n,{});let r=ij.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(iT,{root:e,...t})),r[i]}(t);return iw.set(e,n),r.observe(e),()=>{iw.delete(e),r.unobserve(e)}}(this.node.current,a,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),a=t?n:r;a&&a(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let iA=(0,n9.createContext)({strict:!1});var iN=n(2582);let iS=(0,n9.createContext)({});function i_(e){return i(e.animate)||nr.some(t=>nt(e[t]))}function iM(e){return!!(i_(e)||e.variants)}function iC(e){return Array.isArray(e)?e.join(" "):e}var iO=n(7044);let ik={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iD={};for(let e in ik)iD[e]={isEnabled:t=>ik[e].some(e=>!!t[e])};let iI=Symbol.for("motionComponentSymbol");var iL=n(1279),iU=n(2743);function iV(e,{layout:t,layoutId:n}){return x.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!ra[e]||"opacity"===e)}let iF=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iB={...G,transform:Math.round},iH={borderWidth:eu,borderTopWidth:eu,borderRightWidth:eu,borderBottomWidth:eu,borderLeftWidth:eu,borderRadius:eu,radius:eu,borderTopLeftRadius:eu,borderTopRightRadius:eu,borderBottomRightRadius:eu,borderBottomLeftRadius:eu,width:eu,maxWidth:eu,height:eu,maxHeight:eu,top:eu,right:eu,bottom:eu,left:eu,padding:eu,paddingTop:eu,paddingRight:eu,paddingBottom:eu,paddingLeft:eu,margin:eu,marginTop:eu,marginRight:eu,marginBottom:eu,marginLeft:eu,backgroundPositionX:eu,backgroundPositionY:eu,rotate:eo,rotateX:eo,rotateY:eo,rotateZ:eo,scale:X,scaleX:X,scaleY:X,scaleZ:X,skew:eo,skewX:eo,skewY:eo,distance:eu,translateX:eu,translateY:eu,translateZ:eu,x:eu,y:eu,z:eu,perspective:eu,transformPerspective:eu,opacity:Y,originX:eh,originY:eh,originZ:eu,zIndex:iB,fillOpacity:Y,strokeOpacity:Y,numOctaves:iB},iz={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},i$=v.length;function iW(e,t,n){let{style:r,vars:i,transformOrigin:a}=e,s=!1,o=!1;for(let e in t){let n=t[e];if(x.has(e)){s=!0;continue}if($(e)){i[e]=n;continue}{let t=iF(n,iH[e]);e.startsWith("origin")?(o=!0,a[e]=t):r[e]=t}}if(!t.transform&&(s||n?r.transform=function(e,t,n){let r="",i=!0;for(let a=0;a<i$;a++){let s=v[a],o=e[s];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!s.startsWith("scale"):0===parseFloat(o))||n){let e=iF(o,iH[s]);if(!l){i=!1;let t=iz[s]||s;r+=`${t}(${e}) `}n&&(t[s]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),o){let{originX:e="50%",originY:t="50%",originZ:n=0}=a;r.transformOrigin=`${e} ${t} ${n}`}}let iK=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iq(e,t,n){for(let r in t)M(t[r])||iV(r,n)||(e[r]=t[r])}let iG={offset:"stroke-dashoffset",array:"stroke-dasharray"},iY={offset:"strokeDashoffset",array:"strokeDasharray"};function iX(e,{attrX:t,attrY:n,attrScale:r,pathLength:i,pathSpacing:a=1,pathOffset:s=0,...o},l,u,c){if(iW(e,o,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:h}=e;d.transform&&(h.transform=d.transform,delete d.transform),(h.transform||d.transformOrigin)&&(h.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),h.transform&&(h.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==t&&(d.x=t),void 0!==n&&(d.y=n),void 0!==r&&(d.scale=r),void 0!==i&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let a=i?iG:iY;e[a.offset]=eu.transform(-r);let s=eu.transform(t),o=eu.transform(n);e[a.array]=`${s} ${o}`}(d,i,a,s,!1)}let iZ=()=>({...iK(),attrs:{}}),iQ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),iJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function i0(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||iJ.has(e)}let i1=e=>!i0(e);try{!function(e){"function"==typeof e&&(i1=t=>t.startsWith("on")?!i0(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let i2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i3(e){if("string"!=typeof e||e.includes("-"));else if(i2.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var i4=n(2789);let i5=e=>(t,n)=>{let r=(0,n9.useContext)(iS),a=(0,n9.useContext)(iL.t),o=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,a){return{latestValues:function(e,t,n,r){let a={},o=r(e,{});for(let e in o)a[e]=rp(o[e]);let{initial:l,animate:u}=e,c=i_(e),d=iM(e);t&&d&&!c&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let h=!!n&&!1===n.initial,p=(h=h||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!i(p)){let t=Array.isArray(p)?p:[p];for(let n=0;n<t.length;n++){let r=s(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(a[e]=t)}for(let t in e)a[t]=e[t]}}}return a}(n,r,a,e),renderState:t()}})(e,t,r,a);return n?o():(0,i4.M)(o)};function i6(e,t,n){let{style:r}=e,i={};for(let a in r)(M(r[a])||t.style&&M(t.style[a])||iV(a,e)||n?.getValue(a)?.liveStyle!==void 0)&&(i[a]=r[a]);return i}let i8={useVisualState:i5({scrapeMotionValuesFromProps:i6,createRenderState:iK})};function i9(e,t,n){let r=i6(e,t,n);for(let n in e)(M(e[n])||M(t[n]))&&(r[-1!==v.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}let i7={useVisualState:i5({scrapeMotionValuesFromProps:i9,createRenderState:iZ})},ae=e=>t=>t.test(e),at=[G,eu,el,eo,ed,ec,{test:e=>"auto"===e,parse:e=>e}],an=e=>at.find(ae(e)),ar=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),ai=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,aa=e=>/^0[^.\s]+$/u.test(e),as=new Set(["brightness","contrast","saturate","opacity"]);function ao(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(Q)||[];if(!r)return e;let i=n.replace(r,""),a=+!!as.has(t);return r!==n&&(a*=100),t+"("+a+i+")"}let al=/\b([a-z-]*)\(.*?\)/gu,au={...eP,getAnimatableNone:e=>{let t=e.match(al);return t?t.map(ao).join(" "):e}},ac={...iH,color:ef,backgroundColor:ef,outlineColor:ef,fill:ef,stroke:ef,borderColor:ef,borderTopColor:ef,borderRightColor:ef,borderBottomColor:ef,borderLeftColor:ef,filter:au,WebkitFilter:au},ad=e=>ac[e];function ah(e,t){let n=ad(e);return n!==au&&(n=eP),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let ap=new Set(["auto","none","0"]);class af extends tD{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&K(r=r.trim())){let i=function e(t,n,r=1){H(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,a]=function(e){let t=ai.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${n??r}`,i]}(t);if(!i)return;let s=window.getComputedStyle(n).getPropertyValue(i);if(s){let e=s.trim();return ar(e)?parseFloat(e):e}return K(a)?e(a,n,r+1):a}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!b.has(n)||2!==e.length)return;let[r,i]=e,a=an(r),s=an(i);if(a!==s)if(tE(a)&&tE(s))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else tN[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||aa(r)))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!ap.has(t)&&ex(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=ah(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tN[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let i=n.length-1,a=n[i];n[i]=tN[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}let am=[...at,ef,eP],ag=e=>am.find(ae(e)),ay={current:null},av={current:!1},ax=new WeakMap,ab=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class aw{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:a},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tD,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=E.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,f.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=a;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=s,this.blockInitialAnimation=!!i,this.isControllingVariants=i_(t),this.isVariantNode=iM(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==o[e]&&M(t)&&t.set(o[e],!1)}}mount(e){this.current=e,ax.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),av.current||function(){if(av.current=!0,iO.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>ay.current=e.matches;e.addListener(t),t()}else ay.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ay.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=x.has(e);r&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&f.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),a=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),a(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iD){let t=iD[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):nS()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<ab.length;t++){let n=ab[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],a=n[r];if(M(i))e.addValue(r,i);else if(M(a))e.addValue(r,S(i,{owner:e}));else if(a!==i)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,S(void 0!==t?t:i,{owner:e}))}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=S(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){let n=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=n&&("string"==typeof n&&(ar(n)||aa(n))?n=parseFloat(n):!ag(n)&&eP.test(t)&&(n=ah(e,t)),this.setBaseTarget(e,M(n)?n.get():n)),M(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=s(this.props,n,this.presenceContext?.custom);r&&(t=r[e])}if(n&&void 0!==t)return t;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||M(r)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new P),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class aj extends aw{constructor(){super(...arguments),this.KeyframeResolver=af}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;M(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function aP(e,{style:t,vars:n},r,i){for(let a in Object.assign(e.style,t,i&&i.getProjectionStyles(r)),n)e.style.setProperty(a,n[a])}class aT extends aj{constructor(){super(...arguments),this.type="html",this.renderInstance=aP}readValueFromInstance(e,t){if(x.has(t))return this.projection?.isProjecting?tw(t):tP(e,t);{let n=window.getComputedStyle(e),r=($(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return nB(e,t)}build(e,t,n){iW(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return i6(e,t,n)}}let aE=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class aR extends aj{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=nS}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(x.has(t)){let e=ad(t);return e&&e.default||0}return t=aE.has(t)?t:O(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return i9(e,t,n)}build(e,t,n){iX(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){for(let n in aP(e,t,void 0,r),t.attrs)e.setAttribute(aE.has(n)?n:O(n),t.attrs[n])}mount(e){this.isSVGTag=iQ(e.tagName),super.mount(e)}}let aA=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(n,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((tG={animation:{Feature:nc},exit:{Feature:nh},inView:{Feature:iR},tap:{Feature:ib},focus:{Feature:ic},hover:{Feature:iu},pan:{Feature:n5},drag:{Feature:n3,ProjectionNode:ia,MeasureLayout:ro},layout:{ProjectionNode:ia,MeasureLayout:ro}},tY=(e,t)=>i3(e)?new aR(t):new aT(t,{allowProjection:e!==n9.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){function a(e,a){var s,o,l;let u,c={...(0,n9.useContext)(iN.Q),...e,layoutId:function({layoutId:e}){let t=(0,n9.useContext)(re.L).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:d}=c,h=function(e){let{initial:t,animate:n}=function(e,t){if(i_(e)){let{initial:t,animate:n}=e;return{initial:!1===t||nt(t)?t:void 0,animate:nt(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,n9.useContext)(iS));return(0,n9.useMemo)(()=>({initial:t,animate:n}),[iC(t),iC(n)])}(e),p=r(e,d);if(!d&&iO.B){o=0,l=0,(0,n9.useContext)(iA).strict;let e=function(e){let{drag:t,layout:n}=iD;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:t?.isEnabled(e)||n?.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(c);u=e.MeasureLayout,h.visualElement=function(e,t,n,r,i){let{visualElement:a}=(0,n9.useContext)(iS),s=(0,n9.useContext)(iA),o=(0,n9.useContext)(iL.t),l=(0,n9.useContext)(iN.Q).reducedMotion,u=(0,n9.useRef)(null);r=r||s.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:a,props:n,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let c=u.current,d=(0,n9.useContext)(rt);c&&!c.projection&&i&&("html"===c.type||"svg"===c.type)&&function(e,t,n,r){let{layoutId:i,layout:a,drag:s,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:a,alwaysMeasureLayout:!!s||o&&nz(o),visualElement:e,animationType:"string"==typeof a?a:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,n,i,d);let h=(0,n9.useRef)(!1);(0,n9.useInsertionEffect)(()=>{c&&h.current&&c.update(n,o)});let p=n[k],f=(0,n9.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,iU.E)(()=>{c&&(h.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),n8.render(c.render),f.current&&c.animationState&&c.animationState.animateChanges())}),(0,n9.useEffect)(()=>{c&&(!f.current&&c.animationState&&c.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),f.current=!1))}),c}(i,p,c,t,e.ProjectionNode)}return(0,n6.jsxs)(iS.Provider,{value:h,children:[u&&h.visualElement?(0,n6.jsx)(u,{visualElement:h.visualElement,...c}):null,n(i,e,(s=h.visualElement,(0,n9.useCallback)(e=>{e&&p.onMount&&p.onMount(e),s&&(e?s.mount(e):s.unmount()),a&&("function"==typeof a?a(e):nz(a)&&(a.current=e))},[s])),p,d,h.visualElement)]})}e&&function(e){for(let t in e)iD[t]={...iD[t],...e[t]}}(e),a.displayName=`motion.${"string"==typeof i?i:`create(${i.displayName??i.name??""})`}`;let s=(0,n9.forwardRef)(a);return s[iI]=i,s}({...i3(e)?i7:i8,preloadedFeatures:tG,useRender:function(e=!1){return(t,n,r,{latestValues:i},a)=>{let s=(i3(t)?function(e,t,n,r){let i=(0,n9.useMemo)(()=>{let n=iZ();return iX(n,t,iQ(r),e.transformTemplate,e.style),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};iq(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return iq(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,n9.useMemo)(()=>{let n=iK();return iW(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(n,i,a,t),o=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(i1(i)||!0===n&&i0(i)||!t&&!i0(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"==typeof t,e),l=t!==n9.Fragment?{...o,...s,ref:r}:{},{children:u}=n,c=(0,n9.useMemo)(()=>M(u)?u.get():u,[u]);return(0,n9.createElement)(t,{...l,children:c})}}(t),createVisualElement:tY,Component:e})}))},6044:(e,t,n)=>{"use strict";n.d(t,{xQ:()=>a});var r=n(3210),i=n(1279);function a(e=!0){let t=(0,r.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:s,register:o}=t,l=(0,r.useId)();(0,r.useEffect)(()=>{if(e)return o(l)},[e]);let u=(0,r.useCallback)(()=>e&&s&&s(l),[l,s,e]);return!n&&s?[!1,u]:[!0]}},6127:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return a}});let r=n(8834),i=n(4674);function a(e,t){return(0,i.normalizePathTrailingSlash)((0,r.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6145:(e,t,n)=>{"use strict";n.d(t,{default:()=>d});var r=n(687),i=n(6001),a=n(8200),s=n(5583),o=n(8947),l=n(8559),u=n(4398);let c=(0,n(2688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]),d=()=>{let e=[{id:1,name:"ARIA-7",title:"AI Strength Specialist",specialty:"Neural-Enhanced Strength Training",experience:"10,000+ Sessions",rating:4.9,avatar:"/api/placeholder/300/300",description:"Advanced AI trainer specializing in biomechanical optimization and strength enhancement through neural feedback.",skills:["Form Analysis","Progressive Overload","Injury Prevention","Neural Optimization"],achievements:["99.2% Success Rate","50% Faster Results","Zero Injuries"],icon:a.A,color:"blue"},{id:2,name:"NOVA-X",title:"VR Cardio Master",specialty:"Immersive Cardio Experiences",experience:"8,500+ Sessions",rating:4.8,avatar:"/api/placeholder/300/300",description:"Virtual reality fitness expert creating immersive cardio adventures that make workouts feel like epic quests.",skills:["VR Environment Design","Cardio Optimization","Gamification","Endurance Building"],achievements:["95% Retention Rate","40% Improved Endurance","Award-Winning VR Design"],icon:s.A,color:"purple"},{id:3,name:"ZENITH-9",title:"Biometric Yoga Guide",specialty:"Mind-Body Synchronization",experience:"12,000+ Sessions",rating:5,avatar:"/api/placeholder/300/300",description:"Holistic wellness AI combining ancient yoga wisdom with cutting-edge biometric monitoring for perfect balance.",skills:["Breath Analysis","Flexibility Tracking","Stress Reduction","Mindfulness"],achievements:["Perfect 5.0 Rating","60% Stress Reduction","Meditation Master"],icon:o.A,color:"green"},{id:4,name:"TITAN-5",title:"HIIT Performance Coach",specialty:"High-Intensity Optimization",experience:"9,200+ Sessions",rating:4.9,avatar:"/api/placeholder/300/300",description:"Elite performance AI designed for maximum intensity training with real-time adaptation to your limits.",skills:["HIIT Protocols","Recovery Optimization","Performance Analytics","Motivation Systems"],achievements:["35% Faster Fat Loss","98% Goal Achievement","Elite Athlete Approved"],icon:l.A,color:"cyan"}];return(0,r.jsxs)("section",{className:"py-24 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-green-900/10 via-transparent to-cyan-900/10"}),(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-20",children:[(0,r.jsxs)("div",{className:"inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-neon-green"}),(0,r.jsx)("span",{className:"font-orbitron text-sm font-medium text-white",children:"AI TRAINERS"})]}),(0,r.jsxs)("h2",{className:"font-orbitron text-4xl md:text-6xl font-bold mb-8",children:[(0,r.jsx)("span",{className:"text-white",children:"MEET YOUR "}),(0,r.jsx)("span",{className:"text-gradient-neon",children:"DIGITAL"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"text-gradient-cyber",children:"COACHES"})]}),(0,r.jsx)("p",{className:"font-exo text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed",children:"Our AI trainers combine thousands of hours of expertise with real-time adaptation, providing personalized coaching that evolves with your progress."})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16",children:e.map((e,t)=>{let n=e.icon;return(0,r.jsx)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2*t},viewport:{once:!0},children:(0,r.jsxs)("div",{className:"glass rounded-2xl p-8 h-full relative overflow-hidden hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 pointer-events-none"}),(0,r.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent"}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-6 mb-6",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-gradient-to-br from-slate-700 to-slate-800 rounded-2xl flex items-center justify-center",children:(0,r.jsx)(n,{className:"w-12 h-12 text-cyan-400"})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-transparent via-cyan-400/20 to-transparent rounded-2xl"}),(0,r.jsx)("div",{className:"absolute top-2 left-0 w-full h-0.5 bg-cyan-400/30"}),(0,r.jsx)("div",{className:"absolute bottom-2 left-0 w-full h-0.5 bg-cyan-400/30"})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-orbitron text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent mb-2",children:e.name}),(0,r.jsx)("p",{className:"font-rajdhani text-lg text-cyan-400 mb-2",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:[(0,r.jsxs)("span",{className:"flex items-center space-x-1",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 text-yellow-400 fill-current"}),(0,r.jsx)("span",{children:e.rating})]}),(0,r.jsx)("span",{children:e.experience})]})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"font-orbitron text-sm font-semibold text-white mb-2",children:"SPECIALTY"}),(0,r.jsx)("p",{className:"font-exo text-gray-300",children:e.specialty})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("p",{className:"font-exo text-gray-400 leading-relaxed",children:e.description})}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"font-orbitron text-sm font-semibold text-white mb-3",children:"CORE SKILLS"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.skills.map(e=>(0,r.jsx)("span",{className:"px-3 py-1 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-xs font-rajdhani text-blue-300",children:e},e))})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h4",{className:"font-orbitron text-sm font-semibold text-white mb-3",children:"ACHIEVEMENTS"}),(0,r.jsx)("div",{className:"space-y-2",children:e.achievements.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c,{className:"w-4 h-4 text-yellow-400"}),(0,r.jsx)("span",{className:"font-exo text-sm text-gray-300",children:e})]},e))})]}),(0,r.jsxs)("button",{className:"w-full px-6 py-3 bg-transparent border-2 border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black rounded-lg font-orbitron font-semibold transition-all duration-300",children:["Train with ",e.name]})]})]})},e.id)})}),(0,r.jsx)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center",children:(0,r.jsxs)("div",{className:"glass rounded-2xl p-12 max-w-4xl mx-auto hover:shadow-lg hover:shadow-purple-500/20 transition-all duration-300",children:[(0,r.jsx)("h3",{className:"font-orbitron text-3xl font-bold bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent mb-6",children:"Ready to Meet Your Perfect AI Match?"}),(0,r.jsx)("p",{className:"font-exo text-lg text-gray-300 leading-relaxed mb-8",children:"Our advanced matching algorithm will pair you with the ideal AI trainer based on your goals, preferences, and fitness level."}),(0,r.jsx)("button",{className:"px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg font-orbitron font-bold text-white text-lg hover:from-blue-500 hover:to-purple-500 transition-all duration-300 shadow-lg hover:shadow-blue-500/50",children:"Find My AI Trainer"})]})})]})]})}},6156:(e,t,n)=>{"use strict";n.d(t,{default:()=>d});var r=n(687),i=n(3210),a=n(5583),s=n(2688);let o=(0,s.A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]),l=(0,s.A)("rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]]);var u=n(4398);let c=(0,s.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),d=()=>{let[e,t]=(0,i.useState)("monthly"),n=[{id:"standard",name:"Standard",description:"Perfect for fitness enthusiasts",icon:a.A,popular:!1,pricing:{monthly:99,yearly:999},features:["Access to AI trainers","VR environments","Biometric tracking","24/7 gym access","Mobile app access","Community challenges","Progress analytics","Email support"]},{id:"premium",name:"Premium",description:"Advanced AI coaching with personalization",icon:o,popular:!0,pricing:{monthly:199,yearly:1999},features:["All Standard features","Advanced AI trainers","Premium VR environments","Advanced biometric analysis","Personalized nutrition AI","Recovery optimization","Priority booking","Video call support","Custom workout plans","Genetic fitness profiling"]},{id:"elite",name:"Elite",description:"The ultimate fitness experience",icon:l,popular:!1,pricing:{monthly:399,yearly:3999},features:["All Premium features","Exclusive elite AI trainers","Custom VR environment creation","Neural feedback training","Personal training","Concierge health services","Private training pods","24/7 dedicated support","Quarterly health assessments","Access to experimental programs","VIP events and workshops"]}],s={standard:Math.round((12*n[0].pricing.monthly-n[0].pricing.yearly)/(12*n[0].pricing.monthly)*100),premium:Math.round((12*n[1].pricing.monthly-n[1].pricing.yearly)/(12*n[1].pricing.monthly)*100),elite:Math.round((12*n[2].pricing.monthly-n[2].pricing.yearly)/(12*n[2].pricing.monthly)*100)};return(0,r.jsx)("section",{className:"py-24 bg-gray-900",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsxs)("div",{className:"inline-flex items-center space-x-2 px-4 py-2 glass rounded-full mb-8",children:[(0,r.jsx)(o,{className:"w-4 h-4 text-blue-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-300",children:"Membership Plans"})]}),(0,r.jsxs)("h2",{className:"font-space text-4xl md:text-6xl font-bold mb-8",children:[(0,r.jsx)("span",{className:"text-white",children:"Choose Your "}),(0,r.jsx)("span",{className:"bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent",children:"Plan"})]}),(0,r.jsx)("p",{className:"text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed mb-12",children:"Unlock the power of next-generation fitness technology with our flexible membership options."}),(0,r.jsxs)("div",{className:"inline-flex items-center glass rounded-lg p-1",children:[(0,r.jsx)("button",{onClick:()=>t("monthly"),className:`px-6 py-3 rounded-md font-medium transition-all duration-200 ${"monthly"===e?"bg-blue-500 text-white":"text-gray-400 hover:text-white"}`,children:"Monthly"}),(0,r.jsxs)("button",{onClick:()=>t("yearly"),className:`px-6 py-3 rounded-md font-medium transition-all duration-200 relative ${"yearly"===e?"bg-blue-500 text-white":"text-gray-400 hover:text-white"}`,children:["Yearly",(0,r.jsx)("span",{className:"absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full",children:"Save 20%"})]})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto",children:n.map(t=>{let n=t.icon,i=t.pricing[e],a="yearly"===e?s[t.id]:0;return(0,r.jsxs)("div",{className:"relative",children:[t.popular&&(0,r.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2 z-20",children:(0,r.jsx)("div",{className:"bg-blue-500 text-white px-4 py-2 rounded-full font-medium text-sm",children:"Most Popular"})}),(0,r.jsxs)("div",{className:`card p-8 h-full relative ${t.popular?"border-blue-500":""}`,children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-16 h-16 bg-blue-500/20 rounded-xl mb-4 mx-auto",children:(0,r.jsx)(n,{className:"w-8 h-8 text-blue-400"})}),(0,r.jsx)("h3",{className:"font-space text-2xl font-bold text-white mb-2",children:t.name}),(0,r.jsx)("p",{className:"text-gray-400 mb-6",children:t.description}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("span",{className:"text-4xl font-bold text-white",children:["$",i]}),(0,r.jsxs)("span",{className:"text-gray-400 ml-2",children:["/","monthly"===e?"month":"year"]})]}),"yearly"===e&&a>0&&(0,r.jsxs)("div",{className:"inline-flex items-center space-x-1 bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-sm",children:[(0,r.jsx)(u.A,{className:"w-4 h-4"}),(0,r.jsxs)("span",{children:["Save ",a,"%"]})]})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)("ul",{className:"space-y-3",children:t.features.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-start space-x-3",children:[(0,r.jsx)(c,{className:"w-5 h-5 text-green-400 mt-0.5 flex-shrink-0"}),(0,r.jsx)("span",{className:"text-gray-300 text-sm",children:e})]},t))})}),(0,r.jsx)("button",{className:t.popular?"btn-primary w-full":"btn-secondary w-full",children:t.popular?"Start Premium Trial":`Choose ${t.name}`})]})]},t.id)})}),(0,r.jsxs)("div",{className:"text-center mt-16",children:[(0,r.jsx)("p",{className:"text-gray-400 mb-8",children:"All plans include a 7-day free trial. No commitment, cancel anytime."}),(0,r.jsxs)("div",{className:"flex flex-wrap justify-center gap-8 text-sm text-gray-500",children:[(0,r.jsx)("span",{children:"✓ No setup fees"}),(0,r.jsx)("span",{children:"✓ 30-day money-back guarantee"}),(0,r.jsx)("span",{children:"✓ Pause membership anytime"}),(0,r.jsx)("span",{children:"✓ Upgrade/downgrade flexibility"})]})]})]})})}},6312:(e,t,n)=>{"use strict";function r(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}n.r(t),n.d(t,{_:()=>r})},6341:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getPreviouslyRevalidatedTags:function(){return y},getUtils:function(){return g},interpolateDynamicPath:function(){return f},normalizeDynamicRouteParams:function(){return m},normalizeVercelUrl:function(){return p}});let r=n(9551),i=n(1959),a=n(2437),s=n(4396),o=n(8034),l=n(5526),u=n(2887),c=n(4722),d=n(6143),h=n(7912);function p(e,t,n){let i=(0,r.parse)(e.url,!0);for(let e of(delete i.search,Object.keys(i.query))){let r=e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX),a=e!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(r||a||t.includes(e)||n&&Object.keys(n.groups).includes(e))&&delete i.query[e]}e.url=(0,r.format)(i)}function f(e,t,n){if(!n)return e;for(let r of Object.keys(n.groups)){let i,{optional:a,repeat:s}=n.groups[r],o=`[${s?"...":""}${r}]`;a&&(o=`[${o}]`);let l=t[r];i=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(o,i)}return e}function m(e,t,n,r){let i={};for(let a of Object.keys(t.groups)){let s=e[a];"string"==typeof s?s=(0,c.normalizeRscURL)(s):Array.isArray(s)&&(s=s.map(c.normalizeRscURL));let o=n[a],l=t.groups[a].optional;if((Array.isArray(o)?o.some(e=>Array.isArray(s)?s.some(t=>t.includes(e)):null==s?void 0:s.includes(e)):null==s?void 0:s.includes(o))||void 0===s&&!(l&&r))return{params:{},hasValidParams:!1};l&&(!s||Array.isArray(s)&&1===s.length&&("index"===s[0]||s[0]===`[[...${a}]]`))&&(s=void 0,delete e[a]),s&&"string"==typeof s&&t.groups[a].repeat&&(s=s.split("/")),s&&(i[a]=s)}return{params:i,hasValidParams:!0}}function g({page:e,i18n:t,basePath:n,rewrites:r,pageIsDynamic:c,trailingSlash:d,caseSensitive:g}){let y,v,x;return c&&(y=(0,s.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),x=(v=(0,o.getRouteMatcher)(y))(e)),{handleRewrites:function(s,o){let h={},p=o.pathname,f=r=>{let u=(0,a.getPathMatch)(r.source+(d?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!o.pathname)return!1;let f=u(o.pathname);if((r.has||r.missing)&&f){let e=(0,l.matchHas)(s,o.query,r.has,r.missing);e?Object.assign(f,e):f=!1}if(f){let{parsedDestination:a,destQuery:s}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:r.destination,params:f,query:o.query});if(a.protocol)return!0;if(Object.assign(h,s,f),Object.assign(o.query,a.query),delete a.query,Object.assign(o,a),!(p=o.pathname))return!1;if(n&&(p=p.replace(RegExp(`^${n}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(p,t.locales);p=e.pathname,o.query.nextInternalLocale=e.detectedLocale||f.nextInternalLocale}if(p===e)return!0;if(c&&v){let e=v(p);if(e)return o.query={...o.query,...e},!0}}return!1};for(let e of r.beforeFiles||[])f(e);if(p!==e){let t=!1;for(let e of r.afterFiles||[])if(t=f(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(p||"");return t===(0,u.removeTrailingSlash)(e)||(null==v?void 0:v(t))})()){for(let e of r.fallback||[])if(t=f(e))break}}return h},defaultRouteRegex:y,dynamicRouteMatcher:v,defaultRouteMatches:x,getParamsFromRouteMatches:function(e){if(!y)return null;let{groups:t,routeKeys:n}=y,r=(0,o.getRouteMatcher)({re:{exec:e=>{let r=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(r)){let n=(0,h.normalizeNextQueryParam)(e);n&&(r[n]=t,delete r[e])}let i={};for(let e of Object.keys(n)){let a=n[e];if(!a)continue;let s=t[a],o=r[e];if(!s.optional&&!o)return null;i[s.pos]=o}return i}},groups:t})(e);return r||null},normalizeDynamicRouteParams:(e,t)=>y&&x?m(e,y,x,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>p(e,t,y),interpolateDynamicPath:(e,t)=>f(e,t,y)}}function y(e,t){return"string"==typeof e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[d.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6361:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return i}});let r=n(6127);function i(e,t){if(e.startsWith(".")){let n=t.origin+t.pathname;return new URL((n.endsWith("/")?n:n+"/")+e)}return new URL((0,r.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,n){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},a=t.split(r),s=(n||{}).decode||e,o=0;o<a.length;o++){var l=a[o],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[c]&&(i[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,s))}}return i},t.serialize=function(e,t,r){var a=r||{},s=a.encode||n;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var o=s(t);if(o&&!i.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=a.maxAge){var u=a.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(a.domain){if(!i.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!i.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,n=encodeURIComponent,r=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6493:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return i}});let r=n(5232);function i(e,t,n){return(0,r.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6715:(e,t)=>{"use strict";function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[n,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(n,r(e));else t.set(n,r(i));return t}function a(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},6736:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let r=n(2255);function i(e){return(0,r.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6759:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return a}});let r=n(2785),i=n(3736);function a(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,r.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},6770:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,n,r,l){let u,[c,d,h,p,f]=n;if(1===t.length){let e=o(n,r);return(0,s.addRefreshMarkerToActiveParallelSegments)(e,l),e}let[m,g]=t;if(!(0,a.matchSegment)(m,c))return null;if(2===t.length)u=o(d[g],r);else if(null===(u=e((0,i.getNextFlightSegmentPath)(t),d[g],r,l)))return null;let y=[t[0],{...d,[g]:u},h,p];return f&&(y[4]=!0),(0,s.addRefreshMarkerToActiveParallelSegments)(y,l),y}}});let r=n(3913),i=n(4007),a=n(4077),s=n(2308);function o(e,t){let[n,i]=e,[s,l]=t;if(s===r.DEFAULT_SEGMENT_KEY&&n!==r.DEFAULT_SEGMENT_KEY)return e;if((0,a.matchSegment)(n,s)){let t={};for(let e in i)void 0!==l[e]?t[e]=o(i[e],l[e]):t[e]=i[e];for(let e in l)t[e]||(t[e]=l[e]);let r=[n,t];return e[2]&&(r[2]=e[2]),e[3]&&(r[3]=e[3]),e[4]&&(r[4]=e[4]),r}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6928:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return a}});let r=n(1500),i=n(3898);function a(e,t,n,a,s){let{tree:o,seedData:l,head:u,isRootRender:c}=a;if(null===l)return!1;if(c){let i=l[1];n.loading=l[3],n.rsc=i,n.prefetchRsc=null,(0,r.fillLazyItemsTillLeafWithHead)(e,n,t,o,l,u,s)}else n.rsc=t.rsc,n.prefetchRsc=t.prefetchRsc,n.parallelRoutes=new Map(t.parallelRoutes),n.loading=t.loading,(0,i.fillCacheWithNewSubTreeData)(e,n,t,a,s);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7022:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return s}});let r=n(3210),i=n(1215),a="next-route-announcer";function s(e){let{tree:t}=e,[n,s]=(0,r.useState)(null);(0,r.useEffect)(()=>(s(function(){var e;let t=document.getElementsByName(a)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[o,l]=(0,r.useState)(""),u=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&l(e),u.current=e},[t]),n?(0,i.createPortal)(o,n):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7044:(e,t,n)=>{"use strict";n.d(t,{B:()=>r});let r="undefined"!=typeof window},7378:()=>{},7464:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,n,a){let s=a.length<=2,[o,l]=a,u=(0,i.createRouterCacheKey)(l),c=n.parallelRoutes.get(o),d=t.parallelRoutes.get(o);d&&d!==c||(d=new Map(c),t.parallelRoutes.set(o,d));let h=null==c?void 0:c.get(u),p=d.get(u);if(s){p&&p.lazyData&&p!==h||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!p||!h){p||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return p===h&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes),loading:p.loading},d.set(u,p)),e(p,h,(0,r.getNextFlightSegmentPath)(a))}}});let r=n(4007),i=n(3123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7810:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return S}});let r=n(1264),i=n(1448),a=n(1563),s=n(9154),o=n(6361),l=n(7391),u=n(5232),c=n(6770),d=n(2030),h=n(9435),p=n(1500),f=n(9752),m=n(8214),g=n(6493),y=n(2308),v=n(4007),x=n(6875),b=n(7860),w=n(5334),j=n(5942),P=n(6736),T=n(4642);n(593);let{createFromFetch:E,createTemporaryReferenceSet:R,encodeReply:A}=n(9357);async function N(e,t,n){let s,l,{actionId:u,actionArgs:c}=n,d=R(),h=(0,T.extractInfoFromServerReferenceId)(u),p="use-cache"===h.type?(0,T.omitUnusedArgs)(c,h):c,f=await A(p,{temporaryReferences:d}),m=await fetch("",{method:"POST",headers:{Accept:a.RSC_CONTENT_TYPE_HEADER,[a.ACTION_HEADER]:u,[a.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...{},...t?{[a.NEXT_URL]:t}:{}},body:f}),g=m.headers.get("x-action-redirect"),[y,x]=(null==g?void 0:g.split(";"))||[];switch(x){case"push":s=b.RedirectType.push;break;case"replace":s=b.RedirectType.replace;break;default:s=void 0}let w=!!m.headers.get(a.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(m.headers.get("x-action-revalidated")||"[[],0,0]");l={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){l={paths:[],tag:!1,cookie:!1}}let j=y?(0,o.assignLocation)(y,new URL(e.canonicalUrl,window.location.href)):void 0,P=m.headers.get("content-type");if(null==P?void 0:P.startsWith(a.RSC_CONTENT_TYPE_HEADER)){let e=await E(Promise.resolve(m),{callServer:r.callServer,findSourceMapURL:i.findSourceMapURL,temporaryReferences:d});return y?{actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:j,redirectType:s,revalidatedParts:l,isPrerender:w}:{actionResult:e.a,actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:j,redirectType:s,revalidatedParts:l,isPrerender:w}}if(m.status>=400)throw Object.defineProperty(Error("text/plain"===P?await m.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:j,redirectType:s,revalidatedParts:l,isPrerender:w}}function S(e,t){let{resolve:n,reject:r}=t,i={},a=e.tree;i.preserveCustomHistoryState=!1;let o=e.nextUrl&&(0,m.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,v=Date.now();return N(e,o,t).then(async m=>{let T,{actionResult:E,actionFlightData:R,redirectLocation:A,redirectType:N,isPrerender:S,revalidatedParts:_}=m;if(A&&(N===b.RedirectType.replace?(e.pushRef.pendingPush=!1,i.pendingPush=!1):(e.pushRef.pendingPush=!0,i.pendingPush=!0),i.canonicalUrl=T=(0,l.createHrefFromUrl)(A,!1)),!R)return(n(E),A)?(0,u.handleExternalUrl)(e,i,A.href,e.pushRef.pendingPush):e;if("string"==typeof R)return n(E),(0,u.handleExternalUrl)(e,i,R,e.pushRef.pendingPush);let M=_.paths.length>0||_.tag||_.cookie;for(let r of R){let{tree:s,seedData:l,head:h,isRootRender:m}=r;if(!m)return console.log("SERVER ACTION APPLY FAILED"),n(E),e;let x=(0,c.applyRouterStatePatchToTree)([""],a,s,T||e.canonicalUrl);if(null===x)return n(E),(0,g.handleSegmentMismatch)(e,t,s);if((0,d.isNavigatingToNewRootLayout)(a,x))return n(E),(0,u.handleExternalUrl)(e,i,T||e.canonicalUrl,e.pushRef.pendingPush);if(null!==l){let t=l[1],n=(0,f.createEmptyCacheNode)();n.rsc=t,n.prefetchRsc=null,n.loading=l[3],(0,p.fillLazyItemsTillLeafWithHead)(v,n,void 0,s,l,h,void 0),i.cache=n,i.prefetchCache=new Map,M&&await (0,y.refreshInactiveParallelSegments)({navigatedAt:v,state:e,updatedTree:x,updatedCache:n,includeNextUrl:!!o,canonicalUrl:i.canonicalUrl||e.canonicalUrl})}i.patchedTree=x,a=x}return A&&T?(M||((0,w.createSeededPrefetchCacheEntry)({url:A,data:{flightData:R,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:S?s.PrefetchKind.FULL:s.PrefetchKind.AUTO}),i.prefetchCache=e.prefetchCache),r((0,x.getRedirectError)((0,P.hasBasePath)(T)?(0,j.removeBasePath)(T):T,N||b.RedirectType.push))):n(E),(0,h.handleMutable)(e,i)},t=>(r(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7936:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return r}}),n(9008),n(7391),n(6770),n(2030),n(5232),n(9435),n(6928),n(9752),n(6493),n(8214);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7967:(e,t,n)=>{"use strict";n.d(t,{default:()=>h});var r=n(687),i=n(6001),a=n(8200),s=n(2688);let o=(0,s.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var l=n(5583),u=n(8559),c=n(8947);let d=(0,s.A)("cpu",[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]]),h=()=>{let e=[{icon:a.A,title:"AI Personal Trainers",description:"Advanced AI algorithms analyze your form, track progress, and provide real-time coaching tailored to your unique fitness journey.",color:"blue"},{icon:o,title:"Biometric Tracking",description:"Cutting-edge sensors monitor heart rate, muscle activation, and movement patterns for optimal workout efficiency.",color:"purple"},{icon:l.A,title:"VR Integration",description:"Immerse yourself in virtual environments that make workouts engaging, from climbing mountains to exploring alien worlds.",color:"green"},{icon:u.A,title:"Smart Equipment",description:"IoT-enabled machines automatically adjust to your settings and provide detailed performance analytics.",color:"cyan"},{icon:c.A,title:"Precision Nutrition",description:"AI-powered meal planning based on your genetic profile, fitness goals, and real-time metabolic data.",color:"blue"},{icon:d,title:"Neural Feedback",description:"Advanced neurofeedback technology helps optimize mind-muscle connection and mental performance.",color:"purple"}];return(0,r.jsxs)("section",{className:"py-24 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-transparent via-blue-900/10 to-transparent"}),(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-20",children:[(0,r.jsxs)("div",{className:"inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-cyan-400"}),(0,r.jsx)("span",{className:"font-orbitron text-sm font-medium text-white",children:"FUTURE TECHNOLOGY"})]}),(0,r.jsxs)("h2",{className:"font-orbitron text-4xl md:text-6xl font-bold mb-8",children:[(0,r.jsx)("span",{className:"text-white",children:"BEYOND "}),(0,r.jsx)("span",{className:"bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent",children:"HUMAN"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent",children:"LIMITS"})]}),(0,r.jsx)("p",{className:"font-exo text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed",children:"NeoGym represents the convergence of cutting-edge technology and human potential. Our revolutionary approach combines artificial intelligence, virtual reality, and advanced biometrics to create the ultimate fitness experience."})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20",children:e.map((e,t)=>{let n=e.icon;return(0,r.jsx)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},children:(0,r.jsxs)("div",{className:"glass rounded-2xl p-8 h-full hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl mb-6 mx-auto",children:(0,r.jsx)(n,{className:"w-8 h-8 text-cyan-400"})}),(0,r.jsx)("h3",{className:"font-orbitron text-xl font-bold text-white mb-4 text-center",children:e.title}),(0,r.jsx)("p",{className:"font-exo text-gray-400 leading-relaxed text-center",children:e.description})]})},e.title)})}),(0,r.jsx)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center",children:(0,r.jsxs)("div",{className:"glass rounded-2xl p-12 max-w-4xl mx-auto hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300",children:[(0,r.jsx)("h3",{className:"font-orbitron text-3xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent mb-6",children:"Our Philosophy"}),(0,r.jsx)("p",{className:"text-lg text-gray-300 leading-relaxed mb-8",children:"“The future of fitness isn't just about stronger bodies—it's about enhanced minds, optimized performance, and the seamless integration of human potential with technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.”"}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-0.5 bg-gradient-to-r from-transparent to-blue-500"}),(0,r.jsx)("span",{className:"font-orbitron text-sm text-cyan-400 font-medium",children:"DR. ALEX CHEN, FOUNDER & CEO"}),(0,r.jsx)("div",{className:"w-12 h-0.5 bg-gradient-to-l from-transparent to-purple-500"})]})]})})]})]})}},7992:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(2688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},8034:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let r=n(4827);function i(e){let{re:t,groups:n}=e;return e=>{let i=t.exec(e);if(!i)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new r.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},s={};for(let[e,t]of Object.entries(n)){let n=i[t.pos];void 0!==n&&(t.repeat?s[e]=n.split("/").map(e=>a(e)):s[e]=a(n))}return s}}},8171:(e,t,n)=>{"use strict";n.d(t,{s:()=>i});var r=n(4479);function i(e){return(0,r.G)(e)&&"offsetHeight"in e}},8200:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(2688).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},8212:(e,t,n)=>{"use strict";function r(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:r}=n(6415);return r(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return r}})},8304:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return s},getExtensionRegexString:function(){return l},isMetadataPage:function(){return d},isMetadataRoute:function(){return h},isMetadataRouteFile:function(){return u},isStaticMetadataRoute:function(){return c}});let r=n(2958),i=n(4722),a=n(554),s={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function u(e,t,n){let i=(n?"":"?")+"$",a=`\\d?${n?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${i}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${i}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${i}`),RegExp(`[\\\\/]${s.icon.filename}${a}${l(s.icon.extensions,t)}${i}`),RegExp(`[\\\\/]${s.apple.filename}${a}${l(s.apple.extensions,t)}${i}`),RegExp(`[\\\\/]${s.openGraph.filename}${a}${l(s.openGraph.extensions,t)}${i}`),RegExp(`[\\\\/]${s.twitter.filename}${a}${l(s.twitter.extensions,t)}${i}`)],u=(0,r.normalizePathSep)(e);return o.some(e=>e.test(u))}function c(e){let t=e.replace(/\/route$/,"");return(0,a.isAppRouteRoute)(e)&&u(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function d(e){return!(0,a.isAppRouteRoute)(e)&&u(e,[],!1)}function h(e){let t=(0,i.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,a.isAppRouteRoute)(e)&&u(t,[],!1)}},8340:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(2688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},8468:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,n,a){let s=a.length<=2,[o,l]=a,u=(0,r.createRouterCacheKey)(l),c=n.parallelRoutes.get(o);if(!c)return;let d=t.parallelRoutes.get(o);if(d&&d!==c||(d=new Map(c),t.parallelRoutes.set(o,d)),s)return void d.delete(u);let h=c.get(u),p=d.get(u);p&&h&&(p===h&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes)},d.set(u,p)),e(p,h,(0,i.getNextFlightSegmentPath)(a)))}}});let r=n(3123),i=n(4007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8559:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(2688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},8595:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Store\\\\gym-website\\\\neogym\\\\src\\\\components\\\\sections\\\\Hero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Hero.tsx","default")},8627:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return a}});let r=n(7391),i=n(642);function a(e,t){var n;let{url:a,tree:s}=t,o=(0,r.createHrefFromUrl)(a),l=s||e.tree,u=e.cache;return{canonicalUrl:o,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:u,prefetchCache:e.prefetchCache,tree:l,nextUrl:null!=(n=(0,i.extractPathFromFlightRouterState)(l))?n:a.pathname}}n(5956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8637:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,6346,23)),Promise.resolve().then(n.t.bind(n,7924,23)),Promise.resolve().then(n.t.bind(n,5656,23)),Promise.resolve().then(n.t.bind(n,99,23)),Promise.resolve().then(n.t.bind(n,8243,23)),Promise.resolve().then(n.t.bind(n,8827,23)),Promise.resolve().then(n.t.bind(n,2763,23)),Promise.resolve().then(n.t.bind(n,7173,23))},8730:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(2688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},8830:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return r}}),n(9154),n(5232),n(9651),n(8627),n(8866),n(5076),n(7936),n(7810);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8834:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let r=n(1550);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:n,query:i,hash:a}=(0,r.parsePath)(e);return""+t+n+i+a}},8866:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return f}});let r=n(9008),i=n(7391),a=n(6770),s=n(2030),o=n(5232),l=n(9435),u=n(1500),c=n(9752),d=n(6493),h=n(8214),p=n(2308);function f(e,t){let{origin:n}=t,f={},m=e.canonicalUrl,g=e.tree;f.preserveCustomHistoryState=!1;let y=(0,c.createEmptyCacheNode)(),v=(0,h.hasInterceptionRouteInCurrentTree)(e.tree);y.lazyData=(0,r.fetchServerResponse)(new URL(m,n),{flightRouterState:[g[0],g[1],g[2],"refetch"],nextUrl:v?e.nextUrl:null});let x=Date.now();return y.lazyData.then(async n=>{let{flightData:r,canonicalUrl:c}=n;if("string"==typeof r)return(0,o.handleExternalUrl)(e,f,r,e.pushRef.pendingPush);for(let n of(y.lazyData=null,r)){let{tree:r,seedData:l,head:h,isRootRender:b}=n;if(!b)return console.log("REFRESH FAILED"),e;let w=(0,a.applyRouterStatePatchToTree)([""],g,r,e.canonicalUrl);if(null===w)return(0,d.handleSegmentMismatch)(e,t,r);if((0,s.isNavigatingToNewRootLayout)(g,w))return(0,o.handleExternalUrl)(e,f,m,e.pushRef.pendingPush);let j=c?(0,i.createHrefFromUrl)(c):void 0;if(c&&(f.canonicalUrl=j),null!==l){let e=l[1],t=l[3];y.rsc=e,y.prefetchRsc=null,y.loading=t,(0,u.fillLazyItemsTillLeafWithHead)(x,y,void 0,r,l,h,void 0),f.prefetchCache=new Map}await (0,p.refreshInactiveParallelSegments)({navigatedAt:x,state:e,updatedTree:w,updatedCache:y,includeNextUrl:v,canonicalUrl:f.canonicalUrl||e.canonicalUrl}),f.cache=y,f.patchedTree=w,g=w}return(0,l.handleMutable)(e,f)},()=>e)}n(593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8947:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(2688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9285:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Store\\\\gym-website\\\\neogym\\\\src\\\\components\\\\sections\\\\Trainers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Store\\gym-website\\neogym\\src\\components\\sections\\Trainers.tsx","default")},9289:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return o},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return x}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,i=Array(r),a=0;a<r;a++)i[a]=arguments[a];return n||(n=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function s(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function o(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&u(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let h="undefined"!=typeof performance,p=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class f extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(e){return JSON.stringify({message:e.message,stack:e.stack})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9435:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return a}});let r=n(642);function i(e){return void 0!==e}function a(e,t){var n,a;let s=null==(n=t.shouldScroll)||n,o=e.nextUrl;if(i(t.patchedTree)){let n=(0,r.computeChangedPath)(e.tree,t.patchedTree);n?o=n:o||(o=e.canonicalUrl)}return{canonicalUrl:i(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:i(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:i(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:i(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!s&&(!!i(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:s?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:s?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:i(t.patchedTree)?t.patchedTree:e.tree,nextUrl:o}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9551:e=>{"use strict";e.exports=require("url")},9651:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let r=n(7391),i=n(6770),a=n(2030),s=n(5232),o=n(6928),l=n(9435),u=n(9752);function c(e,t){let{serverResponse:{flightData:n,canonicalUrl:c},navigatedAt:d}=t,h={};if(h.preserveCustomHistoryState=!1,"string"==typeof n)return(0,s.handleExternalUrl)(e,h,n,e.pushRef.pendingPush);let p=e.tree,f=e.cache;for(let t of n){let{segmentPath:n,tree:l}=t,m=(0,i.applyRouterStatePatchToTree)(["",...n],p,l,e.canonicalUrl);if(null===m)return e;if((0,a.isNavigatingToNewRootLayout)(p,m))return(0,s.handleExternalUrl)(e,h,e.canonicalUrl,e.pushRef.pendingPush);let g=c?(0,r.createHrefFromUrl)(c):void 0;g&&(h.canonicalUrl=g);let y=(0,u.createEmptyCacheNode)();(0,o.applyFlightData)(d,f,y,t),h.patchedTree=m,h.cache=y,f=y,p=m}return(0,l.handleMutable)(e,h)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9656:(e,t,n)=>{"use strict";n.r(t),n.d(t,{_:()=>i});var r=0;function i(e){return"__private_"+r+++"_"+e}},9707:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addSearchParamsToPageSegments:function(){return d},handleAliasedPrefetchEntry:function(){return c}});let r=n(3913),i=n(9752),a=n(6770),s=n(7391),o=n(3123),l=n(3898),u=n(9435);function c(e,t,n,c,h){let p,f=t.tree,m=t.cache,g=(0,s.createHrefFromUrl)(c);if("string"==typeof n)return!1;for(let t of n){if(!function e(t){if(!t)return!1;let n=t[2];if(t[3])return!0;for(let t in n)if(e(n[t]))return!0;return!1}(t.seedData))continue;let n=t.tree;n=d(n,Object.fromEntries(c.searchParams));let{seedData:s,isRootRender:u,pathToSegment:h}=t,y=["",...h];n=d(n,Object.fromEntries(c.searchParams));let v=(0,a.applyRouterStatePatchToTree)(y,f,n,g),x=(0,i.createEmptyCacheNode)();if(u&&s){let t=s[1];x.loading=s[3],x.rsc=t,function e(t,n,i,a,s){if(0!==Object.keys(a[1]).length)for(let l in a[1]){let u,c=a[1][l],d=c[0],h=(0,o.createRouterCacheKey)(d),p=null!==s&&void 0!==s[2][l]?s[2][l]:null;if(null!==p){let e=p[1],n=p[3];u={lazyData:null,rsc:d.includes(r.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else u={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let f=n.parallelRoutes.get(l);f?f.set(h,u):n.parallelRoutes.set(l,new Map([[h,u]])),e(t,u,i,c,p)}}(e,x,m,n,s)}else x.rsc=m.rsc,x.prefetchRsc=m.prefetchRsc,x.loading=m.loading,x.parallelRoutes=new Map(m.parallelRoutes),(0,l.fillCacheWithNewSubTreeDataButOnlyLoading)(e,x,m,t);v&&(f=v,m=x,p=!0)}return!!p&&(h.patchedTree=f,h.cache=m,h.canonicalUrl=g,h.hashFragment=c.hash,(0,u.handleMutable)(t,h))}function d(e,t){let[n,i,...a]=e;if(n.includes(r.PAGE_SEGMENT_KEY))return[(0,r.addSearchParamsIfPageSegment)(n,t),i,...a];let s={};for(let[e,n]of Object.entries(i))s[e]=d(n,t);return[n,s,...a]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9752:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createEmptyCacheNode:function(){return S},createPrefetchURL:function(){return A},default:function(){return O},isExternalURL:function(){return R}});let r=n(740),i=n(687),a=r._(n(3210)),s=n(2142),o=n(9154),l=n(7391),u=n(449),c=n(9129),d=r._(n(5656)),h=n(5416),p=n(6127),f=n(7022),m=n(7086),g=n(4397),y=n(9330),v=n(5942),x=n(6736),b=n(642),w=n(2776),j=n(3690),P=n(6875),T=n(7860);n(3406);let E={};function R(e){return e.origin!==window.location.origin}function A(e){let t;if((0,h.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return R(t)?null:t}function N(e){let{appRouterState:t}=e;return(0,a.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:r}=t,i={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==r?(n.pendingPush=!1,window.history.pushState(i,"",r)):window.history.replaceState(i,"",r)},[t]),(0,a.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function S(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function _(e){null==e&&(e={});let t=window.history.state,n=null==t?void 0:t.__NA;n&&(e.__NA=n);let r=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return r&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=r),e}function M(e){let{headCacheNode:t}=e,n=null!==t?t.head:null,r=null!==t?t.prefetchHead:null,i=null!==r?r:n;return(0,a.useDeferredValue)(n,i)}function C(e){let t,{actionQueue:n,assetPrefix:r,globalError:l}=e,h=(0,c.useActionQueue)(n),{canonicalUrl:p}=h,{searchParams:w,pathname:R}=(0,a.useMemo)(()=>{let e=new URL(p,"http://n");return{searchParams:e.searchParams,pathname:(0,x.hasBasePath)(e.pathname)?(0,v.removeBasePath)(e.pathname):e.pathname}},[p]);(0,a.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(E.pendingMpaPath=void 0,(0,c.dispatchAppRouterAction)({type:o.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,a.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,T.isRedirectError)(t)){e.preventDefault();let n=(0,P.getURLFromRedirectError)(t);(0,P.getRedirectTypeFromError)(t)===T.RedirectType.push?j.publicAppRouterInstance.push(n,{}):j.publicAppRouterInstance.replace(n,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:A}=h;if(A.mpaNavigation){if(E.pendingMpaPath!==p){let e=window.location;A.pendingPush?e.assign(p):e.replace(p),E.pendingMpaPath=p}(0,a.use)(y.unresolvedThenable)}(0,a.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),n=e=>{var t;let n=window.location.href,r=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,a.startTransition)(()=>{(0,c.dispatchAppRouterAction)({type:o.ACTION_RESTORE,url:new URL(null!=e?e:n,n),tree:r})})};window.history.pushState=function(t,r,i){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=_(t),i&&n(i)),e(t,r,i)},window.history.replaceState=function(e,r,i){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=_(e),i&&n(i)),t(e,r,i)};let r=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,a.startTransition)(()=>{(0,j.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",r),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",r)}},[]);let{cache:S,tree:C,nextUrl:O,focusAndScrollRef:k}=h,D=(0,a.useMemo)(()=>(0,g.findHeadInCache)(S,C[1]),[S,C]),L=(0,a.useMemo)(()=>(0,b.getSelectedParams)(C),[C]),U=(0,a.useMemo)(()=>({parentTree:C,parentCacheNode:S,parentSegmentPath:null,url:p}),[C,S,p]),V=(0,a.useMemo)(()=>({tree:C,focusAndScrollRef:k,nextUrl:O}),[C,k,O]);if(null!==D){let[e,n]=D;t=(0,i.jsx)(M,{headCacheNode:e},n)}else t=null;let F=(0,i.jsxs)(m.RedirectBoundary,{children:[t,S.rsc,(0,i.jsx)(f.AppRouterAnnouncer,{tree:C})]});return F=(0,i.jsx)(d.ErrorBoundary,{errorComponent:l[0],errorStyles:l[1],children:F}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(N,{appRouterState:h}),(0,i.jsx)(I,{}),(0,i.jsx)(u.PathParamsContext.Provider,{value:L,children:(0,i.jsx)(u.PathnameContext.Provider,{value:R,children:(0,i.jsx)(u.SearchParamsContext.Provider,{value:w,children:(0,i.jsx)(s.GlobalLayoutRouterContext.Provider,{value:V,children:(0,i.jsx)(s.AppRouterContext.Provider,{value:j.publicAppRouterInstance,children:(0,i.jsx)(s.LayoutRouterContext.Provider,{value:U,children:F})})})})})})]})}function O(e){let{actionQueue:t,globalErrorComponentAndStyles:[n,r],assetPrefix:a}=e;return(0,w.useNavFailureHandler)(),(0,i.jsx)(d.ErrorBoundary,{errorComponent:d.default,children:(0,i.jsx)(C,{actionQueue:t,assetPrefix:a,globalError:[n,r]})})}let k=new Set,D=new Set;function I(){let[,e]=a.default.useState(0),t=k.size;return(0,a.useEffect)(()=>{let n=()=>e(e=>e+1);return D.add(n),t!==k.size&&n(),()=>{D.delete(n)}},[t,e]),[...k].map((e,t)=>(0,i.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=k.size;return k.add(e),k.size!==t&&D.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[447,145],()=>n(427));module.exports=r})();