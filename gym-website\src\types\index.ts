// Common types for the gym website

export interface NavItem {
  name: string;
  href: string;
  icon?: string;
}

export interface Trainer {
  id: string;
  name: string;
  title: string;
  bio: string;
  image: string;
  specialties: string[];
  experience: string;
  certifications: string[];
  socialLinks: {
    instagram?: string;
    twitter?: string;
    linkedin?: string;
    facebook?: string;
  };
}

export interface Class {
  id: string;
  name: string;
  description: string;
  image: string;
  duration: number; // in minutes
  intensity: 'Low' | 'Medium' | 'High';
  category: 'Strength' | 'Cardio' | 'Flexibility' | 'HIIT' | 'Yoga' | 'CrossFit' | 'Boxing' | 'Dance';
  maxParticipants: number;
  equipment: string[];
  benefits: string[];
  trainer?: Trainer;
}

export interface PricingPlan {
  id: string;
  name: string;
  price: number;
  period: 'month' | 'year';
  description: string;
  features: string[];
  popular?: boolean;
  buttonText: string;
  buttonVariant: 'primary' | 'secondary' | 'accent';
}

export interface Testimonial {
  id: string;
  name: string;
  role?: string;
  content: string;
  image: string;
  rating: number;
  date: string;
}

export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  image: string;
  author: {
    name: string;
    image: string;
    bio: string;
  };
  category: 'Fitness' | 'Nutrition' | 'Motivation' | 'Lifestyle' | 'Training';
  tags: string[];
  publishedAt: string;
  readTime: number; // in minutes
  featured?: boolean;
}

export interface ContactInfo {
  address: string;
  phone: string;
  email: string;
  hours: {
    weekdays: string;
    weekends: string;
  };
  socialLinks: {
    instagram?: string;
    facebook?: string;
    twitter?: string;
    youtube?: string;
  };
}

export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
}

export interface TeamMember {
  id: string;
  name: string;
  position: string;
  bio: string;
  image: string;
  socialLinks: {
    instagram?: string;
    linkedin?: string;
    twitter?: string;
  };
}

export interface Facility {
  id: string;
  name: string;
  description: string;
  image: string;
  features: string[];
}

export interface Schedule {
  id: string;
  classId: string;
  trainerId: string;
  day: 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday' | 'Sunday';
  startTime: string;
  endTime: string;
  availableSpots: number;
}

// Component prop types
export interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'accent' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

export interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}

export interface SectionProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
  background?: 'white' | 'gray' | 'dark';
}
