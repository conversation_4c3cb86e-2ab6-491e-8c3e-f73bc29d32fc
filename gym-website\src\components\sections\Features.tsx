'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, Users, Clock, Trophy, Heart, Zap } from 'lucide-react';
import Section from '@/components/ui/Section';
import Card from '@/components/ui/Card';

const features = [
  {
    icon: Dumbbell,
    title: 'State-of-the-Art Equipment',
    description: 'Access to the latest fitness equipment from leading brands, maintained to the highest standards for optimal performance.',
  },
  {
    icon: Users,
    title: 'Expert Personal Trainers',
    description: 'Work with certified professionals who create personalized workout plans tailored to your goals and fitness level.',
  },
  {
    icon: Clock,
    title: '24/7 Access',
    description: 'Train on your schedule with round-the-clock access to our facilities, perfect for busy lifestyles.',
  },
  {
    icon: Trophy,
    title: 'Diverse Class Programs',
    description: 'From HIIT to yoga, boxing to dance, explore a wide variety of classes led by experienced instructors.',
  },
  {
    icon: Heart,
    title: 'Wellness & Recovery',
    description: 'Complete your fitness journey with our recovery services including massage therapy and wellness programs.',
  },
  {
    icon: Zap,
    title: 'High-Energy Environment',
    description: 'Train in a motivating atmosphere designed to push your limits and help you achieve breakthrough results.',
  },
];

const Features: React.FC = () => {
  return (
    <Section id="features" background="gray">
      <div className="text-center mb-16">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="heading-lg mb-4">
            Why Choose <span className="text-primary-500">EliteGym</span>?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience the difference with our premium facilities, expert guidance, 
            and comprehensive approach to fitness and wellness.
          </p>
        </motion.div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {features.map((feature, index) => {
          const Icon = feature.icon;
          return (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card hover className="p-8 h-full text-center group">
                <div className="flex items-center justify-center w-16 h-16 bg-primary-100 rounded-full mx-auto mb-6 group-hover:bg-primary-500 transition-colors duration-300">
                  <Icon className="w-8 h-8 text-primary-500 group-hover:text-white transition-colors duration-300" />
                </div>
                <h3 className="text-xl font-semibold mb-4 text-gray-900">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </Card>
            </motion.div>
          );
        })}
      </div>
    </Section>
  );
};

export default Features;
