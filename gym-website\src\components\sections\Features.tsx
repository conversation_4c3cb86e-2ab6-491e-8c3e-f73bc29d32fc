'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, Users, Clock, Trophy, Heart, Zap } from 'lucide-react';
import Section from '@/components/ui/Section';
import Card from '@/components/ui/Card';

const features = [
  {
    icon: Dumbbell,
    title: 'State-of-the-Art Equipment',
    description: 'Access to the latest fitness equipment from leading brands, maintained to the highest standards for optimal performance.',
  },
  {
    icon: Users,
    title: 'Expert Personal Trainers',
    description: 'Work with certified professionals who create personalized workout plans tailored to your goals and fitness level.',
  },
  {
    icon: Clock,
    title: '24/7 Access',
    description: 'Train on your schedule with round-the-clock access to our facilities, perfect for busy lifestyles.',
  },
  {
    icon: Trophy,
    title: 'Diverse Class Programs',
    description: 'From HIIT to yoga, boxing to dance, explore a wide variety of classes led by experienced instructors.',
  },
  {
    icon: Heart,
    title: 'Wellness & Recovery',
    description: 'Complete your fitness journey with our recovery services including massage therapy and wellness programs.',
  },
  {
    icon: Zap,
    title: 'High-Energy Environment',
    description: 'Train in a motivating atmosphere designed to push your limits and help you achieve breakthrough results.',
  },
];

const Features: React.FC = () => {
  return (
    <Section id="features" background="white" className="relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div
          className="w-full h-full"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        />
      </div>

      <div className="relative z-10">
        <div className="text-center mb-20">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-semibold mb-6">
              <span className="w-2 h-2 bg-primary-500 rounded-full mr-2"></span>
              Premium Features
            </div>
            <h2 className="heading-lg mb-6">
              Why Choose <span className="bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent">EliteGym</span>?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Experience the difference with our premium facilities, expert guidance,
              and comprehensive approach to fitness and wellness.
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card hover className="p-8 h-full text-center group border-0 shadow-xl hover:shadow-2xl bg-gradient-to-br from-white to-gray-50">
                  <div className="relative">
                    <div className="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl mx-auto mb-6 group-hover:from-primary-500 group-hover:to-primary-600 transition-all duration-500 shadow-lg group-hover:shadow-xl group-hover:scale-110">
                      <Icon className="w-10 h-10 text-primary-600 group-hover:text-white transition-all duration-500" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-accent-500 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 animate-pulse"></div>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-gray-900 group-hover:text-primary-700 transition-colors duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                    {feature.description}
                  </p>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>
    </Section>
  );
};

export default Features;
