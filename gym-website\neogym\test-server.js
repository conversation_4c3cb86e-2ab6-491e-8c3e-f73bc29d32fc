const http = require('http');

const server = http.createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'text/html' });
  res.end(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>NeoGym Test</title>
      <style>
        body { 
          font-family: Arial, sans-serif; 
          background: #0a0a0a; 
          color: white; 
          text-align: center; 
          padding: 50px; 
        }
        h1 { color: #3b82f6; }
      </style>
    </head>
    <body>
      <h1>🚀 NeoGym Server is Running!</h1>
      <p>The project is working correctly.</p>
      <p>Server running on port 3001</p>
    </body>
    </html>
  `);
});

const PORT = 3001;
server.listen(PORT, () => {
  console.log(`🚀 Test server running at http://localhost:${PORT}`);
});
