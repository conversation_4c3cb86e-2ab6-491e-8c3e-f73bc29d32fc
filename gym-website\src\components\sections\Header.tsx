'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Menu, <PERSON>, Du<PERSON><PERSON> } from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';

const navigation = [
  { name: 'Home', href: '/' },
  { name: 'About', href: '/about' },
  { name: 'Classes', href: '/classes' },
  { name: 'Trainers', href: '/trainers' },
  { name: 'Pricing', href: '/pricing' },
  { name: 'Contact', href: '/contact' },
];

const Header: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  return (
    <header
      className={cn(
        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',
        isScrolled
          ? 'bg-white/98 backdrop-blur-lg shadow-xl border-b border-gray-100'
          : 'bg-black/20 backdrop-blur-sm'
      )}
    >
      <nav className="container-custom">
        <div className="flex items-center justify-between h-20 md:h-24">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
              <Dumbbell className="w-7 h-7 text-white" />
            </div>
            <span
              className={cn(
                "text-2xl md:text-3xl font-bold transition-colors duration-300",
                isScrolled ? "text-gray-900" : "text-white"
              )}
              style={{fontFamily: 'var(--font-oswald), system-ui, sans-serif'}}
            >
              ELITE<span className="text-blue-500">GYM</span>
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'px-4 py-2 text-sm font-semibold rounded-lg transition-all duration-300 hover:bg-blue-50 hover:text-blue-600 relative group',
                  isScrolled ? 'text-gray-700' : 'text-white hover:bg-white/10'
                )}
              >
                {item.name}
                <span className="absolute bottom-0 left-1/2 w-0 h-0.5 bg-blue-500 transition-all duration-300 group-hover:w-full group-hover:left-0"></span>
              </Link>
            ))}
          </div>

          {/* CTA Button */}
          <div className="hidden md:flex items-center space-x-3">
            <Button variant="secondary" size="sm" className="hidden lg:block">
              Sign In
            </Button>
            <Button variant="primary" size="sm" className="shadow-lg hover:shadow-xl">
              Join Now
            </Button>
          </div>

          {/* Mobile menu button */}
          <button
            onClick={toggleMenu}
            className={cn(
              'lg:hidden p-3 rounded-xl transition-all duration-300 border',
              isScrolled
                ? 'text-gray-700 hover:bg-gray-50 border-gray-200'
                : 'text-white hover:bg-white/10 border-white/20'
            )}
          >
            {isOpen ? (
              <X className="w-5 h-5" />
            ) : (
              <Menu className="w-5 h-5" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <>
            {/* Backdrop */}
            <div
              className="lg:hidden fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
              onClick={() => setIsOpen(false)}
            />
            {/* Menu */}
            <div className="lg:hidden absolute top-full left-0 right-0 z-50">
              <div className="mx-4 mt-2">
                <div className="bg-white/98 backdrop-blur-lg rounded-2xl shadow-2xl border border-gray-100 overflow-hidden">
                  <div className="px-6 py-4 space-y-1">
                    {navigation.map((item) => (
                      <Link
                        key={item.name}
                        href={item.href}
                        className="block px-4 py-3 text-base font-semibold text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-xl transition-all duration-300 group"
                        onClick={() => setIsOpen(false)}
                      >
                        <span className="flex items-center justify-between">
                          {item.name}
                          <span className="w-2 h-2 bg-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                        </span>
                      </Link>
                    ))}
                    <div className="pt-4 space-y-3">
                      <Button variant="secondary" size="md" className="w-full">
                        Sign In
                      </Button>
                      <Button variant="primary" size="md" className="w-full shadow-lg">
                        Join Now
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </nav>
    </header>
  );
};

export default Header;
