{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\n\n// Utility function to merge class names\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n// Format currency\nexport function formatCurrency(amount: number, currency: string = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n}\n\n// Format date\nexport function formatDate(date: string | Date): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(dateObj);\n}\n\n// Format time\nexport function formatTime(time: string): string {\n  const [hours, minutes] = time.split(':');\n  const hour = parseInt(hours);\n  const ampm = hour >= 12 ? 'PM' : 'AM';\n  const displayHour = hour % 12 || 12;\n  return `${displayHour}:${minutes} ${ampm}`;\n}\n\n// Generate slug from string\nexport function generateSlug(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '');\n}\n\n// Truncate text\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength).trim() + '...';\n}\n\n// Validate email\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// Validate phone number (basic)\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n  return phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, ''));\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Throttle function\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\n// Scroll to element\nexport function scrollToElement(elementId: string, offset: number = 0): void {\n  const element = document.getElementById(elementId);\n  if (element) {\n    const elementPosition = element.getBoundingClientRect().top;\n    const offsetPosition = elementPosition + window.pageYOffset - offset;\n\n    window.scrollTo({\n      top: offsetPosition,\n      behavior: 'smooth',\n    });\n  }\n}\n\n// Check if element is in viewport\nexport function isInViewport(element: Element): boolean {\n  const rect = element.getBoundingClientRect();\n  return (\n    rect.top >= 0 &&\n    rect.left >= 0 &&\n    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&\n    rect.right <= (window.innerWidth || document.documentElement.clientWidth)\n  );\n}\n\n// Random number between min and max\nexport function randomBetween(min: number, max: number): number {\n  return Math.floor(Math.random() * (max - min + 1)) + min;\n}\n\n// Shuffle array\nexport function shuffleArray<T>(array: T[]): T[] {\n  const shuffled = [...array];\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\n  }\n  return shuffled;\n}\n\n// Get initials from name\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2);\n}\n\n// Calculate reading time\nexport function calculateReadingTime(text: string): number {\n  const wordsPerMinute = 200;\n  const wordCount = text.split(/\\s+/).length;\n  return Math.ceil(wordCount / wordsPerMinute);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAGO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,IAAI,KAAK,UAAU,CAAC,SAAS;QAClC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,WAAW,IAAY;IACrC,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC;IACpC,MAAM,OAAO,SAAS;IACtB,MAAM,OAAO,QAAQ,KAAK,OAAO;IACjC,MAAM,cAAc,OAAO,MAAM;IACjC,OAAO,GAAG,YAAY,CAAC,EAAE,QAAQ,CAAC,EAAE,MAAM;AAC5C;AAGO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,IAAI,KAAK;AAC3C;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,eAAe;AACtD;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAGO,SAAS,gBAAgB,SAAiB,EAAE,SAAiB,CAAC;IACnE,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;QAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;QAE9D,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;AACF;AAGO,SAAS,aAAa,OAAgB;IAC3C,MAAM,OAAO,QAAQ,qBAAqB;IAC1C,OACE,KAAK,GAAG,IAAI,KACZ,KAAK,IAAI,IAAI,KACb,KAAK,MAAM,IAAI,CAAC,OAAO,WAAW,IAAI,SAAS,eAAe,CAAC,YAAY,KAC3E,KAAK,KAAK,IAAI,CAAC,OAAO,UAAU,IAAI,SAAS,eAAe,CAAC,WAAW;AAE5E;AAGO,SAAS,cAAc,GAAW,EAAE,GAAW;IACpD,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,MAAM,MAAM,CAAC,KAAK;AACvD;AAGO,SAAS,aAAgB,KAAU;IACxC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IACzD;IACA,OAAO;AACT;AAGO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd;AAGO,SAAS,qBAAqB,IAAY;IAC/C,MAAM,iBAAiB;IACvB,MAAM,YAAY,KAAK,KAAK,CAAC,OAAO,MAAM;IAC1C,OAAO,KAAK,IAAI,CAAC,YAAY;AAC/B", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\nimport { ButtonProps } from '@/types';\n\nconst Button: React.FC<ButtonProps> = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  className,\n  onClick,\n  disabled = false,\n  type = 'button',\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-300 focus:outline-none focus:ring-4 disabled:opacity-50 disabled:cursor-not-allowed';\n  \n  const variantClasses = {\n    primary: 'bg-primary-500 hover:bg-primary-600 text-white focus:ring-primary-200 transform hover:scale-105',\n    secondary: 'bg-transparent border-2 border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white focus:ring-primary-200',\n    accent: 'bg-accent-500 hover:bg-accent-600 text-white focus:ring-accent-200 transform hover:scale-105',\n    outline: 'bg-transparent border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus:ring-gray-200',\n  };\n\n  const sizeClasses = {\n    sm: 'px-4 py-2 text-sm',\n    md: 'px-6 py-3 text-base',\n    lg: 'px-8 py-4 text-lg',\n  };\n\n  return (\n    <button\n      type={type}\n      className={cn(\n        baseClasses,\n        variantClasses[variant],\n        sizeClasses[size],\n        className\n      )}\n      onClick={onClick}\n      disabled={disabled}\n      {...props}\n    >\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMA,MAAM,SAAgC,CAAC,EACrC,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,SAAS,EACT,OAAO,EACP,WAAW,KAAK,EAChB,OAAO,QAAQ,EACf,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB;QAEF,SAAS;QACT,UAAU;QACT,GAAG,KAAK;kBAER;;;;;;AAGP;KAzCM;uCA2CS", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/src/components/sections/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Menu, <PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'About', href: '/about' },\n  { name: 'Classes', href: '/classes' },\n  { name: 'Trainers', href: '/trainers' },\n  { name: 'Pricing', href: '/pricing' },\n  { name: 'Contact', href: '/contact' },\n];\n\nconst Header: React.FC = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const toggleMenu = () => {\n    setIsOpen(!isOpen);\n  };\n\n  return (\n    <header\n      className={cn(\n        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',\n        isScrolled\n          ? 'bg-white/95 backdrop-blur-md shadow-lg'\n          : 'bg-transparent'\n      )}\n    >\n      <nav className=\"container-custom\">\n        <div className=\"flex items-center justify-between h-16 md:h-20\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"flex items-center justify-center w-10 h-10 bg-primary-500 rounded-lg\">\n              <Dumbbell className=\"w-6 h-6 text-white\" />\n            </div>\n            <span className=\"text-xl md:text-2xl font-bold font-display text-gray-900\">\n              ELITE<span className=\"text-primary-500\">GYM</span>\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  'text-sm font-medium transition-colors duration-200 hover:text-primary-500',\n                  isScrolled ? 'text-gray-700' : 'text-white'\n                )}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:block\">\n            <Button variant=\"primary\" size=\"sm\">\n              Join Now\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={toggleMenu}\n            className={cn(\n              'md:hidden p-2 rounded-lg transition-colors duration-200',\n              isScrolled\n                ? 'text-gray-700 hover:bg-gray-100'\n                : 'text-white hover:bg-white/10'\n            )}\n          >\n            {isOpen ? (\n              <X className=\"w-6 h-6\" />\n            ) : (\n              <Menu className=\"w-6 h-6\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 bg-white/95 backdrop-blur-md rounded-lg mt-2 shadow-lg\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-500 hover:bg-gray-50 rounded-md transition-colors duration-200\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"px-3 py-2\">\n                <Button variant=\"primary\" size=\"sm\" className=\"w-full\">\n                  Join Now\n                </Button>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAED,MAAM,SAAmB;;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,UAAU,CAAC;IACb;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,2CACA;kBAGN,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC;oCAAK,WAAU;;wCAA2D;sDACpE,6LAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;;;;;;;sCAK5C,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6EACA,aAAa,kBAAkB;8CAGhC,KAAK,IAAI;mCAPL,KAAK,IAAI;;;;;;;;;;sCAapB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAU,MAAK;0CAAK;;;;;;;;;;;sCAMtC,6LAAC;4BACC,SAAS;4BACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA,aACI,oCACA;sCAGL,uBACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,UAAU;8CAExB,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAQlB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvE;GAxGM;KAAA;uCA0GS", "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/src/components/sections/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { <PERSON><PERSON><PERSON>, MapPin, Phone, Mail, Instagram, Facebook, Twitter, Youtube } from 'lucide-react';\n\nconst Footer: React.FC = () => {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    company: [\n      { name: 'About Us', href: '/about' },\n      { name: 'Our Story', href: '/about#story' },\n      { name: 'Team', href: '/about#team' },\n      { name: 'Careers', href: '/careers' },\n    ],\n    services: [\n      { name: 'Classes', href: '/classes' },\n      { name: 'Personal Training', href: '/trainers' },\n      { name: 'Nutrition Coaching', href: '/nutrition' },\n      { name: 'Corporate Wellness', href: '/corporate' },\n    ],\n    support: [\n      { name: 'Contact Us', href: '/contact' },\n      { name: 'FAQ', href: '/faq' },\n      { name: 'Membership', href: '/pricing' },\n      { name: 'Blog', href: '/blog' },\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'Refund Policy', href: '/refund' },\n    ],\n  };\n\n  const socialLinks = [\n    { name: 'Instagram', href: '#', icon: Instagram },\n    { name: 'Facebook', href: '#', icon: Facebook },\n    { name: 'Twitter', href: '#', icon: Twitter },\n    { name: 'YouTube', href: '#', icon: Youtube },\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container-custom\">\n        {/* Main Footer Content */}\n        <div className=\"py-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <Link href=\"/\" className=\"flex items-center space-x-2 mb-6\">\n                <div className=\"flex items-center justify-center w-10 h-10 bg-primary-500 rounded-lg\">\n                  <Dumbbell className=\"w-6 h-6 text-white\" />\n                </div>\n                <span className=\"text-2xl font-bold font-display\">\n                  ELITE<span className=\"text-primary-500\">GYM</span>\n                </span>\n              </Link>\n              <p className=\"text-gray-400 mb-6 max-w-md\">\n                Transform your body and mind at EliteGym. Join our community of fitness enthusiasts \n                and achieve your goals with world-class facilities and expert trainers.\n              </p>\n              \n              {/* Contact Info */}\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3\">\n                  <MapPin className=\"w-5 h-5 text-primary-500\" />\n                  <span className=\"text-gray-400\">123 Fitness Street, Gym City, GC 12345</span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <Phone className=\"w-5 h-5 text-primary-500\" />\n                  <span className=\"text-gray-400\">+****************</span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <Mail className=\"w-5 h-5 text-primary-500\" />\n                  <span className=\"text-gray-400\"><EMAIL></span>\n                </div>\n              </div>\n            </div>\n\n            {/* Company Links */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-6\">Company</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.company.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-gray-400 hover:text-primary-500 transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Services Links */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-6\">Services</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.services.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-gray-400 hover:text-primary-500 transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Support Links */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-6\">Support</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.support.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-gray-400 hover:text-primary-500 transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Legal Links */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-6\">Legal</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.legal.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-gray-400 hover:text-primary-500 transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Footer */}\n        <div className=\"border-t border-gray-800 py-8\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\n            <p className=\"text-gray-400 text-sm\">\n              © {currentYear} EliteGym. All rights reserved.\n            </p>\n            \n            {/* Social Links */}\n            <div className=\"flex items-center space-x-4\">\n              {socialLinks.map((social) => {\n                const Icon = social.icon;\n                return (\n                  <Link\n                    key={social.name}\n                    href={social.href}\n                    className=\"text-gray-400 hover:text-primary-500 transition-colors duration-200\"\n                    aria-label={social.name}\n                  >\n                    <Icon className=\"w-5 h-5\" />\n                  </Link>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AAMA,MAAM,SAAmB;IACvB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAa,MAAM;YAAe;YAC1C;gBAAE,MAAM;gBAAQ,MAAM;YAAc;YACpC;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,UAAU;YACR;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAqB,MAAM;YAAY;YAC/C;gBAAE,MAAM;gBAAsB,MAAM;YAAa;YACjD;gBAAE,MAAM;gBAAsB,MAAM;YAAa;SAClD;QACD,SAAS;YACP;gBAAE,MAAM;gBAAc,MAAM;YAAW;YACvC;gBAAE,MAAM;gBAAO,MAAM;YAAO;YAC5B;gBAAE,MAAM;gBAAc,MAAM;YAAW;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAiB,MAAM;YAAU;SAC1C;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAa,MAAM;YAAK,MAAM,+MAAA,CAAA,YAAS;QAAC;QAChD;YAAE,MAAM;YAAY,MAAM;YAAK,MAAM,6MAAA,CAAA,WAAQ;QAAC;QAC9C;YAAE,MAAM;YAAW,MAAM;YAAK,MAAM,2MAAA,CAAA,UAAO;QAAC;QAC5C;YAAE,MAAM;YAAW,MAAM;YAAK,MAAM,2MAAA,CAAA,UAAO;QAAC;KAC7C;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,6LAAC;gDAAK,WAAU;;oDAAkC;kEAC3C,6LAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;kDAG5C,6LAAC;wCAAE,WAAU;kDAA8B;;;;;;kDAM3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;0CAMtC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAe5B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;oCAAwB;oCAChC;oCAAY;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC;oCAChB,MAAM,OAAO,OAAO,IAAI;oCACxB,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,OAAO,IAAI;wCACjB,WAAU;wCACV,cAAY,OAAO,IAAI;kDAEvB,cAAA,6LAAC;4CAAK,WAAU;;;;;;uCALX,OAAO,IAAI;;;;;gCAQtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd;KA7KM;uCA+KS", "debugId": null}}, {"offset": {"line": 928, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/src/components/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Header from '@/components/sections/Header';\nimport Footer from '@/components/sections/Footer';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children, className }) => {\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      <Header />\n      <main className={`flex-1 ${className || ''}`}>\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAWA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IAC5D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,2IAAA,CAAA,UAAM;;;;;0BACP,6LAAC;gBAAK,WAAW,CAAC,OAAO,EAAE,aAAa,IAAI;0BACzC;;;;;;0BAEH,6LAAC,2IAAA,CAAA,UAAM;;;;;;;;;;;AAGb;KAVM;uCAYS", "debugId": null}}, {"offset": {"line": 980, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/src/components/sections/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Play, ArrowRight } from 'lucide-react';\nimport Button from '@/components/ui/Button';\n\nconst Hero: React.FC = () => {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Image */}\n      <div className=\"absolute inset-0 z-0\">\n        <div \n          className=\"w-full h-full bg-cover bg-center bg-no-repeat\"\n          style={{\n            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url('https://images.unsplash.com/photo-1534438327276-14e5300c3a48?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')`\n          }}\n        />\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 container-custom text-center text-white\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"max-w-4xl mx-auto\"\n        >\n          {/* Badge */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            className=\"inline-flex items-center px-4 py-2 bg-primary-500/20 backdrop-blur-sm border border-primary-500/30 rounded-full text-primary-300 text-sm font-medium mb-6\"\n          >\n            <span className=\"w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse\"></span>\n            Premium Fitness Experience\n          </motion.div>\n\n          {/* Main Heading */}\n          <motion.h1\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.3 }}\n            className=\"heading-xl mb-6 leading-tight\"\n          >\n            Transform Your Body,{' '}\n            <span className=\"text-gradient bg-gradient-to-r from-primary-400 to-accent-400 bg-clip-text text-transparent\">\n              Elevate Your Mind\n            </span>\n          </motion.h1>\n\n          {/* Subtitle */}\n          <motion.p\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto leading-relaxed\"\n          >\n            Join EliteGym and discover a world-class fitness experience with state-of-the-art equipment, \n            expert trainers, and a community that pushes you to achieve your best.\n          </motion.p>\n\n          {/* CTA Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            className=\"flex flex-col sm:flex-row items-center justify-center gap-4 mb-12\"\n          >\n            <Button \n              variant=\"primary\" \n              size=\"lg\"\n              className=\"group\"\n            >\n              Start Your Journey\n              <ArrowRight className=\"ml-2 w-5 h-5 transition-transform group-hover:translate-x-1\" />\n            </Button>\n            \n            <Button \n              variant=\"secondary\" \n              size=\"lg\"\n              className=\"group bg-white/10 backdrop-blur-sm border-white/30 text-white hover:bg-white hover:text-gray-900\"\n            >\n              <Play className=\"mr-2 w-5 h-5\" />\n              Watch Our Story\n            </Button>\n          </motion.div>\n\n          {/* Stats */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            className=\"grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto\"\n          >\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold text-primary-400 mb-2\">5000+</div>\n              <div className=\"text-gray-300 text-sm uppercase tracking-wide\">Happy Members</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold text-primary-400 mb-2\">50+</div>\n              <div className=\"text-gray-300 text-sm uppercase tracking-wide\">Expert Trainers</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold text-primary-400 mb-2\">24/7</div>\n              <div className=\"text-gray-300 text-sm uppercase tracking-wide\">Access Available</div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 1, delay: 1 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\"\n      >\n        <div className=\"flex flex-col items-center text-white/70\">\n          <span className=\"text-sm mb-2 uppercase tracking-wide\">Scroll Down</span>\n          <div className=\"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center\">\n            <motion.div\n              animate={{ y: [0, 12, 0] }}\n              transition={{ duration: 1.5, repeat: Infinity }}\n              className=\"w-1 h-3 bg-white/50 rounded-full mt-2\"\n            />\n          </div>\n        </div>\n      </motion.div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AALA;;;;;AAOA,MAAM,OAAiB;IACrB,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,iBAAiB,CAAC,sOAAsO,CAAC;oBAC3P;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAK,WAAU;;;;;;gCAAgE;;;;;;;sCAKlF,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;gCACX;gCACsB;8CACrB,6LAAC;oCAAK,WAAU;8CAA8F;;;;;;;;;;;;sCAMhH,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAMD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC,qIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;wCACX;sDAEC,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAGxB,6LAAC,qIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAuD;;;;;;sDACtE,6LAAC;4CAAI,WAAU;sDAAgD;;;;;;;;;;;;8CAEjE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAuD;;;;;;sDACtE,6LAAC;4CAAI,WAAU;sDAAgD;;;;;;;;;;;;8CAEjE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAuD;;;;;;sDACtE,6LAAC;4CAAI,WAAU;sDAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAG,OAAO;gBAAE;gBACpC,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAuC;;;;;;sCACvD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACzB,YAAY;oCAAE,UAAU;oCAAK,QAAQ;gCAAS;gCAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB;KA7HM;uCA+HS", "debugId": null}}, {"offset": {"line": 1355, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/src/components/ui/Section.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\nimport { SectionProps } from '@/types';\n\nconst Section: React.FC<SectionProps> = ({\n  children,\n  className,\n  id,\n  background = 'white',\n  ...props\n}) => {\n  const backgroundClasses = {\n    white: 'bg-white',\n    gray: 'bg-gray-50',\n    dark: 'bg-gray-900 text-white',\n  };\n\n  return (\n    <section\n      id={id}\n      className={cn(\n        'section-padding',\n        backgroundClasses[background],\n        className\n      )}\n      {...props}\n    >\n      <div className=\"container-custom\">\n        {children}\n      </div>\n    </section>\n  );\n};\n\nexport default Section;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMA,MAAM,UAAkC,CAAC,EACvC,QAAQ,EACR,SAAS,EACT,EAAE,EACF,aAAa,OAAO,EACpB,GAAG,OACJ;IACC,MAAM,oBAAoB;QACxB,OAAO;QACP,MAAM;QACN,MAAM;IACR;IAEA,qBACE,6LAAC;QACC,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mBACA,iBAAiB,CAAC,WAAW,EAC7B;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;KA5BM;uCA8BS", "debugId": null}}, {"offset": {"line": 1400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\nimport { CardProps } from '@/types';\n\nconst Card: React.FC<CardProps> = ({\n  children,\n  className,\n  hover = false,\n  ...props\n}) => {\n  const baseClasses = 'bg-white rounded-xl shadow-lg overflow-hidden';\n  const hoverClasses = hover ? 'transition-all duration-300 transform hover:scale-105 hover:shadow-xl cursor-pointer' : '';\n\n  return (\n    <div\n      className={cn(baseClasses, hoverClasses, className)}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport default Card;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMA,MAAM,OAA4B,CAAC,EACjC,QAAQ,EACR,SAAS,EACT,QAAQ,KAAK,EACb,GAAG,OACJ;IACC,MAAM,cAAc;IACpB,MAAM,eAAe,QAAQ,yFAAyF;IAEtH,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,cAAc;QACxC,GAAG,KAAK;kBAER;;;;;;AAGP;KAjBM;uCAmBS", "debugId": null}}, {"offset": {"line": 1434, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/src/components/sections/Features.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { <PERSON><PERSON><PERSON>, Users, Clock, Trophy, Heart, Zap } from 'lucide-react';\nimport Section from '@/components/ui/Section';\nimport Card from '@/components/ui/Card';\n\nconst features = [\n  {\n    icon: Dumbbell,\n    title: 'State-of-the-Art Equipment',\n    description: 'Access to the latest fitness equipment from leading brands, maintained to the highest standards for optimal performance.',\n  },\n  {\n    icon: Users,\n    title: 'Expert Personal Trainers',\n    description: 'Work with certified professionals who create personalized workout plans tailored to your goals and fitness level.',\n  },\n  {\n    icon: Clock,\n    title: '24/7 Access',\n    description: 'Train on your schedule with round-the-clock access to our facilities, perfect for busy lifestyles.',\n  },\n  {\n    icon: Trophy,\n    title: 'Diverse Class Programs',\n    description: 'From HIIT to yoga, boxing to dance, explore a wide variety of classes led by experienced instructors.',\n  },\n  {\n    icon: Heart,\n    title: 'Wellness & Recovery',\n    description: 'Complete your fitness journey with our recovery services including massage therapy and wellness programs.',\n  },\n  {\n    icon: Zap,\n    title: 'High-Energy Environment',\n    description: 'Train in a motivating atmosphere designed to push your limits and help you achieve breakthrough results.',\n  },\n];\n\nconst Features: React.FC = () => {\n  return (\n    <Section id=\"features\" background=\"gray\">\n      <div className=\"text-center mb-16\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n        >\n          <h2 className=\"heading-lg mb-4\">\n            Why Choose <span className=\"text-primary-500\">EliteGym</span>?\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Experience the difference with our premium facilities, expert guidance, \n            and comprehensive approach to fitness and wellness.\n          </p>\n        </motion.div>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n        {features.map((feature, index) => {\n          const Icon = feature.icon;\n          return (\n            <motion.div\n              key={feature.title}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <Card hover className=\"p-8 h-full text-center group\">\n                <div className=\"flex items-center justify-center w-16 h-16 bg-primary-100 rounded-full mx-auto mb-6 group-hover:bg-primary-500 transition-colors duration-300\">\n                  <Icon className=\"w-8 h-8 text-primary-500 group-hover:text-white transition-colors duration-300\" />\n                </div>\n                <h3 className=\"text-xl font-semibold mb-4 text-gray-900\">\n                  {feature.title}\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  {feature.description}\n                </p>\n              </Card>\n            </motion.div>\n          );\n        })}\n      </div>\n    </Section>\n  );\n};\n\nexport default Features;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;AAQA,MAAM,WAAW;IACf;QACE,MAAM,6MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,mMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;IACf;CACD;AAED,MAAM,WAAqB;IACzB,qBACE,6LAAC,sIAAA,CAAA,UAAO;QAAC,IAAG;QAAW,YAAW;;0BAChC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,6LAAC;4BAAG,WAAU;;gCAAkB;8CACnB,6LAAC;oCAAK,WAAU;8CAAmB;;;;;;gCAAe;;;;;;;sCAE/D,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;0BAO3D,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS;oBACtB,MAAM,OAAO,QAAQ,IAAI;oBACzB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ;wBAAI;wBAChD,UAAU;4BAAE,MAAM;wBAAK;kCAEvB,cAAA,6LAAC,mIAAA,CAAA,UAAI;4BAAC,KAAK;4BAAC,WAAU;;8CACpB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;;;;;;;;;;;8CAElB,6LAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,6LAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;;;;;;uBAdnB,QAAQ,KAAK;;;;;gBAmBxB;;;;;;;;;;;;AAIR;KAhDM;uCAkDS", "debugId": null}}, {"offset": {"line": 1637, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/src/components/sections/CTA.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { ArrowRight, Star } from 'lucide-react';\nimport Button from '@/components/ui/Button';\nimport Section from '@/components/ui/Section';\n\nconst CTA: React.FC = () => {\n  return (\n    <Section background=\"dark\" className=\"relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0 bg-gradient-to-r from-primary-500/20 to-accent-500/20\" />\n        <div \n          className=\"absolute inset-0 bg-repeat opacity-30\"\n          style={{\n            backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n          }}\n        />\n      </div>\n\n      <div className=\"relative z-10 text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"max-w-4xl mx-auto\"\n        >\n          {/* Badge */}\n          <div className=\"inline-flex items-center px-4 py-2 bg-primary-500/20 backdrop-blur-sm border border-primary-500/30 rounded-full text-primary-300 text-sm font-medium mb-6\">\n            <Star className=\"w-4 h-4 mr-2 fill-current\" />\n            Join 5000+ Happy Members\n          </div>\n\n          {/* Heading */}\n          <h2 className=\"heading-lg mb-6 text-white\">\n            Ready to Transform Your{' '}\n            <span className=\"text-gradient bg-gradient-to-r from-primary-400 to-accent-400 bg-clip-text text-transparent\">\n              Fitness Journey\n            </span>\n            ?\n          </h2>\n\n          {/* Description */}\n          <p className=\"text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed\">\n            Start your transformation today with a free trial. Experience our world-class facilities, \n            expert trainers, and supportive community.\n          </p>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4 mb-12\">\n            <Button \n              variant=\"primary\" \n              size=\"lg\"\n              className=\"group\"\n            >\n              Start Free Trial\n              <ArrowRight className=\"ml-2 w-5 h-5 transition-transform group-hover:translate-x-1\" />\n            </Button>\n            \n            <Button \n              variant=\"secondary\" \n              size=\"lg\"\n              className=\"bg-white/10 backdrop-blur-sm border-white/30 text-white hover:bg-white hover:text-gray-900\"\n            >\n              Schedule Tour\n            </Button>\n          </div>\n\n          {/* Trust Indicators */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto text-center\">\n            <div>\n              <div className=\"text-2xl font-bold text-primary-400 mb-1\">No Commitment</div>\n              <div className=\"text-gray-400 text-sm\">Cancel anytime</div>\n            </div>\n            <div>\n              <div className=\"text-2xl font-bold text-primary-400 mb-1\">Free Trial</div>\n              <div className=\"text-gray-400 text-sm\">7 days included</div>\n            </div>\n            <div>\n              <div className=\"text-2xl font-bold text-primary-400 mb-1\">Expert Support</div>\n              <div className=\"text-gray-400 text-sm\">Personal guidance</div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </Section>\n  );\n};\n\nexport default CTA;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AACA;AANA;;;;;;AAQA,MAAM,MAAgB;IACpB,qBACE,6LAAC,sIAAA,CAAA,UAAO;QAAC,YAAW;QAAO,WAAU;;0BAEnC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,CAAC,gQAAgQ,CAAC;wBACrR;;;;;;;;;;;;0BAIJ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;sCAKhD,6LAAC;4BAAG,WAAU;;gCAA6B;gCACjB;8CACxB,6LAAC;oCAAK,WAAU;8CAA8F;;;;;;gCAEvG;;;;;;;sCAKT,6LAAC;4BAAE,WAAU;sCAA+D;;;;;;sCAM5E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;wCACX;sDAEC,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAGxB,6LAAC,qIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAA2C;;;;;;sDAC1D,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAA2C;;;;;;sDAC1D,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAA2C;;;;;;sDAC1D,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrD;KAlFM;uCAoFS", "debugId": null}}]}