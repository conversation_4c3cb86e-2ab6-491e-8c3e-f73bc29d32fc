'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X, Zap, Activity, Users, Calendar, CreditCard, Phone } from 'lucide-react';

const Header = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { name: 'Home', href: '/', icon: Zap },
    { name: 'About', href: '/about', icon: Activity },
    { name: 'Classes', href: '/classes', icon: Users },
    { name: 'Trainers', href: '/trainers', icon: Calendar },
    { name: 'Membership', href: '/membership', icon: CreditCard },
    { name: 'Contact', href: '/contact', icon: Phone },
  ];

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled ? 'holo-glass backdrop-blur-xl' : 'bg-transparent'
      }`}
    >
      <nav className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          {/* Futuristic Logo */}
          <Link href="/" className="flex items-center space-x-4 group">
            <div className="relative">
              <div className="w-14 h-14 bg-gradient-to-br from-cyan-400 to-purple-600 rounded-xl flex items-center justify-center relative overflow-hidden">
                <Zap className="w-8 h-8 text-white z-10" />
                <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/20 to-purple-600/20 animate-pulse"></div>
                <div className="absolute top-0 left-0 w-full h-px bg-cyan-400"></div>
                <div className="absolute bottom-0 right-0 w-full h-px bg-purple-400"></div>
              </div>
              <div className="absolute -inset-1 bg-gradient-to-r from-cyan-400 to-purple-600 rounded-xl opacity-30 blur-lg group-hover:opacity-60 transition-opacity duration-300"></div>
            </div>
            <div className="flex flex-col">
              <span className="font-orbitron text-2xl font-black text-neon-cyan leading-none">
                NEO
              </span>
              <span className="font-orbitron text-lg font-bold text-neon-purple leading-none">
                GYM
              </span>
            </div>
          </Link>

          {/* Futuristic Navigation */}
          <div className="hidden md:flex items-center space-x-2">
            {navItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className="group relative flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-300 hover:bg-cyan-400/10 border border-transparent hover:border-cyan-400/30"
                >
                  <Icon className="w-4 h-4 text-gray-400 group-hover:text-neon-cyan transition-colors duration-300" />
                  <span className="font-orbitron text-sm font-medium text-gray-300 group-hover:text-neon-cyan transition-colors duration-300 tracking-wider">
                    {item.name}
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/0 via-cyan-400/10 to-cyan-400/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
                </Link>
              );
            })}
          </div>

          {/* Futuristic CTA Button */}
          <div className="hidden md:block">
            <button className="cyber-btn">
              <span className="relative z-10">NEURAL LINK</span>
            </button>
          </div>

          {/* Futuristic Mobile Menu Button */}
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="md:hidden p-3 rounded-lg holo-glass hover:bg-cyan-400/10 transition-all duration-300 border border-cyan-400/30"
          >
            {isOpen ? (
              <X className="w-6 h-6 text-neon-cyan" />
            ) : (
              <Menu className="w-6 h-6 text-neon-cyan" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden overflow-hidden"
            >
              <div className="holo-card mt-4 p-6 space-y-4">
                {navItems.map((item, index) => {
                  const Icon = item.icon;
                  return (
                    <motion.div
                      key={item.name}
                      initial={{ x: -20, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Link
                        href={item.href}
                        onClick={() => setIsOpen(false)}
                        className="flex items-center space-x-3 p-3 rounded-lg hover:bg-cyan-400/10 transition-all duration-300 group border border-transparent hover:border-cyan-400/30"
                      >
                        <Icon className="w-5 h-5 text-neon-cyan" />
                        <span className="font-orbitron font-medium text-white group-hover:text-neon-cyan transition-colors duration-300 tracking-wider">
                          {item.name}
                        </span>
                      </Link>
                    </motion.div>
                  );
                })}
                <motion.div
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: navItems.length * 0.1 }}
                  className="pt-4 border-t border-cyan-400/30"
                >
                  <button className="cyber-btn w-full">
                    <span className="relative z-10">NEURAL LINK</span>
                  </button>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </nav>
    </motion.header>
  );
};

export default Header;
