@import "tailwindcss";

@theme {
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;

  --color-accent-50: #fef2f2;
  --color-accent-100: #fee2e2;
  --color-accent-200: #fecaca;
  --color-accent-300: #fca5a5;
  --color-accent-400: #f87171;
  --color-accent-500: #ef4444;
  --color-accent-600: #dc2626;
  --color-accent-700: #b91c1c;
  --color-accent-800: #991b1b;
  --color-accent-900: #7f1d1d;

  --font-family-display: '<PERSON>', system-ui, sans-serif;
  --font-family-sans: 'Inter', system-ui, sans-serif;
}

:root {
  --background: #ffffff;
  --foreground: #0f172a;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f8fafc;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

/* Selection styles */
::selection {
  background: var(--primary);
  color: white;
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Button and link hover effects */
.btn-primary {
  @apply bg-primary-500 hover:bg-primary-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 focus:ring-4 focus:ring-primary-200;
}

.btn-secondary {
  @apply bg-transparent border-2 border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300;
}

.btn-accent {
  @apply bg-accent-500 hover:bg-accent-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 focus:ring-4 focus:ring-accent-200;
}

/* Text gradient effects */
.text-gradient {
  @apply bg-gradient-to-r from-primary-500 to-accent-500 bg-clip-text text-transparent;
}

.text-gradient-dark {
  @apply bg-gradient-to-r from-gray-700 to-gray-900 bg-clip-text text-transparent;
}

/* Card hover effects */
.card-hover {
  @apply transition-all duration-300 transform hover:scale-105 hover:shadow-xl;
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Responsive typography */
.heading-xl {
  @apply text-4xl md:text-5xl lg:text-6xl font-bold;
  font-family: var(--font-oswald), system-ui, sans-serif;
}

.heading-lg {
  @apply text-3xl md:text-4xl lg:text-5xl font-bold;
  font-family: var(--font-oswald), system-ui, sans-serif;
}

.heading-md {
  @apply text-2xl md:text-3xl lg:text-4xl font-bold;
  font-family: var(--font-oswald), system-ui, sans-serif;
}

.heading-sm {
  @apply text-xl md:text-2xl lg:text-3xl font-semibold;
  font-family: var(--font-oswald), system-ui, sans-serif;
}

/* Container utilities */
.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.section-padding {
  @apply py-16 md:py-20 lg:py-24;
}
