@import "tailwindcss";

/* CSS Variables for Dynamic Theming */
:root {
  --background: #ffffff;
  --foreground: #0a0a0a;
  --surface: #f5f5f5;
  --surface-elevated: #ffffff;
  --border: #8a8a8a;
  --border-light: #e5e7eb;
  --text-primary: #0a0a0a;
  --text-secondary: #3a3a3a;
  --text-muted: #8a8a8a;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ffffff;
    --surface: #1a1a1a;
    --surface-elevated: #2a2a2a;
    --border: #3a3a3a;
    --border-light: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #f5f5f5;
    --text-muted: #8a8a8a;
  }
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 7rem;
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-family-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  cursor: pointer;
}

/* Remove default link styles */
a {
  color: inherit;
  text-decoration: none;
}

/* Image optimization */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Focus management */
:focus {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Elite Scrollbar Design */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface);
  border-radius: var(--radius-lg);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-accent-500));
  border-radius: var(--radius-lg);
  border: 1px solid var(--surface);
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-accent-600));
  transform: scale(1.1);
}

::-webkit-scrollbar-corner {
  background: var(--surface);
}

/* Elite Selection Styles */
::selection {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-accent-500));
  color: var(--color-elite-white);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

::-moz-selection {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-accent-500));
  color: var(--color-elite-white);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Advanced Focus Management */
.focus-ring {
  @apply focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:ring-offset-white focus-visible:outline-none;
}

.focus-ring-dark {
  @apply focus-visible:ring-2 focus-visible:ring-primary-400 focus-visible:ring-offset-2 focus-visible:ring-offset-gray-900 focus-visible:outline-none;
}

/* Elite Button System */
.btn-elite-primary {
  @apply relative overflow-hidden bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 focus:ring-4 focus:ring-primary-300 shadow-lg hover:shadow-xl;
}

.btn-elite-secondary {
  @apply relative overflow-hidden bg-transparent border-2 border-primary-500 text-primary-600 hover:bg-primary-500 hover:text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 focus:ring-4 focus:ring-primary-200 shadow-md hover:shadow-lg;
}

.btn-elite-accent {
  @apply relative overflow-hidden bg-gradient-to-r from-accent-500 to-accent-600 hover:from-accent-600 hover:to-accent-700 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 focus:ring-4 focus:ring-accent-300 shadow-lg hover:shadow-xl;
}

.btn-elite-ghost {
  @apply relative bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 font-semibold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 focus:ring-4 focus:ring-white/30 shadow-lg hover:shadow-lg;
}

/* Elite Text Effects */
.text-gradient-elite {
  @apply bg-gradient-to-r from-primary-400 via-primary-500 to-accent-500 bg-clip-text text-transparent;
}

.text-gradient-gold {
  @apply bg-gradient-to-r from-yellow-400 via-yellow-500 to-orange-500 bg-clip-text text-transparent;
}

.text-shadow-elite {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3), 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Elite Card System */
.card-elite {
  @apply bg-white/95 backdrop-blur-lg border border-white/20 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-2;
}

.card-elite-glass {
  @apply bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-2;
}

.card-elite-feature {
  @apply relative overflow-hidden bg-gradient-to-br from-white to-gray-50 border border-gray-200/50 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-2;
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Elite Typography System */
.heading-hero {
  @apply text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-black leading-none tracking-tight font-display;
}

.heading-display {
  @apply text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight font-display;
}

.heading-xl {
  @apply text-3xl md:text-4xl lg:text-5xl font-bold leading-tight tracking-tight font-display;
}

.heading-lg {
  @apply text-2xl md:text-3xl lg:text-4xl font-bold leading-tight tracking-tight font-display;
}

.heading-md {
  @apply text-xl md:text-2xl lg:text-3xl font-bold leading-tight tracking-tight font-display;
}

.heading-sm {
  @apply text-lg md:text-xl lg:text-2xl font-semibold leading-tight tracking-tight font-display;
}

.text-body-lg {
  @apply text-lg md:text-xl leading-relaxed;
}

.text-body {
  @apply text-base md:text-lg leading-relaxed;
}

/* Elite Layout System */
.container-elite {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.section-elite {
  @apply py-16 sm:py-20 md:py-24 lg:py-32;
}

.grid-elite-features {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12;
}
