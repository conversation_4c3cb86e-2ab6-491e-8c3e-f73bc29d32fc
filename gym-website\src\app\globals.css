@import "tailwindcss";

/* CSS Variables for Dynamic Theming */
:root {
  --background: #ffffff;
  --foreground: #0a0a0a;
  --primary: #0ea5e9;
  --primary-dark: #0284c7;
  --accent: #f97316;
  --accent-dark: #ea580c;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 7rem;
}

body {
  background: var(--background);
  color: var(--foreground);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Focus management */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #0ea5e9, #f97316);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #0284c7, #ea580c);
}

/* Selection styles */
::selection {
  background: linear-gradient(135deg, #0ea5e9, #f97316);
  color: white;
}

/* Container utilities */
.container-custom {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container-custom {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-custom {
    padding: 0 2rem;
  }
}

.section-padding {
  padding: 4rem 0;
}

@media (min-width: 768px) {
  .section-padding {
    padding: 5rem 0;
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding: 6rem 0;
  }
}
