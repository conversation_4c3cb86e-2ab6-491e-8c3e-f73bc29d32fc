@import "tailwindcss";

@theme {
  /* Elite Color Palette */
  --color-elite-black: #0a0a0a;
  --color-elite-charcoal: #1a1a1a;
  --color-elite-dark: #2a2a2a;
  --color-elite-gray: #3a3a3a;
  --color-elite-silver: #8a8a8a;
  --color-elite-light: #f5f5f5;
  --color-elite-white: #ffffff;

  /* Primary Brand Colors */
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;

  /* Accent Colors */
  --color-accent-50: #fff7ed;
  --color-accent-100: #ffedd5;
  --color-accent-200: #fed7aa;
  --color-accent-300: #fdba74;
  --color-accent-400: #fb923c;
  --color-accent-500: #f97316;
  --color-accent-600: #ea580c;
  --color-accent-700: #c2410c;
  --color-accent-800: #9a3412;
  --color-accent-900: #7c2d12;

  /* Success Colors */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;

  /* Typography */
  --font-family-display: 'Oswald', system-ui, sans-serif;
  --font-family-sans: 'Inter', system-ui, sans-serif;
  --font-family-mono: 'JetBrains Mono', monospace;

  /* Spacing Scale */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;
  --spacing-5xl: 8rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-3xl: 2rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}

/* CSS Variables for Dynamic Theming */
:root {
  --background: var(--color-elite-white);
  --foreground: var(--color-elite-black);
  --surface: var(--color-elite-light);
  --surface-elevated: var(--color-elite-white);
  --border: var(--color-elite-silver);
  --border-light: #e5e7eb;
  --text-primary: var(--color-elite-black);
  --text-secondary: var(--color-elite-gray);
  --text-muted: var(--color-elite-silver);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--color-elite-black);
    --foreground: var(--color-elite-white);
    --surface: var(--color-elite-charcoal);
    --surface-elevated: var(--color-elite-dark);
    --border: var(--color-elite-gray);
    --border-light: var(--color-elite-dark);
    --text-primary: var(--color-elite-white);
    --text-secondary: var(--color-elite-light);
    --text-muted: var(--color-elite-silver);
  }
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 7rem;
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-family-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  cursor: pointer;
}

/* Remove default link styles */
a {
  color: inherit;
  text-decoration: none;
}

/* Image optimization */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Focus management */
:focus {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Elite Scrollbar Design */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface);
  border-radius: var(--radius-lg);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-accent-500));
  border-radius: var(--radius-lg);
  border: 1px solid var(--surface);
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-accent-600));
  transform: scale(1.1);
}

::-webkit-scrollbar-corner {
  background: var(--surface);
}

/* Elite Selection Styles */
::selection {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-accent-500));
  color: var(--color-elite-white);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

::-moz-selection {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-accent-500));
  color: var(--color-elite-white);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Advanced Focus Management */
.focus-ring {
  @apply focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:ring-offset-white focus-visible:outline-none;
}

.focus-ring-dark {
  @apply focus-visible:ring-2 focus-visible:ring-primary-400 focus-visible:ring-offset-2 focus-visible:ring-offset-gray-900 focus-visible:outline-none;
}

/* Elite Button System */
.btn-elite-primary {
  @apply relative overflow-hidden bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-bold py-4 px-8 rounded-xl transition-all duration-500 transform hover:scale-[1.02] hover:-translate-y-1 focus:ring-4 focus:ring-primary-300 shadow-lg hover:shadow-2xl;
}

.btn-elite-primary::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300;
}

.btn-elite-primary:hover::before {
  @apply opacity-100;
}

.btn-elite-secondary {
  @apply relative overflow-hidden bg-transparent border-2 border-primary-500 text-primary-600 hover:text-white font-bold py-4 px-8 rounded-xl transition-all duration-500 transform hover:scale-[1.02] hover:-translate-y-1 focus:ring-4 focus:ring-primary-200 shadow-md hover:shadow-xl;
}

.btn-elite-secondary::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-primary-500 to-primary-600 transform scale-x-0 origin-left transition-transform duration-500;
}

.btn-elite-secondary:hover::before {
  @apply scale-x-100;
}

.btn-elite-accent {
  @apply relative overflow-hidden bg-gradient-to-r from-accent-500 to-accent-600 hover:from-accent-600 hover:to-accent-700 text-white font-bold py-4 px-8 rounded-xl transition-all duration-500 transform hover:scale-[1.02] hover:-translate-y-1 focus:ring-4 focus:ring-accent-300 shadow-lg hover:shadow-2xl;
}

.btn-elite-ghost {
  @apply relative bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 font-semibold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-[1.02] hover:-translate-y-1 focus:ring-4 focus:ring-white/30 shadow-lg hover:shadow-xl;
}

/* Elite Text Effects */
.text-gradient-elite {
  @apply bg-gradient-to-r from-primary-400 via-primary-500 to-accent-500 bg-clip-text text-transparent;
}

.text-gradient-gold {
  @apply bg-gradient-to-r from-yellow-400 via-yellow-500 to-orange-500 bg-clip-text text-transparent;
}

.text-gradient-silver {
  @apply bg-gradient-to-r from-gray-300 via-gray-400 to-gray-500 bg-clip-text text-transparent;
}

.text-shadow-elite {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3), 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Elite Card System */
.card-elite {
  @apply bg-white/95 backdrop-blur-lg border border-white/20 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.02] hover:-translate-y-2;
}

.card-elite-dark {
  @apply bg-gray-900/95 backdrop-blur-lg border border-gray-700/50 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.02] hover:-translate-y-2;
}

.card-elite-glass {
  @apply bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.02] hover:-translate-y-2;
}

.card-elite-feature {
  @apply relative overflow-hidden bg-gradient-to-br from-white to-gray-50 border border-gray-200/50 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.02] hover:-translate-y-2 group;
}

.card-elite-feature::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-br from-primary-500/5 to-accent-500/5 opacity-0 transition-opacity duration-500;
}

.card-elite-feature:hover::before {
  @apply opacity-100;
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Elite Typography System */
.heading-hero {
  @apply text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-black leading-none tracking-tight;
  font-family: var(--font-family-display);
}

.heading-display {
  @apply text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight;
  font-family: var(--font-family-display);
}

.heading-xl {
  @apply text-3xl md:text-4xl lg:text-5xl font-bold leading-tight tracking-tight;
  font-family: var(--font-family-display);
}

.heading-lg {
  @apply text-2xl md:text-3xl lg:text-4xl font-bold leading-tight tracking-tight;
  font-family: var(--font-family-display);
}

.heading-md {
  @apply text-xl md:text-2xl lg:text-3xl font-bold leading-tight tracking-tight;
  font-family: var(--font-family-display);
}

.heading-sm {
  @apply text-lg md:text-xl lg:text-2xl font-semibold leading-tight tracking-tight;
  font-family: var(--font-family-display);
}

.text-body-lg {
  @apply text-lg md:text-xl leading-relaxed;
}

.text-body {
  @apply text-base md:text-lg leading-relaxed;
}

.text-body-sm {
  @apply text-sm md:text-base leading-relaxed;
}

.text-caption {
  @apply text-xs md:text-sm leading-normal font-medium uppercase tracking-wider;
}

/* Elite Layout System */
.container-elite {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12;
}

.container-elite-narrow {
  @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
}

.container-elite-wide {
  @apply max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12;
}

.section-elite {
  @apply py-16 sm:py-20 md:py-24 lg:py-32;
}

.section-elite-sm {
  @apply py-12 sm:py-16 md:py-20 lg:py-24;
}

.section-elite-lg {
  @apply py-20 sm:py-24 md:py-32 lg:py-40;
}

/* Elite Grid System */
.grid-elite-auto {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8 lg:gap-10;
}

.grid-elite-features {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12;
}

.grid-elite-testimonials {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8;
}

.grid-elite-pricing {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12 max-w-6xl mx-auto;
}

/* Elite Flex Utilities */
.flex-elite-center {
  @apply flex items-center justify-center;
}

.flex-elite-between {
  @apply flex items-center justify-between;
}

.flex-elite-col-center {
  @apply flex flex-col items-center justify-center;
}

/* Elite Spacing */
.space-elite-y > * + * {
  @apply mt-6 md:mt-8 lg:mt-12;
}

.space-elite-x > * + * {
  @apply ml-6 md:ml-8 lg:ml-12;
}
