'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { SectionProps } from '@/types';

const Section: React.FC<SectionProps> = ({
  children,
  className,
  id,
  background = 'white',
  ...props
}) => {
  const backgroundClasses = {
    white: 'bg-white',
    gray: 'bg-gray-50',
    dark: 'bg-gray-900 text-white',
  };

  return (
    <section
      id={id}
      className={cn(
        'section-padding',
        backgroundClasses[background],
        className
      )}
      {...props}
    >
      <div className="container-custom">
        {children}
      </div>
    </section>
  );
};

export default Section;
