import { Trainer, Class, PricingPlan, Testimonial, BlogPost, TeamMember } from '@/types';

export const trainers: Trainer[] = [
  {
    id: '1',
    name: '<PERSON>',
    title: 'Head Personal Trainer',
    bio: 'With over 8 years of experience in fitness training, <PERSON> specializes in strength training and functional movement. She holds certifications in NASM-CPT and FMS.',
    image: 'https://images.unsplash.com/photo-1594736797933-d0501ba2fe65?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
    specialties: ['Strength Training', 'Functional Movement', 'Weight Loss', 'Injury Prevention'],
    experience: '8+ years',
    certifications: ['NASM-CPT', 'FMS Level 2', 'Precision Nutrition Level 1'],
    socialLinks: {
      instagram: 'https://instagram.com/sarahfitness',
      linkedin: 'https://linkedin.com/in/sarahjohnson',
    },
  },
  {
    id: '2',
    name: '<PERSON>',
    title: 'CrossFit Coach',
    bio: 'Former competitive athlete turned CrossFit coach. <PERSON> brings high-energy training sessions and helps athletes push their limits safely.',
    image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
    specialties: ['CrossFit', 'Olympic Lifting', 'HIIT', 'Athletic Performance'],
    experience: '6+ years',
    certifications: ['CrossFit Level 2', 'USAW Sports Performance Coach', 'CPR/AED'],
    socialLinks: {
      instagram: 'https://instagram.com/miketrains',
      twitter: 'https://twitter.com/mikerodriguez',
    },
  },
  {
    id: '3',
    name: 'Emma Chen',
    title: 'Yoga & Wellness Instructor',
    bio: 'Emma combines traditional yoga practices with modern wellness techniques to help clients achieve balance in body and mind.',
    image: 'https://images.unsplash.com/photo-1506629905607-d9c8e3b8e0d4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
    specialties: ['Hatha Yoga', 'Vinyasa Flow', 'Meditation', 'Stress Management'],
    experience: '5+ years',
    certifications: ['RYT-500', 'Meditation Teacher Training', 'Reiki Level 2'],
    socialLinks: {
      instagram: 'https://instagram.com/emmayoga',
      facebook: 'https://facebook.com/emmachen.yoga',
    },
  },
];

export const classes: Class[] = [
  {
    id: '1',
    name: 'HIIT Blast',
    description: 'High-intensity interval training that burns calories and builds endurance in just 45 minutes.',
    image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    duration: 45,
    intensity: 'High',
    category: 'HIIT',
    maxParticipants: 20,
    equipment: ['Kettlebells', 'Battle Ropes', 'Medicine Balls', 'Resistance Bands'],
    benefits: ['Burns Fat', 'Improves Cardiovascular Health', 'Builds Endurance', 'Time Efficient'],
  },
  {
    id: '2',
    name: 'Strength & Power',
    description: 'Build muscle and increase strength with compound movements and progressive overload.',
    image: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    duration: 60,
    intensity: 'High',
    category: 'Strength',
    maxParticipants: 15,
    equipment: ['Barbells', 'Dumbbells', 'Power Racks', 'Benches'],
    benefits: ['Builds Muscle', 'Increases Strength', 'Improves Bone Density', 'Boosts Metabolism'],
  },
  {
    id: '3',
    name: 'Yoga Flow',
    description: 'Dynamic yoga sequences that improve flexibility, balance, and mental clarity.',
    image: 'https://images.unsplash.com/photo-1506629905607-d9c8e3b8e0d4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    duration: 75,
    intensity: 'Low',
    category: 'Yoga',
    maxParticipants: 25,
    equipment: ['Yoga Mats', 'Blocks', 'Straps', 'Bolsters'],
    benefits: ['Improves Flexibility', 'Reduces Stress', 'Enhances Balance', 'Promotes Mindfulness'],
  },
];

export const pricingPlans: PricingPlan[] = [
  {
    id: '1',
    name: 'Basic',
    price: 29,
    period: 'month',
    description: 'Perfect for getting started with your fitness journey',
    features: [
      'Access to gym equipment',
      'Locker room access',
      'Basic fitness assessment',
      'Mobile app access',
      'Community support',
    ],
    buttonText: 'Get Started',
    buttonVariant: 'outline',
  },
  {
    id: '2',
    name: 'Premium',
    price: 59,
    period: 'month',
    description: 'Most popular plan with comprehensive features',
    features: [
      'Everything in Basic',
      'Unlimited group classes',
      '2 personal training sessions/month',
      'Nutrition consultation',
      'Priority booking',
      'Guest passes (2/month)',
    ],
    popular: true,
    buttonText: 'Most Popular',
    buttonVariant: 'primary',
  },
  {
    id: '3',
    name: 'Elite',
    price: 99,
    period: 'month',
    description: 'Ultimate fitness experience with premium perks',
    features: [
      'Everything in Premium',
      'Unlimited personal training',
      'Massage therapy (2/month)',
      'Meal planning service',
      '24/7 gym access',
      'VIP locker room',
      'Unlimited guest passes',
    ],
    buttonText: 'Go Elite',
    buttonVariant: 'accent',
  },
];

export const testimonials: Testimonial[] = [
  {
    id: '1',
    name: 'Jessica Martinez',
    role: 'Marketing Manager',
    content: 'EliteGym transformed my life! The trainers are incredibly knowledgeable and the community is so supportive. I\'ve never felt stronger or more confident.',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
    rating: 5,
    date: '2024-01-15',
  },
  {
    id: '2',
    name: 'David Thompson',
    role: 'Software Engineer',
    content: 'The 24/7 access is perfect for my schedule. The equipment is top-notch and always well-maintained. Best gym investment I\'ve ever made!',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
    rating: 5,
    date: '2024-01-10',
  },
  {
    id: '3',
    name: 'Lisa Wang',
    role: 'Entrepreneur',
    content: 'The variety of classes keeps me motivated and engaged. From HIIT to yoga, there\'s something for every mood and fitness goal.',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
    rating: 5,
    date: '2024-01-05',
  },
];

export const teamMembers: TeamMember[] = [
  {
    id: '1',
    name: 'Alex Rivera',
    position: 'Gym Manager',
    bio: 'Alex ensures our facility runs smoothly and our members have the best possible experience.',
    image: 'https://images.unsplash.com/photo-**********-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    socialLinks: {
      linkedin: 'https://linkedin.com/in/alexrivera',
    },
  },
  {
    id: '2',
    name: 'Dr. Rachel Green',
    position: 'Sports Medicine Specialist',
    bio: 'Dr. Green provides injury prevention and rehabilitation services to keep our members healthy.',
    image: 'https://images.unsplash.com/photo-**********-2b71ea197ec2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    socialLinks: {
      linkedin: 'https://linkedin.com/in/rachelgreen',
    },
  },
];

export const blogPosts: BlogPost[] = [
  {
    id: '1',
    title: '10 Essential Exercises for Building Functional Strength',
    excerpt: 'Discover the fundamental movements that will improve your daily life and athletic performance.',
    content: 'Full blog content here...',
    image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    author: {
      name: 'Sarah Johnson',
      image: 'https://images.unsplash.com/photo-1594736797933-d0501ba2fe65?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
      bio: 'Head Personal Trainer at EliteGym',
    },
    category: 'Training',
    tags: ['Strength', 'Functional Movement', 'Beginner'],
    publishedAt: '2024-01-20',
    readTime: 8,
    featured: true,
  },
];
