'use client';

import { motion } from 'framer-motion';
import { <PERSON>R<PERSON>, Play, Zap, Activity, Brain, <PERSON><PERSON>, Eye, Target } from 'lucide-react';

const Hero = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Futuristic Background */}
      <div className="absolute inset-0">
        {/* Space Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-black via-blue-900/20 to-purple-900/20"></div>

        {/* Animated Geometric Shapes */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute border border-cyan-400/20 rounded-full animate-pulse"
              style={{
                width: `${50 + Math.random() * 200}px`,
                height: `${50 + Math.random() * 200}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`,
                animationDuration: `${3 + Math.random() * 4}s`
              }}
            ></div>
          ))}
        </div>

        {/* Floating Particles */}
        <div className="absolute inset-0">
          {[...Array(100)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-cyan-400 rounded-full opacity-60"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animation: `float ${5 + Math.random() * 10}s linear infinite`,
                animationDelay: `${Math.random() * 5}s`
              }}
            ></div>
          ))}
        </div>

        {/* Holographic Grid Lines */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyan-400 to-transparent"></div>
          <div className="absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-purple-400 to-transparent"></div>
          <div className="absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-400 to-transparent"></div>
          <div className="absolute left-1/4 top-0 w-px h-full bg-gradient-to-b from-transparent via-cyan-400 to-transparent"></div>
          <div className="absolute left-1/2 top-0 w-px h-full bg-gradient-to-b from-transparent via-purple-400 to-transparent"></div>
          <div className="absolute left-3/4 top-0 w-px h-full bg-gradient-to-b from-transparent via-blue-400 to-transparent"></div>
        </div>
      </div>

      {/* Futuristic HUD Interface */}
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
          className="max-w-7xl mx-auto"
        >
          {/* HUD Header */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.8 }}
            className="text-center mb-12"
          >
            <div className="holo-glass rounded-2xl px-8 py-4 inline-flex items-center space-x-4 mb-8">
              <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              <span className="font-orbitron text-sm font-medium text-neon-cyan tracking-wider">
                SYSTEM ONLINE • NEURAL LINK ACTIVE
              </span>
              <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            </div>
          </motion.div>

          {/* Epic Holographic Title */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
            className="text-center mb-16"
          >
            <h1 className="font-orbitron text-6xl md:text-8xl lg:text-9xl font-black leading-none mb-8">
              <motion.span
                className="block text-neon-cyan"
                initial={{ opacity: 0, x: -100 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6, duration: 0.8 }}
              >
                NEO
              </motion.span>
              <motion.span
                className="block text-neon-purple relative"
                initial={{ opacity: 0, x: 100 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.8, duration: 0.8 }}
              >
                GYM
                <div className="absolute -top-4 -right-4 w-8 h-8 border-2 border-neon-pink rounded-full animate-ping"></div>
              </motion.span>
            </h1>

            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 1, duration: 0.8 }}
              className="relative"
            >
              <h2 className="font-exo text-2xl md:text-4xl lg:text-5xl font-light text-white/90 mb-4">
                <span className="text-neon-blue">NEURAL</span> • <span className="text-neon-green">ENHANCED</span> • <span className="text-neon-pink">EVOLUTION</span>
              </h2>
              <div className="w-32 h-1 bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 mx-auto rounded-full"></div>
            </motion.div>
          </motion.div>

          {/* Holographic Interface Panels */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2, duration: 0.8 }}
            className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16"
          >
            {/* Left Panel - Mission Brief */}
            <div className="holo-card p-6 text-left">
              <div className="flex items-center space-x-3 mb-4">
                <Brain className="w-6 h-6 text-neon-cyan" />
                <span className="font-orbitron text-sm text-neon-cyan tracking-wider">MISSION BRIEF</span>
              </div>
              <p className="font-exo text-gray-300 leading-relaxed">
                Neural-enhanced training protocols designed to push human limits beyond conventional boundaries.
              </p>
            </div>

            {/* Center Panel - CTA */}
            <div className="holo-card p-8 text-center">
              <motion.button
                className="cyber-btn w-full mb-4"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="relative z-10 flex items-center justify-center space-x-2">
                  <Zap className="w-5 h-5" />
                  <span>INITIATE PROTOCOL</span>
                </span>
              </motion.button>
              <button className="w-full px-6 py-3 border border-purple-400/50 text-purple-300 hover:text-purple-100 hover:border-purple-300 rounded-lg font-orbitron text-sm transition-all duration-300">
                <Play className="w-4 h-4 inline mr-2" />
                NEURAL PREVIEW
              </button>
            </div>

            {/* Right Panel - System Status */}
            <div className="holo-card p-6 text-left">
              <div className="flex items-center space-x-3 mb-4">
                <Activity className="w-6 h-6 text-neon-green" />
                <span className="font-orbitron text-sm text-neon-green tracking-wider">SYSTEM STATUS</span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Neural Link</span>
                  <span className="text-green-400">ACTIVE</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">AI Trainers</span>
                  <span className="text-cyan-400">ONLINE</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">VR Pods</span>
                  <span className="text-purple-400">READY</span>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Holographic Data Display */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.4, duration: 0.8 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-6xl mx-auto"
          >
            <div className="holo-card p-6 text-center group">
              <div className="relative mb-4">
                <div className="w-16 h-16 mx-auto rounded-full border-2 border-cyan-400 flex items-center justify-center relative">
                  <Activity className="w-8 h-8 text-neon-cyan" />
                  <div className="absolute inset-0 rounded-full border-2 border-cyan-400 animate-ping"></div>
                </div>
              </div>
              <h3 className="font-orbitron text-3xl font-bold text-neon-cyan mb-2">10K+</h3>
              <p className="font-exo text-gray-400 text-sm">NEURAL LINKS</p>
            </div>

            <div className="holo-card p-6 text-center group">
              <div className="relative mb-4">
                <div className="w-16 h-16 mx-auto rounded-full border-2 border-purple-400 flex items-center justify-center relative">
                  <Brain className="w-8 h-8 text-neon-purple" />
                  <div className="absolute inset-0 rounded-full border-2 border-purple-400 animate-ping" style={{animationDelay: '0.5s'}}></div>
                </div>
              </div>
              <h3 className="font-orbitron text-3xl font-bold text-neon-purple mb-2">50+</h3>
              <p className="font-exo text-gray-400 text-sm">AI ENTITIES</p>
            </div>

            <div className="holo-card p-6 text-center group">
              <div className="relative mb-4">
                <div className="w-16 h-16 mx-auto rounded-full border-2 border-green-400 flex items-center justify-center relative">
                  <Eye className="w-8 h-8 text-neon-green" />
                  <div className="absolute inset-0 rounded-full border-2 border-green-400 animate-ping" style={{animationDelay: '1s'}}></div>
                </div>
              </div>
              <h3 className="font-orbitron text-3xl font-bold text-neon-green mb-2">24/7</h3>
              <p className="font-exo text-gray-400 text-sm">MONITORING</p>
            </div>

            <div className="holo-card p-6 text-center group">
              <div className="relative mb-4">
                <div className="w-16 h-16 mx-auto rounded-full border-2 border-pink-400 flex items-center justify-center relative">
                  <Target className="w-8 h-8 text-neon-pink" />
                  <div className="absolute inset-0 rounded-full border-2 border-pink-400 animate-ping" style={{animationDelay: '1.5s'}}></div>
                </div>
              </div>
              <h3 className="font-orbitron text-3xl font-bold text-neon-pink mb-2">∞</h3>
              <p className="font-exo text-gray-400 text-sm">POTENTIAL</p>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Futuristic Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 2, duration: 0.8 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div className="flex flex-col items-center space-y-2">
          <div className="w-8 h-12 border-2 border-cyan-400/50 rounded-full flex justify-center relative">
            <div className="w-1 h-3 bg-cyan-400 rounded-full mt-2 animate-bounce"></div>
            <div className="absolute inset-0 border-2 border-cyan-400/30 rounded-full animate-ping"></div>
          </div>
          <span className="font-orbitron text-xs text-cyan-400/70 tracking-wider">SCROLL TO EXPLORE</span>
        </div>
      </motion.div>

      {/* Floating CSS Animation */}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-20px); }
        }
      `}</style>
    </section>
  );
};

export default Hero;
