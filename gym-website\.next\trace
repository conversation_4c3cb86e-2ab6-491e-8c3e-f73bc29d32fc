[{"name": "hot-reloader", "duration": 200, "timestamp": 1487732396796, "id": 3, "tags": {"version": "15.3.3"}, "startTime": 1750259996558, "traceId": "44d4e9eb35f3aafc"}, {"name": "setup-dev-bundler", "duration": 919067, "timestamp": 1487732014329, "id": 2, "parentId": 1, "tags": {}, "startTime": 1750259996176, "traceId": "44d4e9eb35f3aafc"}, {"name": "run-instrumentation-hook", "duration": 33, "timestamp": 1487733040611, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750259997202, "traceId": "44d4e9eb35f3aafc"}, {"name": "start-dev-server", "duration": 2070725, "timestamp": 1487730993674, "id": 1, "tags": {"cpus": "8", "platform": "win32", "memory.freeMem": "7381135360", "memory.totalMem": "17053687808", "memory.heapSizeLimit": "8576303104", "memory.rss": "176947200", "memory.heapTotal": "97697792", "memory.heapUsed": "73769344"}, "startTime": 1750259995155, "traceId": "44d4e9eb35f3aafc"}, {"name": "compile-path", "duration": 5189654, "timestamp": 1487761738789, "id": 7, "tags": {"trigger": "/"}, "startTime": 1750260025900, "traceId": "44d4e9eb35f3aafc"}, {"name": "ensure-page", "duration": 5191334, "timestamp": 1487761737857, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1750260025899, "traceId": "44d4e9eb35f3aafc"}]