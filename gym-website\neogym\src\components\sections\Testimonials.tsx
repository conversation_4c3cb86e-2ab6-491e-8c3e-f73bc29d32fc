'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, Quote, ChevronLeft, ChevronRight, User } from 'lucide-react';
import Card from '@/components/ui/Card';

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      title: 'Biotech Engineer',
      location: 'Neo Tokyo',
      rating: 5,
      text: "NeoGym's AI trainer ARIA-7 completely transformed my approach to fitness. The neural feedback technology helped me achieve a 40% strength increase in just 3 months. It's like having a personal trainer from the future!",
      avatar: '/api/placeholder/80/80',
      achievement: '40% Strength Increase',
      timeframe: '3 months'
    },
    {
      id: 2,
      name: '<PERSON>',
      title: 'VR Developer',
      location: 'Cyber City',
      rating: 5,
      text: "The VR HIIT sessions with NOVA-X are absolutely incredible. I've never enjoyed cardio this much! The immersive environments make you forget you're even working out. Lost 25 pounds and gained so much endurance.",
      avatar: '/api/placeholder/80/80',
      achievement: '25 lbs Weight Loss',
      timeframe: '4 months'
    },
    {
      id: 3,
      name: '<PERSON>. <PERSON>sha <PERSON>',
      title: 'Neuroscientist',
      location: 'Future Labs',
      rating: 5,
      text: "As a scientist, I was skeptical about biometric yoga, but ZENITH-9's mind-body synchronization protocols are revolutionary. My stress levels dropped by 60% and my flexibility improved dramatically.",
      avatar: '/api/placeholder/80/80',
      achievement: '60% Stress Reduction',
      timeframe: '2 months'
    },
    {
      id: 4,
      name: 'Alex Thompson',
      title: 'Professional Athlete',
      location: 'Elite Sports Complex',
      rating: 5,
      text: "TITAN-5's HIIT optimization pushed my performance to levels I never thought possible. The real-time adaptation to my limits helped me break multiple personal records. This is the future of athletic training.",
      avatar: '/api/placeholder/80/80',
      achievement: 'Multiple PRs',
      timeframe: '6 weeks'
    },
    {
      id: 5,
      name: 'Luna Kim',
      title: 'Tech Entrepreneur',
      location: 'Innovation District',
      rating: 5,
      text: "The holographic personal training sessions are mind-blowing. Having a dedicated AI coach that learns and adapts to my schedule and preferences has made consistency effortless. Best investment I've ever made.",
      avatar: '/api/placeholder/80/80',
      achievement: 'Perfect Consistency',
      timeframe: '5 months'
    }
  ];

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  // Auto-advance testimonials
  useEffect(() => {
    const interval = setInterval(nextTestimonial, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-b from-cyan-900/10 via-transparent to-blue-900/10"></div>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8">
            <Star className="w-5 h-5 text-neon-cyan" />
            <span className="font-orbitron text-sm font-medium text-white">
              MEMBER TESTIMONIALS
            </span>
          </div>
          
          <h2 className="font-orbitron text-4xl md:text-6xl font-bold mb-8">
            <span className="text-white">VOICES FROM </span>
            <span className="text-gradient-neon">THE</span>
            <br />
            <span className="text-gradient-cyber">FUTURE</span>
          </h2>
          
          <p className="font-exo text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Hear from our members who have experienced the transformative power of next-generation fitness technology.
          </p>
        </motion.div>

        {/* Main Testimonial Display */}
        <div className="max-w-4xl mx-auto mb-16">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentIndex}
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -100 }}
              transition={{ duration: 0.5 }}
            >
              <div className="glass rounded-2xl p-12 text-center relative hover:shadow-lg hover:shadow-cyan-500/20 transition-all duration-300">
                {/* Quote Icon */}
                <div className="absolute top-8 left-8">
                  <Quote className="w-12 h-12 text-cyan-400 opacity-30" />
                </div>
                
                {/* Rating */}
                <div className="flex justify-center mb-6">
                  {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                    <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                  ))}
                </div>
                
                {/* Testimonial Text */}
                <blockquote className="font-exo text-xl text-gray-300 leading-relaxed mb-8 italic">
                  "{testimonials[currentIndex].text}"
                </blockquote>
                
                {/* Member Info */}
                <div className="flex items-center justify-center space-x-6">
                  <div className="w-20 h-20 bg-gradient-to-br from-slate-700 to-slate-800 rounded-full flex items-center justify-center">
                    <User className="w-10 h-10 text-neon-cyan" />
                  </div>
                  
                  <div className="text-left">
                    <h4 className="font-orbitron text-xl font-bold text-white">
                      {testimonials[currentIndex].name}
                    </h4>
                    <p className="font-rajdhani text-neon-cyan">
                      {testimonials[currentIndex].title}
                    </p>
                    <p className="font-exo text-sm text-gray-400">
                      {testimonials[currentIndex].location}
                    </p>
                  </div>
                  
                  <div className="text-right">
                    <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 border border-green-500/30 rounded-lg px-4 py-2">
                      <p className="font-orbitron text-sm font-bold text-green-400">
                        {testimonials[currentIndex].achievement}
                      </p>
                      <p className="font-exo text-xs text-gray-400">
                        in {testimonials[currentIndex].timeframe}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Navigation Controls */}
        <div className="flex items-center justify-center space-x-8 mb-12">
          <button
            onClick={prevTestimonial}
            className="p-3 glass rounded-full hover:bg-white/10 transition-colors duration-300 group"
          >
            <ChevronLeft className="w-6 h-6 text-white group-hover:text-neon-cyan transition-colors duration-300" />
          </button>
          
          {/* Dots Indicator */}
          <div className="flex space-x-3">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? 'bg-neon-cyan shadow-[0_0_10px_#00ffff]'
                    : 'bg-white/30 hover:bg-white/50'
                }`}
              />
            ))}
          </div>
          
          <button
            onClick={nextTestimonial}
            className="p-3 glass rounded-full hover:bg-white/10 transition-colors duration-300 group"
          >
            <ChevronRight className="w-6 h-6 text-white group-hover:text-neon-cyan transition-colors duration-300" />
          </button>
        </div>

        {/* Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto"
        >
          <div className="text-center">
            <div className="font-orbitron text-3xl font-bold text-gradient-neon mb-2">4.9</div>
            <div className="font-exo text-gray-400">Average Rating</div>
          </div>
          <div className="text-center">
            <div className="font-orbitron text-3xl font-bold text-gradient-cyber mb-2">10K+</div>
            <div className="font-exo text-gray-400">Happy Members</div>
          </div>
          <div className="text-center">
            <div className="font-orbitron text-3xl font-bold text-gradient-neon mb-2">95%</div>
            <div className="font-exo text-gray-400">Goal Achievement</div>
          </div>
          <div className="text-center">
            <div className="font-orbitron text-3xl font-bold text-gradient-cyber mb-2">24/7</div>
            <div className="font-exo text-gray-400">AI Support</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Testimonials;
