'use client';

import { motion } from 'framer-motion';
import { Brain, Zap, Eye, Activity, Target, Cpu } from 'lucide-react';


const About = () => {
  const features = [
    {
      icon: Brain,
      title: 'AI Personal Trainers',
      description: 'Advanced AI algorithms analyze your form, track progress, and provide real-time coaching tailored to your unique fitness journey.',
      color: 'blue'
    },
    {
      icon: Eye,
      title: 'Biometric Tracking',
      description: 'Cutting-edge sensors monitor heart rate, muscle activation, and movement patterns for optimal workout efficiency.',
      color: 'purple'
    },
    {
      icon: Zap,
      title: 'VR Integration',
      description: 'Immerse yourself in virtual environments that make workouts engaging, from climbing mountains to exploring alien worlds.',
      color: 'green'
    },
    {
      icon: Activity,
      title: 'Smart Equipment',
      description: 'IoT-enabled machines automatically adjust to your settings and provide detailed performance analytics.',
      color: 'cyan'
    },
    {
      icon: Target,
      title: 'Precision Nutrition',
      description: 'AI-powered meal planning based on your genetic profile, fitness goals, and real-time metabolic data.',
      color: 'blue'
    },
    {
      icon: Cpu,
      title: 'Neural Feedback',
      description: 'Advanced neurofeedback technology helps optimize mind-muscle connection and mental performance.',
      color: 'purple'
    }
  ];

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-blue-900/10 to-transparent"></div>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8">
            <Brain className="w-5 h-5 text-cyan-400" />
            <span className="font-orbitron text-sm font-medium text-white">
              FUTURE TECHNOLOGY
            </span>
          </div>

          <h2 className="font-orbitron text-4xl md:text-6xl font-bold mb-8">
            <span className="text-white">BEYOND </span>
            <span className="bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">HUMAN</span>
            <br />
            <span className="bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent">LIMITS</span>
          </h2>
          
          <p className="font-exo text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            NeoGym represents the convergence of cutting-edge technology and human potential. 
            Our revolutionary approach combines artificial intelligence, virtual reality, and 
            advanced biometrics to create the ultimate fitness experience.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="glass rounded-2xl p-8 h-full hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300">
                  <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl mb-6 mx-auto">
                    <Icon className="w-8 h-8 text-cyan-400" />
                  </div>
                  
                  <h3 className="font-orbitron text-xl font-bold text-white mb-4 text-center">
                    {feature.title}
                  </h3>
                  
                  <p className="font-exo text-gray-400 leading-relaxed text-center">
                    {feature.description}
                  </p>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Philosophy Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="glass rounded-2xl p-12 max-w-4xl mx-auto hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300">
            <h3 className="font-orbitron text-3xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent mb-6">
              Our Philosophy
            </h3>
            <p className="font-exo text-lg text-gray-300 leading-relaxed mb-8">
              "The future of fitness isn't just about stronger bodies—it's about enhanced minds, 
              optimized performance, and the seamless integration of human potential with 
              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being."
            </p>
            <div className="flex items-center justify-center space-x-4">
              <div className="w-12 h-0.5 bg-gradient-to-r from-transparent to-blue-500"></div>
              <span className="font-orbitron text-sm text-cyan-400 font-medium">
                DR. ALEX CHEN, FOUNDER & CEO
              </span>
              <div className="w-12 h-0.5 bg-gradient-to-l from-transparent to-purple-500"></div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
