{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/neogym/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Menu, X, Zap, Activity, Users, Calendar, CreditCard, Phone } from 'lucide-react';\n\nconst Header = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { name: 'Home', href: '/', icon: Zap },\n    { name: 'About', href: '/about', icon: Activity },\n    { name: 'Classes', href: '/classes', icon: Users },\n    { name: 'Trainers', href: '/trainers', icon: Calendar },\n    { name: 'Membership', href: '/membership', icon: CreditCard },\n    { name: 'Contact', href: '/contact', icon: Phone },\n  ];\n\n  return (\n    <motion.header\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        scrolled ? 'glass backdrop-blur-xl' : 'bg-transparent'\n      }`}\n    >\n      <nav className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-20\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-3 group\">\n            <div className=\"relative\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center glow-blue group-hover:glow-purple transition-all duration-300\">\n                <Zap className=\"w-6 h-6 text-white\" />\n              </div>\n              <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg opacity-20 blur-xl group-hover:opacity-40 transition-opacity duration-300\"></div>\n            </div>\n            <span className=\"font-orbitron text-2xl font-bold text-gradient-neon\">\n              NEO<span className=\"text-neon-green\">GYM</span>\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => {\n              const Icon = item.icon;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-300 hover:bg-white/5\"\n                >\n                  <Icon className=\"w-4 h-4 text-gray-400 group-hover:text-neon-blue transition-colors duration-300\" />\n                  <span className=\"font-rajdhani font-medium text-gray-300 group-hover:text-white transition-colors duration-300\">\n                    {item.name}\n                  </span>\n                  <div className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-600 group-hover:w-full transition-all duration-300\"></div>\n                </Link>\n              );\n            })}\n          </div>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:block\">\n            <button className=\"btn-neon\">\n              Join the Future\n            </button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsOpen(!isOpen)}\n            className=\"md:hidden p-2 rounded-lg glass hover:bg-white/10 transition-colors duration-300\"\n          >\n            {isOpen ? (\n              <X className=\"w-6 h-6 text-white\" />\n            ) : (\n              <Menu className=\"w-6 h-6 text-white\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              className=\"md:hidden overflow-hidden\"\n            >\n              <div className=\"glass-dark rounded-2xl mt-4 p-6 space-y-4\">\n                {navItems.map((item, index) => {\n                  const Icon = item.icon;\n                  return (\n                    <motion.div\n                      key={item.name}\n                      initial={{ x: -20, opacity: 0 }}\n                      animate={{ x: 0, opacity: 1 }}\n                      transition={{ delay: index * 0.1 }}\n                    >\n                      <Link\n                        href={item.href}\n                        onClick={() => setIsOpen(false)}\n                        className=\"flex items-center space-x-3 p-3 rounded-lg hover:bg-white/5 transition-colors duration-300 group\"\n                      >\n                        <Icon className=\"w-5 h-5 text-neon-blue\" />\n                        <span className=\"font-rajdhani font-medium text-white group-hover:text-neon-blue transition-colors duration-300\">\n                          {item.name}\n                        </span>\n                      </Link>\n                    </motion.div>\n                  );\n                })}\n                <motion.div\n                  initial={{ x: -20, opacity: 0 }}\n                  animate={{ x: 0, opacity: 1 }}\n                  transition={{ delay: navItems.length * 0.1 }}\n                  className=\"pt-4 border-t border-white/10\"\n                >\n                  <button className=\"btn-neon w-full\">\n                    Join the Future\n                  </button>\n                </motion.div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </nav>\n    </motion.header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAOA,MAAM,SAAS;IACb,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAQ,MAAM;YAAK,MAAM,gMAAA,CAAA,MAAG;QAAC;QACrC;YAAE,MAAM;YAAS,MAAM;YAAU,MAAM,0MAAA,CAAA,WAAQ;QAAC;QAChD;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,oMAAA,CAAA,QAAK;QAAC;QACjD;YAAE,MAAM;YAAY,MAAM;YAAa,MAAM,0MAAA,CAAA,WAAQ;QAAC;QACtD;YAAE,MAAM;YAAc,MAAM;YAAe,MAAM,kNAAA,CAAA,aAAU;QAAC;QAC5D;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,oMAAA,CAAA,QAAK;QAAC;KAClD;IAED,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,WAAW,CAAC,4DAA4D,EACtE,WAAW,2BAA2B,kBACtC;kBAEF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAK,WAAU;;wCAAsD;sDACjE,8OAAC;4CAAK,WAAU;sDAAkB;;;;;;;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,OAAO,KAAK,IAAI;gCACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDACb,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;;;;;;mCARV,KAAK,IAAI;;;;;4BAWpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAO,WAAU;0CAAW;;;;;;;;;;;sCAM/B,8OAAC;4BACC,SAAS,IAAM,UAAU,CAAC;4BAC1B,WAAU;sCAET,uBACC,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAMtB,8OAAC,yLAAA,CAAA,kBAAe;8BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,MAAM;oCACnB,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,GAAG,CAAC;4CAAI,SAAS;wCAAE;wCAC9B,SAAS;4CAAE,GAAG;4CAAG,SAAS;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,QAAQ;wCAAI;kDAEjC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,UAAU;4CACzB,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DACb,KAAK,IAAI;;;;;;;;;;;;uCAZT,KAAK,IAAI;;;;;gCAiBpB;8CACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,GAAG,CAAC;wCAAI,SAAS;oCAAE;oCAC9B,SAAS;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,SAAS,MAAM,GAAG;oCAAI;oCAC3C,WAAU;8CAEV,cAAA,8OAAC;wCAAO,WAAU;kDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtD;uCAEe", "debugId": null}}, {"offset": {"line": 384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/neogym/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { \n  Zap, \n  MapPin, \n  Phone, \n  Mail, \n  Facebook, \n  Twitter, \n  Instagram, \n  Youtube,\n  ArrowUp\n} from 'lucide-react';\n\nconst Footer = () => {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const footerLinks = {\n    company: [\n      { name: 'About NeoGym', href: '/about' },\n      { name: 'Our Mission', href: '/mission' },\n      { name: 'Careers', href: '/careers' },\n      { name: 'Press', href: '/press' },\n    ],\n    services: [\n      { name: 'VR HIIT', href: '/classes/vr-hiit' },\n      { name: 'Gravity Yoga', href: '/classes/gravity-yoga' },\n      { name: 'AI Coaching', href: '/classes/ai-coaching' },\n      { name: 'Biometric Tracking', href: '/services/biometric' },\n    ],\n    support: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Contact Us', href: '/contact' },\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n    ],\n  };\n\n  const socialLinks = [\n    { name: 'Facebook', icon: Facebook, href: '#' },\n    { name: 'Twitter', icon: Twitter, href: '#' },\n    { name: 'Instagram', icon: Instagram, href: '#' },\n    { name: 'YouTube', icon: Youtube, href: '#' },\n  ];\n\n  return (\n    <footer className=\"relative bg-black/50 backdrop-blur-xl border-t border-white/10\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <Link href=\"/\" className=\"flex items-center space-x-3 mb-6\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center glow-blue\">\n                <Zap className=\"w-6 h-6 text-white\" />\n              </div>\n              <span className=\"font-orbitron text-2xl font-bold text-gradient-neon\">\n                NEO<span className=\"text-neon-green\">GYM</span>\n              </span>\n            </Link>\n            <p className=\"text-gray-400 mb-6 max-w-md font-exo\">\n              The future of fitness starts now. Experience cutting-edge technology, \n              AI-powered training, and immersive workouts that transform your body and mind.\n            </p>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center space-x-3\">\n                <MapPin className=\"w-5 h-5 text-neon-blue\" />\n                <span className=\"text-gray-400\">2077 Future Street, Neo City, NC 12345</span>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <Phone className=\"w-5 h-5 text-neon-blue\" />\n                <span className=\"text-gray-400\">+1 (555) NEO-GYMS</span>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <Mail className=\"w-5 h-5 text-neon-blue\" />\n                <span className=\"text-gray-400\"><EMAIL></span>\n              </div>\n            </div>\n          </div>\n\n          {/* Company Links */}\n          <div>\n            <h3 className=\"font-orbitron text-lg font-semibold text-white mb-6\">Company</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.company.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-neon-blue transition-colors duration-300 font-exo\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Services Links */}\n          <div>\n            <h3 className=\"font-orbitron text-lg font-semibold text-white mb-6\">Services</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.services.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-neon-blue transition-colors duration-300 font-exo\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support Links */}\n          <div>\n            <h3 className=\"font-orbitron text-lg font-semibold text-white mb-6\">Support</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.support.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-neon-blue transition-colors duration-300 font-exo\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Social Links & Newsletter */}\n        <div className=\"border-t border-white/10 mt-12 pt-8\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <div className=\"flex items-center space-x-6 mb-6 md:mb-0\">\n              <span className=\"font-orbitron text-white font-medium\">Follow the Future:</span>\n              {socialLinks.map((social) => {\n                const Icon = social.icon;\n                return (\n                  <Link\n                    key={social.name}\n                    href={social.href}\n                    className=\"p-2 rounded-lg glass hover:glow-blue transition-all duration-300 group\"\n                  >\n                    <Icon className=\"w-5 h-5 text-gray-400 group-hover:text-neon-blue transition-colors duration-300\" />\n                  </Link>\n                );\n              })}\n            </div>\n\n            {/* Back to Top */}\n            <button\n              onClick={scrollToTop}\n              className=\"flex items-center space-x-2 px-4 py-2 glass rounded-lg hover:glow-blue transition-all duration-300 group\"\n            >\n              <ArrowUp className=\"w-4 h-4 text-neon-blue group-hover:animate-bounce\" />\n              <span className=\"font-rajdhani text-white\">Back to Top</span>\n            </button>\n          </div>\n        </div>\n\n        {/* Copyright */}\n        <div className=\"border-t border-white/10 mt-8 pt-8 text-center\">\n          <p className=\"text-gray-400 font-exo\">\n            © 2024 NeoGym. All rights reserved. The future of fitness is here.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AAgBA,MAAM,SAAS;IACb,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAgB,MAAM;YAAS;YACvC;gBAAE,MAAM;gBAAe,MAAM;YAAW;YACxC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAS,MAAM;YAAS;SACjC;QACD,UAAU;YACR;gBAAE,MAAM;gBAAW,MAAM;YAAmB;YAC5C;gBAAE,MAAM;gBAAgB,MAAM;YAAwB;YACtD;gBAAE,MAAM;gBAAe,MAAM;YAAuB;YACpD;gBAAE,MAAM;gBAAsB,MAAM;YAAsB;SAC3D;QACD,SAAS;YACP;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAc,MAAM;YAAW;YACvC;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;SAC5C;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAY,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;QAAI;QAC9C;YAAE,MAAM;YAAW,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;QAAI;QAC5C;YAAE,MAAM;YAAa,MAAM,4MAAA,CAAA,YAAS;YAAE,MAAM;QAAI;QAChD;YAAE,MAAM;YAAW,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;QAAI;KAC7C;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAK,WAAU;;gDAAsD;8DACjE,8OAAC;oDAAK,WAAU;8DAAkB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;8CAIpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;sCAMtC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CACpE,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CACpE,8OAAC;oCAAG,WAAU;8CACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CACpE,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAuC;;;;;;oCACtD,YAAY,GAAG,CAAC,CAAC;wCAChB,MAAM,OAAO,OAAO,IAAI;wCACxB,qBACE,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,OAAO,IAAI;4CACjB,WAAU;sDAEV,cAAA,8OAAC;gDAAK,WAAU;;;;;;2CAJX,OAAO,IAAI;;;;;oCAOtB;;;;;;;0CAIF,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAK,WAAU;kDAA2B;;;;;;;;;;;;;;;;;;;;;;;8BAMjD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAyB;;;;;;;;;;;;;;;;;;;;;;AAOhD;uCAEe", "debugId": null}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/neogym/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface LayoutProps {\n  children: ReactNode;\n}\n\nconst Layout = ({ children }: LayoutProps) => {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      {/* Animated Background */}\n      <div className=\"fixed inset-0 overflow-hidden pointer-events-none\">\n        <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse\"></div>\n        <div className=\"absolute top-3/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse\" style={{ animationDelay: '2s' }}></div>\n        <div className=\"absolute bottom-1/4 left-1/2 w-96 h-96 bg-green-500/10 rounded-full blur-3xl animate-pulse\" style={{ animationDelay: '4s' }}></div>\n      </div>\n\n      {/* Grid Pattern Overlay */}\n      <div \n        className=\"fixed inset-0 opacity-20 pointer-events-none\"\n        style={{\n          backgroundImage: `\n            linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px)\n          `,\n          backgroundSize: '50px 50px'\n        }}\n      ></div>\n\n      <Header />\n      <main className=\"relative z-10\">\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUA,MAAM,SAAS,CAAC,EAAE,QAAQ,EAAe;IACvC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;wBAA4F,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;kCACzI,8OAAC;wBAAI,WAAU;wBAA6F,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;;;;;;;0BAI5I,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC;;;UAGlB,CAAC;oBACD,gBAAgB;gBAClB;;;;;;0BAGF,8OAAC,sIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;uCAEe", "debugId": null}}, {"offset": {"line": 961, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/neogym/src/components/sections/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ArrowRight, Play, Zap, Activity, Brain } from 'lucide-react';\n\nconst Hero = () => {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Video Background Placeholder */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n        <div className=\"absolute inset-0 bg-black/40\"></div>\n        {/* Animated particles */}\n        <div className=\"absolute inset-0\">\n          {[...Array(50)].map((_, i) => (\n            <div\n              key={i}\n              className=\"absolute w-1 h-1 bg-blue-400 rounded-full animate-pulse\"\n              style={{\n                left: `${Math.random() * 100}%`,\n                top: `${Math.random() * 100}%`,\n                animationDelay: `${Math.random() * 3}s`,\n                animationDuration: `${2 + Math.random() * 2}s`\n              }}\n            ></div>\n          ))}\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1 }}\n          className=\"max-w-5xl mx-auto\"\n        >\n          {/* Badge */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2, duration: 0.8 }}\n            className=\"inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8\"\n          >\n            <Zap className=\"w-5 h-5 text-cyan-400\" />\n            <span className=\"font-orbitron text-sm font-medium text-white\">\n              NEXT-GEN FITNESS TECHNOLOGY\n            </span>\n          </motion.div>\n\n          {/* Main Heading */}\n          <motion.h1\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4, duration: 0.8 }}\n            className=\"font-orbitron text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight\"\n          >\n            <span className=\"text-white\">THE </span>\n            <span className=\"bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent\">FUTURE</span>\n            <br />\n            <span className=\"text-white\">OF </span>\n            <span className=\"bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent\">FITNESS</span>\n            <br />\n            <span className=\"text-green-400\" style={{textShadow: '0 0 20px #4ade80'}}>STARTS NOW</span>\n          </motion.h1>\n\n          {/* Subtitle */}\n          <motion.p\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6, duration: 0.8 }}\n            className=\"font-exo text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed\"\n          >\n            Experience revolutionary workouts with AI trainers, VR environments, \n            and biometric tracking. Transform your body with technology that adapts to you.\n          </motion.p>\n\n          {/* CTA Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.8, duration: 0.8 }}\n            className=\"flex flex-col sm:flex-row items-center justify-center gap-6 mb-16\"\n          >\n            <button className=\"group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg font-orbitron font-bold text-white text-lg hover:from-blue-500 hover:to-purple-500 transition-all duration-300 shadow-lg hover:shadow-blue-500/50\">\n              <span className=\"flex items-center space-x-2\">\n                <span>JOIN THE FUTURE</span>\n                <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-300\" />\n              </span>\n            </button>\n\n            <button className=\"group flex items-center space-x-3 px-8 py-4 glass rounded-lg hover:bg-white/10 transition-all duration-300\">\n              <div className=\"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center group-hover:bg-white/30 transition-colors duration-300\">\n                <Play className=\"w-5 h-5 text-white ml-1\" />\n              </div>\n              <span className=\"font-rajdhani font-semibold text-white text-lg\">\n                Watch Demo\n              </span>\n            </button>\n          </motion.div>\n\n          {/* Stats */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1, duration: 0.8 }}\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\"\n          >\n            <div className=\"glass rounded-2xl p-6 hover:shadow-blue-500/30 hover:shadow-lg transition-all duration-300\">\n              <div className=\"flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg mb-4 mx-auto\">\n                <Activity className=\"w-6 h-6 text-white\" />\n              </div>\n              <h3 className=\"font-orbitron text-2xl font-bold text-white mb-2\">10,000+</h3>\n              <p className=\"font-exo text-gray-400\">Active Members</p>\n            </div>\n\n            <div className=\"glass rounded-2xl p-6 hover:shadow-purple-500/30 hover:shadow-lg transition-all duration-300\">\n              <div className=\"flex items-center justify-center w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg mb-4 mx-auto\">\n                <Brain className=\"w-6 h-6 text-white\" />\n              </div>\n              <h3 className=\"font-orbitron text-2xl font-bold text-white mb-2\">50+</h3>\n              <p className=\"font-exo text-gray-400\">AI Trainers</p>\n            </div>\n\n            <div className=\"glass rounded-2xl p-6 hover:shadow-green-500/30 hover:shadow-lg transition-all duration-300\">\n              <div className=\"flex items-center justify-center w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg mb-4 mx-auto\">\n                <Zap className=\"w-6 h-6 text-white\" />\n              </div>\n              <h3 className=\"font-orbitron text-2xl font-bold text-white mb-2\">24/7</h3>\n              <p className=\"font-exo text-gray-400\">Smart Access</p>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 1.5, duration: 0.8 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <div className=\"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-white rounded-full mt-2 animate-bounce\"></div>\n        </div>\n      </motion.div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,OAAO;IACX,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC;gCAEC,WAAU;gCACV,OAAO;oCACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC9B,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;oCACvC,mBAAmB,GAAG,IAAI,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;gCAChD;+BAPK;;;;;;;;;;;;;;;;0BAcb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAE;oBAC1B,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAA+C;;;;;;;;;;;;sCAMjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAK,WAAU;8CAAa;;;;;;8CAC7B,8OAAC;oCAAK,WAAU;8CAA2E;;;;;;8CAC3F,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;8CAAa;;;;;;8CAC7B,8OAAC;oCAAK,WAAU;8CAA4E;;;;;;8CAC5F,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;oCAAiB,OAAO;wCAAC,YAAY;oCAAkB;8CAAG;;;;;;;;;;;;sCAI5E,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC;wCAAK,WAAU;;0DACd,8OAAC;0DAAK;;;;;;0DACN,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAI1B,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAK,WAAU;sDAAiD;;;;;;;;;;;;;;;;;;sCAOrE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAG,UAAU;4BAAI;4BACtC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAG,WAAU;sDAAmD;;;;;;sDACjE,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;8CAGxC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAAmD;;;;;;sDACjE,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;8CAGxC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAG,WAAU;sDAAmD;;;;;;sDACjE,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;gBACxC,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB;uCAEe", "debugId": null}}, {"offset": {"line": 1439, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/neogym/src/components/sections/About.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Brain, Zap, Eye, Activity, Target, Cpu } from 'lucide-react';\nimport Card from '@/components/ui/Card';\n\nconst About = () => {\n  const features = [\n    {\n      icon: Brain,\n      title: 'AI Personal Trainers',\n      description: 'Advanced AI algorithms analyze your form, track progress, and provide real-time coaching tailored to your unique fitness journey.',\n      color: 'blue'\n    },\n    {\n      icon: Eye,\n      title: 'Biometric Tracking',\n      description: 'Cutting-edge sensors monitor heart rate, muscle activation, and movement patterns for optimal workout efficiency.',\n      color: 'purple'\n    },\n    {\n      icon: Zap,\n      title: 'VR Integration',\n      description: 'Immerse yourself in virtual environments that make workouts engaging, from climbing mountains to exploring alien worlds.',\n      color: 'green'\n    },\n    {\n      icon: Activity,\n      title: 'Smart Equipment',\n      description: 'IoT-enabled machines automatically adjust to your settings and provide detailed performance analytics.',\n      color: 'cyan'\n    },\n    {\n      icon: Target,\n      title: 'Precision Nutrition',\n      description: 'AI-powered meal planning based on your genetic profile, fitness goals, and real-time metabolic data.',\n      color: 'blue'\n    },\n    {\n      icon: Cpu,\n      title: 'Neural Feedback',\n      description: 'Advanced neurofeedback technology helps optimize mind-muscle connection and mental performance.',\n      color: 'purple'\n    }\n  ];\n\n  return (\n    <section className=\"py-24 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-blue-900/10 to-transparent\"></div>\n      \n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-20\"\n        >\n          <div className=\"inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8\">\n            <Brain className=\"w-5 h-5 text-cyan-400\" />\n            <span className=\"font-orbitron text-sm font-medium text-white\">\n              FUTURE TECHNOLOGY\n            </span>\n          </div>\n\n          <h2 className=\"font-orbitron text-4xl md:text-6xl font-bold mb-8\">\n            <span className=\"text-white\">BEYOND </span>\n            <span className=\"bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent\">HUMAN</span>\n            <br />\n            <span className=\"bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent\">LIMITS</span>\n          </h2>\n          \n          <p className=\"font-exo text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed\">\n            NeoGym represents the convergence of cutting-edge technology and human potential. \n            Our revolutionary approach combines artificial intelligence, virtual reality, and \n            advanced biometrics to create the ultimate fitness experience.\n          </p>\n        </motion.div>\n\n        {/* Features Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20\">\n          {features.map((feature, index) => {\n            const Icon = feature.icon;\n            return (\n              <motion.div\n                key={feature.title}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n              >\n                <div className=\"glass rounded-2xl p-8 h-full hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300\">\n                  <div className=\"flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl mb-6 mx-auto\">\n                    <Icon className=\"w-8 h-8 text-cyan-400\" />\n                  </div>\n                  \n                  <h3 className=\"font-orbitron text-xl font-bold text-white mb-4 text-center\">\n                    {feature.title}\n                  </h3>\n                  \n                  <p className=\"font-exo text-gray-400 leading-relaxed text-center\">\n                    {feature.description}\n                  </p>\n                </div>\n              </motion.div>\n            );\n          })}\n        </div>\n\n        {/* Philosophy Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center\"\n        >\n          <div className=\"glass rounded-2xl p-12 max-w-4xl mx-auto hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300\">\n            <h3 className=\"font-orbitron text-3xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent mb-6\">\n              Our Philosophy\n            </h3>\n            <p className=\"font-exo text-lg text-gray-300 leading-relaxed mb-8\">\n              \"The future of fitness isn't just about stronger bodies—it's about enhanced minds, \n              optimized performance, and the seamless integration of human potential with \n              technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.\"\n            </p>\n            <div className=\"flex items-center justify-center space-x-4\">\n              <div className=\"w-12 h-0.5 bg-gradient-to-r from-transparent to-blue-500\"></div>\n              <span className=\"font-orbitron text-sm text-cyan-400 font-medium\">\n                DR. ALEX CHEN, FOUNDER & CEO\n              </span>\n              <div className=\"w-12 h-0.5 bg-gradient-to-l from-transparent to-purple-500\"></div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default About;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAMA,MAAM,QAAQ;IACZ,MAAM,WAAW;QACf;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAKjE,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAa;;;;;;kDAC7B,8OAAC;wCAAK,WAAU;kDAA2E;;;;;;kDAC3F,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAA4E;;;;;;;;;;;;0CAG9F,8OAAC;gCAAE,WAAU;0CAAmE;;;;;;;;;;;;kCAQlF,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS;4BACtB,MAAM,OAAO,QAAQ,IAAI;4BACzB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;0CAEvB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;;;;;;;;;;sDAGlB,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAGhB,8OAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;;;;;;;+BAhBnB,QAAQ,KAAK;;;;;wBAqBxB;;;;;;kCAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiH;;;;;;8CAG/H,8OAAC;oCAAE,WAAU;8CAAsD;;;;;;8CAKnE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAAkD;;;;;;sDAGlE,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B;uCAEe", "debugId": null}}, {"offset": {"line": 1766, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/neogym/src/components/sections/Classes.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Headset, Zap, Brain, Target, Users, Clock } from 'lucide-react';\nimport Card from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\n\nconst Classes = () => {\n  const [activeFilter, setActiveFilter] = useState('all');\n\n  const filters = [\n    { id: 'all', name: 'All Classes' },\n    { id: 'vr', name: 'VR Training' },\n    { id: 'ai', name: 'AI Coaching' },\n    { id: 'biometric', name: 'Biometric' },\n    { id: 'group', name: 'Group Sessions' }\n  ];\n\n  const classes = [\n    {\n      id: 1,\n      title: 'VR HIIT Infinity',\n      category: 'vr',\n      description: 'High-intensity interval training in immersive virtual environments. Battle through alien landscapes while burning calories.',\n      duration: '45 min',\n      intensity: 'High',\n      participants: '8-12',\n      icon: Headset,\n      image: '/api/placeholder/400/300',\n      features: ['Virtual Reality', 'Heart Rate Monitoring', 'Calorie Tracking'],\n      color: 'blue'\n    },\n    {\n      id: 2,\n      title: 'Gravity Yoga Flow',\n      category: 'biometric',\n      description: 'Anti-gravity yoga sessions with real-time posture analysis and breathing optimization through advanced sensors.',\n      duration: '60 min',\n      intensity: 'Medium',\n      participants: '6-10',\n      icon: Target,\n      image: '/api/placeholder/400/300',\n      features: ['Anti-Gravity', 'Posture Analysis', 'Breathing Optimization'],\n      color: 'purple'\n    },\n    {\n      id: 3,\n      title: 'AI Strength Mastery',\n      category: 'ai',\n      description: 'Personalized strength training with AI form correction and adaptive resistance based on your performance.',\n      duration: '50 min',\n      intensity: 'High',\n      participants: '1-4',\n      icon: Brain,\n      image: '/api/placeholder/400/300',\n      features: ['AI Form Correction', 'Adaptive Resistance', 'Progress Tracking'],\n      color: 'green'\n    },\n    {\n      id: 4,\n      title: 'Neural Sync Cardio',\n      category: 'biometric',\n      description: 'Cardio workouts synchronized with your brainwaves for optimal performance and mental clarity.',\n      duration: '40 min',\n      intensity: 'Medium',\n      participants: '5-8',\n      icon: Zap,\n      image: '/api/placeholder/400/300',\n      features: ['Brainwave Sync', 'Mental Clarity', 'Cardio Optimization'],\n      color: 'cyan'\n    },\n    {\n      id: 5,\n      title: 'Hologram Boxing',\n      category: 'vr',\n      description: 'Box against holographic opponents with real-time technique analysis and impact measurement.',\n      duration: '35 min',\n      intensity: 'High',\n      participants: '4-6',\n      icon: Target,\n      image: '/api/placeholder/400/300',\n      features: ['Holographic Opponents', 'Technique Analysis', 'Impact Measurement'],\n      color: 'blue'\n    },\n    {\n      id: 6,\n      title: 'Collective Mind Fitness',\n      category: 'group',\n      description: 'Group workouts where participants\\' biometrics are synchronized for enhanced team performance.',\n      duration: '55 min',\n      intensity: 'Medium',\n      participants: '12-20',\n      icon: Users,\n      image: '/api/placeholder/400/300',\n      features: ['Team Sync', 'Group Biometrics', 'Collective Goals'],\n      color: 'purple'\n    }\n  ];\n\n  const filteredClasses = activeFilter === 'all' \n    ? classes \n    : classes.filter(cls => cls.category === activeFilter);\n\n  return (\n    <section className=\"py-24 relative overflow-hidden\">\n      {/* Background */}\n      <div className=\"absolute inset-0 bg-gradient-to-b from-purple-900/10 via-transparent to-blue-900/10\"></div>\n      \n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8\">\n            <Headset className=\"w-5 h-5 text-purple-400\" />\n            <span className=\"font-orbitron text-sm font-medium text-white\">\n              FUTURISTIC CLASSES\n            </span>\n          </div>\n\n          <h2 className=\"font-orbitron text-4xl md:text-6xl font-bold mb-8\">\n            <span className=\"text-white\">NEXT-GEN </span>\n            <span className=\"bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent\">TRAINING</span>\n            <br />\n            <span className=\"bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent\">PROGRAMS</span>\n          </h2>\n          \n          <p className=\"font-exo text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n            Experience revolutionary fitness classes that blend cutting-edge technology \n            with proven training methodologies for unprecedented results.\n          </p>\n        </motion.div>\n\n        {/* Filter Buttons */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"flex flex-wrap justify-center gap-4 mb-16\"\n        >\n          {filters.map((filter) => (\n            <button\n              key={filter.id}\n              onClick={() => setActiveFilter(filter.id)}\n              className={`px-6 py-3 rounded-lg font-orbitron font-medium transition-all duration-300 ${\n                activeFilter === filter.id\n                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'\n                  : 'glass text-gray-300 hover:text-white hover:bg-white/10'\n              }`}\n            >\n              {filter.name}\n            </button>\n          ))}\n        </motion.div>\n\n        {/* Classes Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {filteredClasses.map((classItem, index) => {\n            const Icon = classItem.icon;\n            return (\n              <motion.div\n                key={classItem.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                layout\n              >\n                <div className=\"glass rounded-2xl overflow-hidden h-full hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300\">\n                  {/* Image Placeholder */}\n                  <div className=\"relative h-48 bg-gradient-to-br from-slate-800 to-slate-900 flex items-center justify-center\">\n                    <Icon className=\"w-16 h-16 text-cyan-400 opacity-50\" />\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent\"></div>\n                  </div>\n                  \n                  <div className=\"p-6\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <h3 className=\"font-orbitron text-xl font-bold text-white\">\n                        {classItem.title}\n                      </h3>\n                      <Icon className=\"w-6 h-6 text-cyan-400\" />\n                    </div>\n                    \n                    <p className=\"font-exo text-gray-400 mb-6 leading-relaxed\">\n                      {classItem.description}\n                    </p>\n                    \n                    {/* Class Info */}\n                    <div className=\"grid grid-cols-3 gap-4 mb-6\">\n                      <div className=\"text-center\">\n                        <Clock className=\"w-4 h-4 text-cyan-400 mx-auto mb-1\" />\n                        <span className=\"font-rajdhani text-sm text-gray-400\">{classItem.duration}</span>\n                      </div>\n                      <div className=\"text-center\">\n                        <Zap className=\"w-4 h-4 text-purple-400 mx-auto mb-1\" />\n                        <span className=\"font-rajdhani text-sm text-gray-400\">{classItem.intensity}</span>\n                      </div>\n                      <div className=\"text-center\">\n                        <Users className=\"w-4 h-4 text-green-400 mx-auto mb-1\" />\n                        <span className=\"font-rajdhani text-sm text-gray-400\">{classItem.participants}</span>\n                      </div>\n                    </div>\n                    \n                    {/* Features */}\n                    <div className=\"flex flex-wrap gap-2 mb-6\">\n                      {classItem.features.map((feature) => (\n                        <span\n                          key={feature}\n                          className=\"px-3 py-1 bg-white/10 rounded-full text-xs font-rajdhani text-gray-300\"\n                        >\n                          {feature}\n                        </span>\n                      ))}\n                    </div>\n                    \n                    <button className=\"w-full px-6 py-3 bg-transparent border-2 border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black rounded-lg font-orbitron font-semibold transition-all duration-300\">\n                      Book Session\n                    </button>\n                  </div>\n                </div>\n              </motion.div>\n            );\n          })}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Classes;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAQA,MAAM,UAAU;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,UAAU;QACd;YAAE,IAAI;YAAO,MAAM;QAAc;QACjC;YAAE,IAAI;YAAM,MAAM;QAAc;QAChC;YAAE,IAAI;YAAM,MAAM;QAAc;QAChC;YAAE,IAAI;YAAa,MAAM;QAAY;QACrC;YAAE,IAAI;YAAS,MAAM;QAAiB;KACvC;IAED,MAAM,UAAU;QACd;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,aAAa;YACb,UAAU;YACV,WAAW;YACX,cAAc;YACd,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,UAAU;gBAAC;gBAAmB;gBAAyB;aAAmB;YAC1E,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,aAAa;YACb,UAAU;YACV,WAAW;YACX,cAAc;YACd,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,UAAU;gBAAC;gBAAgB;gBAAoB;aAAyB;YACxE,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,aAAa;YACb,UAAU;YACV,WAAW;YACX,cAAc;YACd,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,UAAU;gBAAC;gBAAsB;gBAAuB;aAAoB;YAC5E,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,aAAa;YACb,UAAU;YACV,WAAW;YACX,cAAc;YACd,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,UAAU;gBAAC;gBAAkB;gBAAkB;aAAsB;YACrE,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,aAAa;YACb,UAAU;YACV,WAAW;YACX,cAAc;YACd,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,UAAU;gBAAC;gBAAyB;gBAAsB;aAAqB;YAC/E,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,aAAa;YACb,UAAU;YACV,WAAW;YACX,cAAc;YACd,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,UAAU;gBAAC;gBAAa;gBAAoB;aAAmB;YAC/D,OAAO;QACT;KACD;IAED,MAAM,kBAAkB,iBAAiB,QACrC,UACA,QAAQ,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK;IAE3C,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAKjE,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAa;;;;;;kDAC7B,8OAAC;wCAAK,WAAU;kDAA2E;;;;;;kDAC3F,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAA4E;;;;;;;;;;;;0CAG9F,8OAAC;gCAAE,WAAU;0CAAmE;;;;;;;;;;;;kCAOlF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAET,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;gCAEC,SAAS,IAAM,gBAAgB,OAAO,EAAE;gCACxC,WAAW,CAAC,2EAA2E,EACrF,iBAAiB,OAAO,EAAE,GACtB,sEACA,0DACJ;0CAED,OAAO,IAAI;+BARP,OAAO,EAAE;;;;;;;;;;kCAcpB,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,WAAW;4BAC/B,MAAM,OAAO,UAAU,IAAI;4BAC3B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,MAAM;0CAEN,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;;;;;8DAChB,8OAAC;oDAAI,WAAU;;;;;;;;;;;;sDAGjB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,UAAU,KAAK;;;;;;sEAElB,8OAAC;4DAAK,WAAU;;;;;;;;;;;;8DAGlB,8OAAC;oDAAE,WAAU;8DACV,UAAU,WAAW;;;;;;8DAIxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;oEAAK,WAAU;8EAAuC,UAAU,QAAQ;;;;;;;;;;;;sEAE3E,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAuC,UAAU,SAAS;;;;;;;;;;;;sEAE5E,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;oEAAK,WAAU;8EAAuC,UAAU,YAAY;;;;;;;;;;;;;;;;;;8DAKjF,8OAAC;oDAAI,WAAU;8DACZ,UAAU,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACvB,8OAAC;4DAEC,WAAU;sEAET;2DAHI;;;;;;;;;;8DAQX,8OAAC;oDAAO,WAAU;8DAA+K;;;;;;;;;;;;;;;;;;+BAtDhM,UAAU,EAAE;;;;;wBA6DvB;;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 2277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/neogym/src/components/sections/Trainers.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Brain, Zap, Target, Activity, Star, Award } from 'lucide-react';\nimport Card from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\n\nconst Trainers = () => {\n  const trainers = [\n    {\n      id: 1,\n      name: 'ARIA-7',\n      title: 'AI Strength Specialist',\n      specialty: 'Neural-Enhanced Strength Training',\n      experience: '10,000+ Sessions',\n      rating: 4.9,\n      avatar: '/api/placeholder/300/300',\n      description: 'Advanced AI trainer specializing in biomechanical optimization and strength enhancement through neural feedback.',\n      skills: ['Form Analysis', 'Progressive Overload', 'Injury Prevention', 'Neural Optimization'],\n      achievements: ['99.2% Success Rate', '50% Faster Results', 'Zero Injuries'],\n      icon: Brain,\n      color: 'blue'\n    },\n    {\n      id: 2,\n      name: 'NOVA-X',\n      title: 'VR Cardio Master',\n      specialty: 'Immersive Cardio Experiences',\n      experience: '8,500+ Sessions',\n      rating: 4.8,\n      avatar: '/api/placeholder/300/300',\n      description: 'Virtual reality fitness expert creating immersive cardio adventures that make workouts feel like epic quests.',\n      skills: ['VR Environment Design', 'Cardio Optimization', 'Gamification', 'Endurance Building'],\n      achievements: ['95% Retention Rate', '40% Improved Endurance', 'Award-Winning VR Design'],\n      icon: Zap,\n      color: 'purple'\n    },\n    {\n      id: 3,\n      name: 'ZENITH-9',\n      title: 'Biometric Yoga Guide',\n      specialty: 'Mind-Body Synchronization',\n      experience: '12,000+ Sessions',\n      rating: 5.0,\n      avatar: '/api/placeholder/300/300',\n      description: 'Holistic wellness AI combining ancient yoga wisdom with cutting-edge biometric monitoring for perfect balance.',\n      skills: ['Breath Analysis', 'Flexibility Tracking', 'Stress Reduction', 'Mindfulness'],\n      achievements: ['Perfect 5.0 Rating', '60% Stress Reduction', 'Meditation Master'],\n      icon: Target,\n      color: 'green'\n    },\n    {\n      id: 4,\n      name: 'TITAN-5',\n      title: 'HIIT Performance Coach',\n      specialty: 'High-Intensity Optimization',\n      experience: '9,200+ Sessions',\n      rating: 4.9,\n      avatar: '/api/placeholder/300/300',\n      description: 'Elite performance AI designed for maximum intensity training with real-time adaptation to your limits.',\n      skills: ['HIIT Protocols', 'Recovery Optimization', 'Performance Analytics', 'Motivation Systems'],\n      achievements: ['35% Faster Fat Loss', '98% Goal Achievement', 'Elite Athlete Approved'],\n      icon: Activity,\n      color: 'cyan'\n    }\n  ];\n\n  return (\n    <section className=\"py-24 relative overflow-hidden\">\n      {/* Background */}\n      <div className=\"absolute inset-0 bg-gradient-to-b from-green-900/10 via-transparent to-cyan-900/10\"></div>\n      \n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-20\"\n        >\n          <div className=\"inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8\">\n            <Brain className=\"w-5 h-5 text-neon-green\" />\n            <span className=\"font-orbitron text-sm font-medium text-white\">\n              AI TRAINERS\n            </span>\n          </div>\n          \n          <h2 className=\"font-orbitron text-4xl md:text-6xl font-bold mb-8\">\n            <span className=\"text-white\">MEET YOUR </span>\n            <span className=\"text-gradient-neon\">DIGITAL</span>\n            <br />\n            <span className=\"text-gradient-cyber\">COACHES</span>\n          </h2>\n          \n          <p className=\"font-exo text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed\">\n            Our AI trainers combine thousands of hours of expertise with real-time adaptation, \n            providing personalized coaching that evolves with your progress.\n          </p>\n        </motion.div>\n\n        {/* Trainers Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16\">\n          {trainers.map((trainer, index) => {\n            const Icon = trainer.icon;\n            return (\n              <motion.div\n                key={trainer.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.2 }}\n                viewport={{ once: true }}\n              >\n                <div className=\"glass rounded-2xl p-8 h-full relative overflow-hidden hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300\">\n                  {/* Hologram Effect */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 pointer-events-none\"></div>\n                  <div className=\"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent\"></div>\n                  \n                  <div className=\"relative z-10\">\n                    {/* Avatar & Basic Info */}\n                    <div className=\"flex items-start space-x-6 mb-6\">\n                      <div className=\"relative\">\n                        <div className=\"w-24 h-24 bg-gradient-to-br from-slate-700 to-slate-800 rounded-2xl flex items-center justify-center\">\n                          <Icon className=\"w-12 h-12 text-cyan-400\" />\n                        </div>\n                        {/* Hologram lines */}\n                        <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-cyan-400/20 to-transparent rounded-2xl\"></div>\n                        <div className=\"absolute top-2 left-0 w-full h-0.5 bg-cyan-400/30\"></div>\n                        <div className=\"absolute bottom-2 left-0 w-full h-0.5 bg-cyan-400/30\"></div>\n                      </div>\n                      \n                      <div className=\"flex-1\">\n                        <h3 className=\"font-orbitron text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent mb-2\">\n                          {trainer.name}\n                        </h3>\n                        <p className=\"font-rajdhani text-lg text-cyan-400 mb-2\">\n                          {trainer.title}\n                        </p>\n                        <div className=\"flex items-center space-x-4 text-sm text-gray-400\">\n                          <span className=\"flex items-center space-x-1\">\n                            <Star className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                            <span>{trainer.rating}</span>\n                          </span>\n                          <span>{trainer.experience}</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Specialty */}\n                    <div className=\"mb-6\">\n                      <h4 className=\"font-orbitron text-sm font-semibold text-white mb-2\">SPECIALTY</h4>\n                      <p className=\"font-exo text-gray-300\">{trainer.specialty}</p>\n                    </div>\n\n                    {/* Description */}\n                    <div className=\"mb-6\">\n                      <p className=\"font-exo text-gray-400 leading-relaxed\">\n                        {trainer.description}\n                      </p>\n                    </div>\n\n                    {/* Skills */}\n                    <div className=\"mb-6\">\n                      <h4 className=\"font-orbitron text-sm font-semibold text-white mb-3\">CORE SKILLS</h4>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {trainer.skills.map((skill) => (\n                          <span\n                            key={skill}\n                            className=\"px-3 py-1 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-xs font-rajdhani text-blue-300\"\n                          >\n                            {skill}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n\n                    {/* Achievements */}\n                    <div className=\"mb-8\">\n                      <h4 className=\"font-orbitron text-sm font-semibold text-white mb-3\">ACHIEVEMENTS</h4>\n                      <div className=\"space-y-2\">\n                        {trainer.achievements.map((achievement) => (\n                          <div key={achievement} className=\"flex items-center space-x-2\">\n                            <Award className=\"w-4 h-4 text-yellow-400\" />\n                            <span className=\"font-exo text-sm text-gray-300\">{achievement}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n\n                    {/* CTA */}\n                    <button className=\"w-full px-6 py-3 bg-transparent border-2 border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black rounded-lg font-orbitron font-semibold transition-all duration-300\">\n                      Train with {trainer.name}\n                    </button>\n                  </div>\n                </div>\n              </motion.div>\n            );\n          })}\n        </div>\n\n        {/* Call to Action */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center\"\n        >\n          <div className=\"glass rounded-2xl p-12 max-w-4xl mx-auto hover:shadow-lg hover:shadow-purple-500/20 transition-all duration-300\">\n            <h3 className=\"font-orbitron text-3xl font-bold bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent mb-6\">\n              Ready to Meet Your Perfect AI Match?\n            </h3>\n            <p className=\"font-exo text-lg text-gray-300 leading-relaxed mb-8\">\n              Our advanced matching algorithm will pair you with the ideal AI trainer\n              based on your goals, preferences, and fitness level.\n            </p>\n            <button className=\"px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg font-orbitron font-bold text-white text-lg hover:from-blue-500 hover:to-purple-500 transition-all duration-300 shadow-lg hover:shadow-blue-500/50\">\n              Find My AI Trainer\n            </button>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Trainers;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAOA,MAAM,WAAW;IACf,MAAM,WAAW;QACf;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,WAAW;YACX,YAAY;YACZ,QAAQ;YACR,QAAQ;YACR,aAAa;YACb,QAAQ;gBAAC;gBAAiB;gBAAwB;gBAAqB;aAAsB;YAC7F,cAAc;gBAAC;gBAAsB;gBAAsB;aAAgB;YAC3E,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,WAAW;YACX,YAAY;YACZ,QAAQ;YACR,QAAQ;YACR,aAAa;YACb,QAAQ;gBAAC;gBAAyB;gBAAuB;gBAAgB;aAAqB;YAC9F,cAAc;gBAAC;gBAAsB;gBAA0B;aAA0B;YACzF,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,WAAW;YACX,YAAY;YACZ,QAAQ;YACR,QAAQ;YACR,aAAa;YACb,QAAQ;gBAAC;gBAAmB;gBAAwB;gBAAoB;aAAc;YACtF,cAAc;gBAAC;gBAAsB;gBAAwB;aAAoB;YACjF,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,WAAW;YACX,YAAY;YACZ,QAAQ;YACR,QAAQ;YACR,aAAa;YACb,QAAQ;gBAAC;gBAAkB;gBAAyB;gBAAyB;aAAqB;YAClG,cAAc;gBAAC;gBAAuB;gBAAwB;aAAyB;YACvF,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAKjE,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAa;;;;;;kDAC7B,8OAAC;wCAAK,WAAU;kDAAqB;;;;;;kDACrC,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAGxC,8OAAC;gCAAE,WAAU;0CAAmE;;;;;;;;;;;;kCAOlF,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS;4BACtB,MAAM,OAAO,QAAQ,IAAI;4BACzB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;0CAEvB,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAU;;;;;;;;;;;8EAGlB,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;;;;;;;sEAGjB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,QAAQ,IAAI;;;;;;8EAEf,8OAAC;oEAAE,WAAU;8EACV,QAAQ,KAAK;;;;;;8EAEhB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;;8FACd,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,8OAAC;8FAAM,QAAQ,MAAM;;;;;;;;;;;;sFAEvB,8OAAC;sFAAM,QAAQ,UAAU;;;;;;;;;;;;;;;;;;;;;;;;8DAM/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAsD;;;;;;sEACpE,8OAAC;4DAAE,WAAU;sEAA0B,QAAQ,SAAS;;;;;;;;;;;;8DAI1D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;;;;;;8DAKxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAsD;;;;;;sEACpE,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,sBACnB,8OAAC;oEAEC,WAAU;8EAET;mEAHI;;;;;;;;;;;;;;;;8DAUb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAsD;;;;;;sEACpE,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,4BACzB,8OAAC;oEAAsB,WAAU;;sFAC/B,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;4EAAK,WAAU;sFAAkC;;;;;;;mEAF1C;;;;;;;;;;;;;;;;8DAShB,8OAAC;oDAAO,WAAU;;wDAA+K;wDACnL,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;+BApFzB,QAAQ,EAAE;;;;;wBA0FrB;;;;;;kCAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkH;;;;;;8CAGhI,8OAAC;oCAAE,WAAU;8CAAsD;;;;;;8CAInE,8OAAC;oCAAO,WAAU;8CAAsN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpP;uCAEe", "debugId": null}}, {"offset": {"line": 2878, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/neogym/src/components/sections/Membership.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Check, Zap, Crown, Rocket, Star } from 'lucide-react';\nimport Card from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\n\nconst Membership = () => {\n  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');\n\n  const plans = [\n    {\n      id: 'standard',\n      name: 'Standard',\n      description: 'Perfect for fitness enthusiasts ready to experience the future',\n      icon: Zap,\n      color: 'blue',\n      popular: false,\n      pricing: {\n        monthly: 99,\n        yearly: 999\n      },\n      features: [\n        'Access to basic AI trainers',\n        'Standard VR environments',\n        'Basic biometric tracking',\n        '24/7 gym access',\n        'Mobile app access',\n        'Community challenges',\n        'Progress analytics',\n        'Email support'\n      ]\n    },\n    {\n      id: 'premium',\n      name: 'Premium AI',\n      description: 'Advanced AI coaching with personalized optimization',\n      icon: Crown,\n      color: 'purple',\n      popular: true,\n      pricing: {\n        monthly: 199,\n        yearly: 1999\n      },\n      features: [\n        'All Standard features',\n        'Advanced AI trainers (ARIA-7, NOVA-X)',\n        'Premium VR environments',\n        'Advanced biometric analysis',\n        'Personalized nutrition AI',\n        'Recovery optimization',\n        'Priority booking',\n        'Video call support',\n        'Custom workout plans',\n        'Genetic fitness profiling'\n      ]\n    },\n    {\n      id: 'elite',\n      name: 'Elite',\n      description: 'The ultimate futuristic fitness experience',\n      icon: Rocket,\n      color: 'green',\n      popular: false,\n      pricing: {\n        monthly: 399,\n        yearly: 3999\n      },\n      features: [\n        'All Premium AI features',\n        'Exclusive elite AI trainers',\n        'Custom VR environment creation',\n        'Neural feedback training',\n        'Holographic personal training',\n        'Concierge health services',\n        'Private training pods',\n        '24/7 dedicated support',\n        'Quarterly health assessments',\n        'Access to experimental programs',\n        'VIP events and workshops'\n      ]\n    }\n  ];\n\n  const savings = {\n    standard: Math.round(((plans[0].pricing.monthly * 12) - plans[0].pricing.yearly) / (plans[0].pricing.monthly * 12) * 100),\n    premium: Math.round(((plans[1].pricing.monthly * 12) - plans[1].pricing.yearly) / (plans[1].pricing.monthly * 12) * 100),\n    elite: Math.round(((plans[2].pricing.monthly * 12) - plans[2].pricing.yearly) / (plans[2].pricing.monthly * 12) * 100)\n  };\n\n  return (\n    <section className=\"py-24 relative overflow-hidden\">\n      {/* Background */}\n      <div className=\"absolute inset-0 bg-gradient-to-b from-purple-900/10 via-transparent to-green-900/10\"></div>\n      \n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8\">\n            <Crown className=\"w-5 h-5 text-neon-purple\" />\n            <span className=\"font-orbitron text-sm font-medium text-white\">\n              MEMBERSHIP PLANS\n            </span>\n          </div>\n          \n          <h2 className=\"font-orbitron text-4xl md:text-6xl font-bold mb-8\">\n            <span className=\"text-white\">CHOOSE YOUR </span>\n            <span className=\"text-gradient-neon\">FUTURE</span>\n            <br />\n            <span className=\"text-gradient-cyber\">FITNESS LEVEL</span>\n          </h2>\n          \n          <p className=\"font-exo text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-12\">\n            Unlock the power of next-generation fitness technology with our flexible membership options.\n          </p>\n\n          {/* Billing Toggle */}\n          <div className=\"inline-flex items-center glass rounded-lg p-1\">\n            <button\n              onClick={() => setBillingCycle('monthly')}\n              className={`px-6 py-3 rounded-md font-orbitron font-medium transition-all duration-300 ${\n                billingCycle === 'monthly'\n                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'\n                  : 'text-gray-400 hover:text-white'\n              }`}\n            >\n              Monthly\n            </button>\n            <button\n              onClick={() => setBillingCycle('yearly')}\n              className={`px-6 py-3 rounded-md font-orbitron font-medium transition-all duration-300 relative ${\n                billingCycle === 'yearly'\n                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'\n                  : 'text-gray-400 hover:text-white'\n              }`}\n            >\n              Yearly\n              <span className=\"absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full\">\n                Save 20%\n              </span>\n            </button>\n          </div>\n        </motion.div>\n\n        {/* Pricing Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto\">\n          {plans.map((plan, index) => {\n            const Icon = plan.icon;\n            const price = plan.pricing[billingCycle];\n            const planSavings = billingCycle === 'yearly' ? savings[plan.id as keyof typeof savings] : 0;\n            \n            return (\n              <motion.div\n                key={plan.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.2 }}\n                viewport={{ once: true }}\n                className=\"relative\"\n              >\n                {plan.popular && (\n                  <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 z-20\">\n                    <div className=\"bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-full font-orbitron font-bold text-sm\">\n                      MOST POPULAR\n                    </div>\n                  </div>\n                )}\n                \n                <div className={`glass rounded-2xl p-8 h-full relative hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300 ${plan.popular ? 'scale-105 border-2 border-purple-500/50' : ''}`}>\n                  {/* Plan Header */}\n                  <div className=\"text-center mb-8\">\n                    <div className=\"flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl mb-4 mx-auto\">\n                      <Icon className=\"w-8 h-8 text-neon-blue\" />\n                    </div>\n                    \n                    <h3 className=\"font-orbitron text-2xl font-bold text-white mb-2\">\n                      {plan.name}\n                    </h3>\n                    \n                    <p className=\"font-exo text-gray-400 mb-6\">\n                      {plan.description}\n                    </p>\n                    \n                    <div className=\"mb-4\">\n                      <span className=\"font-orbitron text-4xl font-bold text-gradient-neon\">\n                        ${price}\n                      </span>\n                      <span className=\"font-exo text-gray-400 ml-2\">\n                        /{billingCycle === 'monthly' ? 'month' : 'year'}\n                      </span>\n                    </div>\n                    \n                    {billingCycle === 'yearly' && planSavings > 0 && (\n                      <div className=\"inline-flex items-center space-x-1 bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-sm\">\n                        <Star className=\"w-4 h-4\" />\n                        <span>Save {planSavings}%</span>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Features */}\n                  <div className=\"mb-8\">\n                    <ul className=\"space-y-3\">\n                      {plan.features.map((feature, featureIndex) => (\n                        <li key={featureIndex} className=\"flex items-start space-x-3\">\n                          <Check className=\"w-5 h-5 text-green-400 mt-0.5 flex-shrink-0\" />\n                          <span className=\"font-exo text-gray-300 text-sm\">{feature}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n\n                  {/* CTA Button */}\n                  <button className={`w-full px-6 py-3 rounded-lg font-orbitron font-semibold transition-all duration-300 ${\n                    plan.popular\n                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white shadow-lg hover:shadow-blue-500/50'\n                      : 'bg-transparent border-2 border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black'\n                  }`}>\n                    {plan.popular ? 'Start Premium Trial' : `Choose ${plan.name}`}\n                  </button>\n                </div>\n              </motion.div>\n            );\n          })}\n        </div>\n\n        {/* Additional Info */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <p className=\"font-exo text-gray-400 mb-8\">\n            All plans include a 7-day free trial. No commitment, cancel anytime.\n          </p>\n          \n          <div className=\"flex flex-wrap justify-center gap-8 text-sm text-gray-500\">\n            <span>✓ No setup fees</span>\n            <span>✓ 30-day money-back guarantee</span>\n            <span>✓ Pause membership anytime</span>\n            <span>✓ Upgrade/downgrade flexibility</span>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Membership;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAQA,MAAM,aAAa;IACjB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAEvE,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,SAAS;YACT,SAAS;gBACP,SAAS;gBACT,QAAQ;YACV;YACA,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;YACT,SAAS;gBACP,SAAS;gBACT,QAAQ;YACV;YACA,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,SAAS;YACT,SAAS;gBACP,SAAS;gBACT,QAAQ;YACV;YACA,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;KACD;IAED,MAAM,UAAU;QACd,UAAU,KAAK,KAAK,CAAC,CAAC,AAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,GAAG,KAAM,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,GAAG,EAAE,IAAI;QACrH,SAAS,KAAK,KAAK,CAAC,CAAC,AAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,GAAG,KAAM,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,GAAG,EAAE,IAAI;QACpH,OAAO,KAAK,KAAK,CAAC,CAAC,AAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,GAAG,KAAM,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,GAAG,EAAE,IAAI;IACpH;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAKjE,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAa;;;;;;kDAC7B,8OAAC;wCAAK,WAAU;kDAAqB;;;;;;kDACrC,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAGxC,8OAAC;gCAAE,WAAU;0CAAyE;;;;;;0CAKtF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,CAAC,2EAA2E,EACrF,iBAAiB,YACb,4DACA,kCACJ;kDACH;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,CAAC,oFAAoF,EAC9F,iBAAiB,WACb,4DACA,kCACJ;;4CACH;0DAEC,8OAAC;gDAAK,WAAU;0DAAkF;;;;;;;;;;;;;;;;;;;;;;;;kCAQxG,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM;4BAChB,MAAM,OAAO,KAAK,IAAI;4BACtB,MAAM,QAAQ,KAAK,OAAO,CAAC,aAAa;4BACxC,MAAM,cAAc,iBAAiB,WAAW,OAAO,CAAC,KAAK,EAAE,CAAyB,GAAG;4BAE3F,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCAET,KAAK,OAAO,kBACX,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDAAiH;;;;;;;;;;;kDAMpI,8OAAC;wCAAI,WAAW,CAAC,2GAA2G,EAAE,KAAK,OAAO,GAAG,4CAA4C,IAAI;;0DAE3L,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;;;;;;;;;;;kEAGlB,8OAAC;wDAAG,WAAU;kEACX,KAAK,IAAI;;;;;;kEAGZ,8OAAC;wDAAE,WAAU;kEACV,KAAK,WAAW;;;;;;kEAGnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;oEAAsD;oEAClE;;;;;;;0EAEJ,8OAAC;gEAAK,WAAU;;oEAA8B;oEAC1C,iBAAiB,YAAY,UAAU;;;;;;;;;;;;;oDAI5C,iBAAiB,YAAY,cAAc,mBAC1C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;;oEAAK;oEAAM;oEAAY;;;;;;;;;;;;;;;;;;;0DAM9B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAG,WAAU;8DACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,8OAAC;4DAAsB,WAAU;;8EAC/B,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;oEAAK,WAAU;8EAAkC;;;;;;;2DAF3C;;;;;;;;;;;;;;;0DASf,8OAAC;gDAAO,WAAW,CAAC,oFAAoF,EACtG,KAAK,OAAO,GACR,uIACA,4FACJ;0DACC,KAAK,OAAO,GAAG,wBAAwB,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;;;;;;;;;;;;;+BAjE5D,KAAK,EAAE;;;;;wBAsElB;;;;;;kCAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAI3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB;uCAEe", "debugId": null}}, {"offset": {"line": 3408, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/neogym/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 3424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/neogym/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport { motion } from 'framer-motion';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: ReactNode;\n  className?: string;\n  hover?: boolean;\n  glass?: boolean;\n}\n\nconst Card = ({\n  children,\n  className,\n  hover = true,\n  glass = true\n}: CardProps) => {\n  const baseClasses = 'relative rounded-2xl transition-all duration-300';\n\n  const glassClasses = glass\n    ? 'bg-white/5 backdrop-blur-xl border border-white/10'\n    : 'bg-slate-800/50 border border-slate-700/50';\n\n  const hoverClasses = hover\n    ? 'hover:scale-105 hover:-translate-y-2 hover:shadow-2xl'\n    : '';\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.6 }}\n      viewport={{ once: true }}\n      className={cn(\n        baseClasses,\n        glassClasses,\n        hoverClasses,\n        className\n      )}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\nexport default Card;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAaA,MAAM,OAAO,CAAC,EACZ,QAAQ,EACR,SAAS,EACT,QAAQ,IAAI,EACZ,QAAQ,IAAI,EACF;IACV,MAAM,cAAc;IAEpB,MAAM,eAAe,QACjB,uDACA;IAEJ,MAAM,eAAe,QACjB,0DACA;IAEJ,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;QAAI;QAC5B,UAAU;YAAE,MAAM;QAAK;QACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,cACA,cACA;kBAGD;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 3477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/neogym/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode, ButtonHTMLAttributes } from 'react';\nimport { motion } from 'framer-motion';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  children: ReactNode;\n  variant?: 'primary' | 'secondary' | 'neon' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nconst Button = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  className,\n  ...props\n}: ButtonProps) => {\n  const baseClasses = 'relative inline-flex items-center justify-center font-orbitron font-semibold transition-all duration-300 overflow-hidden rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white shadow-lg hover:shadow-xl',\n    secondary: 'bg-transparent border-2 border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white',\n    neon: 'bg-transparent border-2 border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black hover:shadow-[0_0_30px_#00ffff]',\n    ghost: 'bg-white/5 backdrop-blur-md border border-white/20 text-white hover:bg-white/10'\n  };\n\n  const sizes = {\n    sm: 'px-4 py-2 text-sm',\n    md: 'px-6 py-3 text-base',\n    lg: 'px-8 py-4 text-lg'\n  };\n\n  return (\n    <motion.button\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size],\n        className\n      )}\n      {...props}\n    >\n      <span className=\"relative z-10\">{children}</span>\n      {variant === 'neon' && (\n        <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-cyan-400/20 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-700\"></div>\n      )}\n    </motion.button>\n  );\n};\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAaA,MAAM,SAAS,CAAC,EACd,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,SAAS,EACT,GAAG,OACS;IACZ,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,MAAM;QACN,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BAAiB;;;;;;YAChC,YAAY,wBACX,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAIvB;uCAEe", "debugId": null}}, {"offset": {"line": 3539, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Store/gym-website/neogym/src/components/sections/Contact.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Send, MapPin, Phone, Mail, Clock, Zap } from 'lucide-react';\nimport Card from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\n\nconst Contact = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    interest: '',\n    message: ''\n  });\n\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    // Simulate form submission\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    console.log('Form submitted:', formData);\n    setIsSubmitting(false);\n    \n    // Reset form\n    setFormData({\n      name: '',\n      email: '',\n      phone: '',\n      interest: '',\n      message: ''\n    });\n  };\n\n  const contactInfo = [\n    {\n      icon: MapPin,\n      title: 'Location',\n      details: ['2077 Future Street', 'Neo City, NC 12345'],\n      color: 'blue'\n    },\n    {\n      icon: Phone,\n      title: 'Phone',\n      details: ['+1 (555) NEO-GYMS', '+****************'],\n      color: 'purple'\n    },\n    {\n      icon: Mail,\n      title: 'Email',\n      details: ['<EMAIL>', '<EMAIL>'],\n      color: 'green'\n    },\n    {\n      icon: Clock,\n      title: 'Hours',\n      details: ['24/7 AI Access', 'Human Support: 6AM-10PM'],\n      color: 'cyan'\n    }\n  ];\n\n  return (\n    <section className=\"py-24 relative overflow-hidden\">\n      {/* Background */}\n      <div className=\"absolute inset-0 bg-gradient-to-b from-blue-900/10 via-transparent to-purple-900/10\"></div>\n      \n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8\">\n            <Zap className=\"w-5 h-5 text-neon-blue\" />\n            <span className=\"font-orbitron text-sm font-medium text-white\">\n              GET IN TOUCH\n            </span>\n          </div>\n          \n          <h2 className=\"font-orbitron text-4xl md:text-6xl font-bold mb-8\">\n            <span className=\"text-white\">START YOUR </span>\n            <span className=\"text-gradient-neon\">FUTURE</span>\n            <br />\n            <span className=\"text-gradient-cyber\">FITNESS JOURNEY</span>\n          </h2>\n          \n          <p className=\"font-exo text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n            Ready to experience the next evolution of fitness? Contact us to schedule your \n            personalized tour and free trial session.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-7xl mx-auto\">\n          {/* Contact Form */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Card glow=\"blue\" className=\"p-8\">\n              <h3 className=\"font-orbitron text-2xl font-bold text-gradient-neon mb-8\">\n                Send us a Message\n              </h3>\n              \n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block font-orbitron text-sm font-medium text-white mb-2\">\n                      Name *\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300\"\n                      placeholder=\"Your full name\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label className=\"block font-orbitron text-sm font-medium text-white mb-2\">\n                      Email *\n                    </label>\n                    <input\n                      type=\"email\"\n                      name=\"email\"\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300\"\n                      placeholder=\"<EMAIL>\"\n                    />\n                  </div>\n                </div>\n                \n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block font-orbitron text-sm font-medium text-white mb-2\">\n                      Phone\n                    </label>\n                    <input\n                      type=\"tel\"\n                      name=\"phone\"\n                      value={formData.phone}\n                      onChange={handleInputChange}\n                      className=\"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300\"\n                      placeholder=\"(*************\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label className=\"block font-orbitron text-sm font-medium text-white mb-2\">\n                      Interest\n                    </label>\n                    <select\n                      name=\"interest\"\n                      value={formData.interest}\n                      onChange={handleInputChange}\n                      className=\"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300\"\n                    >\n                      <option value=\"\">Select your interest</option>\n                      <option value=\"membership\">Membership Plans</option>\n                      <option value=\"trial\">Free Trial</option>\n                      <option value=\"corporate\">Corporate Programs</option>\n                      <option value=\"personal\">Personal Training</option>\n                      <option value=\"other\">Other</option>\n                    </select>\n                  </div>\n                </div>\n                \n                <div>\n                  <label className=\"block font-orbitron text-sm font-medium text-white mb-2\">\n                    Message\n                  </label>\n                  <textarea\n                    name=\"message\"\n                    value={formData.message}\n                    onChange={handleInputChange}\n                    rows={5}\n                    className=\"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 resize-none\"\n                    placeholder=\"Tell us about your fitness goals and how we can help...\"\n                  />\n                </div>\n                \n                <Button\n                  type=\"submit\"\n                  variant=\"primary\"\n                  className=\"w-full\"\n                  glow\n                  disabled={isSubmitting}\n                >\n                  {isSubmitting ? (\n                    <span className=\"flex items-center space-x-2\">\n                      <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n                      <span>Sending...</span>\n                    </span>\n                  ) : (\n                    <span className=\"flex items-center space-x-2\">\n                      <Send className=\"w-5 h-5\" />\n                      <span>Send Message</span>\n                    </span>\n                  )}\n                </Button>\n              </form>\n            </Card>\n          </motion.div>\n\n          {/* Contact Info */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            {contactInfo.map((info, index) => {\n              const Icon = info.icon;\n              return (\n                <motion.div\n                  key={info.title}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                >\n                  <Card glow={info.color as any} className=\"p-6\">\n                    <div className=\"flex items-start space-x-4\">\n                      <div className=\"flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg\">\n                        <Icon className=\"w-6 h-6 text-neon-blue\" />\n                      </div>\n                      \n                      <div>\n                        <h4 className=\"font-orbitron text-lg font-bold text-white mb-2\">\n                          {info.title}\n                        </h4>\n                        {info.details.map((detail, detailIndex) => (\n                          <p key={detailIndex} className=\"font-exo text-gray-300\">\n                            {detail}\n                          </p>\n                        ))}\n                      </div>\n                    </div>\n                  </Card>\n                </motion.div>\n              );\n            })}\n\n            {/* Map Placeholder */}\n            <Card glow=\"purple\" className=\"p-6\">\n              <h4 className=\"font-orbitron text-lg font-bold text-white mb-4\">\n                Find Us\n              </h4>\n              <div className=\"h-48 bg-gradient-to-br from-slate-800 to-slate-900 rounded-lg flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <MapPin className=\"w-12 h-12 text-neon-purple mx-auto mb-2\" />\n                  <p className=\"font-exo text-gray-400\">Interactive Map</p>\n                  <p className=\"font-exo text-sm text-gray-500\">Coming Soon</p>\n                </div>\n              </div>\n            </Card>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,UAAU;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,SAAS;IACX;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,oBAAoB,CAAC;QACzB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,gBAAgB;QAEhB,aAAa;QACb,YAAY;YACV,MAAM;YACN,OAAO;YACP,OAAO;YACP,UAAU;YACV,SAAS;QACX;IACF;IAEA,MAAM,cAAc;QAClB;YACE,MAAM,0MAAA,CAAA,SAAM;YACZ,OAAO;YACP,SAAS;gBAAC;gBAAsB;aAAqB;YACrD,OAAO;QACT;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;gBAAC;gBAAqB;aAAoB;YACnD,OAAO;QACT;QACA;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,SAAS;gBAAC;gBAAsB;aAAwB;YACxD,OAAO;QACT;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;gBAAC;gBAAkB;aAA0B;YACtD,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAKjE,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAa;;;;;;kDAC7B,8OAAC;wCAAK,WAAU;kDAAqB;;;;;;kDACrC,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAGxC,8OAAC;gCAAE,WAAU;0CAAmE;;;;;;;;;;;;kCAMlF,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;0CAEvB,cAAA,8OAAC,gIAAA,CAAA,UAAI;oCAAC,MAAK;oCAAO,WAAU;;sDAC1B,8OAAC;4CAAG,WAAU;sDAA2D;;;;;;sDAIzE,8OAAC;4CAAK,UAAU;4CAAc,WAAU;;8DACtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA0D;;;;;;8EAG3E,8OAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAO,SAAS,IAAI;oEACpB,UAAU;oEACV,QAAQ;oEACR,WAAU;oEACV,aAAY;;;;;;;;;;;;sEAIhB,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA0D;;;;;;8EAG3E,8OAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAO,SAAS,KAAK;oEACrB,UAAU;oEACV,QAAQ;oEACR,WAAU;oEACV,aAAY;;;;;;;;;;;;;;;;;;8DAKlB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA0D;;;;;;8EAG3E,8OAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAO,SAAS,KAAK;oEACrB,UAAU;oEACV,WAAU;oEACV,aAAY;;;;;;;;;;;;sEAIhB,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA0D;;;;;;8EAG3E,8OAAC;oEACC,MAAK;oEACL,OAAO,SAAS,QAAQ;oEACxB,UAAU;oEACV,WAAU;;sFAEV,8OAAC;4EAAO,OAAM;sFAAG;;;;;;sFACjB,8OAAC;4EAAO,OAAM;sFAAa;;;;;;sFAC3B,8OAAC;4EAAO,OAAM;sFAAQ;;;;;;sFACtB,8OAAC;4EAAO,OAAM;sFAAY;;;;;;sFAC1B,8OAAC;4EAAO,OAAM;sFAAW;;;;;;sFACzB,8OAAC;4EAAO,OAAM;sFAAQ;;;;;;;;;;;;;;;;;;;;;;;;8DAK5B,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA0D;;;;;;sEAG3E,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,MAAM;4DACN,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,8OAAC,kIAAA,CAAA,UAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,WAAU;oDACV,IAAI;oDACJ,UAAU;8DAET,6BACC,8OAAC;wDAAK,WAAU;;0EACd,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;6EAGR,8OAAC;wDAAK,WAAU;;0EACd,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCAET,YAAY,GAAG,CAAC,CAAC,MAAM;wCACtB,MAAM,OAAO,KAAK,IAAI;wCACtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;sDAEvB,cAAA,8OAAC,gIAAA,CAAA,UAAI;gDAAC,MAAM,KAAK,KAAK;gDAAS,WAAU;0DACvC,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;;;;;;;;;;;sEAGlB,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EACX,KAAK,KAAK;;;;;;gEAEZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACzB,8OAAC;wEAAoB,WAAU;kFAC5B;uEADK;;;;;;;;;;;;;;;;;;;;;;2CAjBX,KAAK,KAAK;;;;;oCA0BrB;kDAGA,8OAAC,gIAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;;0DAC5B,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAGhE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAE,WAAU;sEAAyB;;;;;;sEACtC,8OAAC;4DAAE,WAAU;sEAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShE;uCAEe", "debugId": null}}]}