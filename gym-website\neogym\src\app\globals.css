@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Exo+2:wght@300;400;500;600;700&family=Rajdhani:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

/* Futuristic Design System */
:root {
  --neon-cyan: #00ffff;
  --neon-blue: #0080ff;
  --neon-purple: #8000ff;
  --neon-pink: #ff0080;
  --neon-green: #00ff80;
  --neon-orange: #ff8000;

  --bg-space: #000011;
  --bg-dark: #0a0a1a;
  --bg-darker: #050508;

  --glow-cyan: 0 0 20px var(--neon-cyan), 0 0 40px var(--neon-cyan), 0 0 60px var(--neon-cyan);
  --glow-blue: 0 0 20px var(--neon-blue), 0 0 40px var(--neon-blue), 0 0 60px var(--neon-blue);
  --glow-purple: 0 0 20px var(--neon-purple), 0 0 40px var(--neon-purple), 0 0 60px var(--neon-purple);
  --glow-pink: 0 0 20px var(--neon-pink), 0 0 40px var(--neon-pink), 0 0 60px var(--neon-pink);
  --glow-green: 0 0 20px var(--neon-green), 0 0 40px var(--neon-green), 0 0 60px var(--neon-green);
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--bg-space);
  background-image:
    radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(128, 0, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 128, 255, 0.05) 0%, transparent 50%);
  color: #ffffff;
  font-family: 'Exo 2', sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
  position: relative;
}

/* Animated Background Grid */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* Typography */
.font-orbitron {
  font-family: 'Orbitron', monospace;
}

.font-exo {
  font-family: 'Exo 2', sans-serif;
}

.font-rajdhani {
  font-family: 'Rajdhani', sans-serif;
}

/* Neon Text Effects */
.text-neon-cyan {
  color: var(--neon-cyan);
  text-shadow: var(--glow-cyan);
  animation: neon-flicker 2s infinite alternate;
}

.text-neon-blue {
  color: var(--neon-blue);
  text-shadow: var(--glow-blue);
}

.text-neon-purple {
  color: var(--neon-purple);
  text-shadow: var(--glow-purple);
}

.text-neon-pink {
  color: var(--neon-pink);
  text-shadow: var(--glow-pink);
}

.text-neon-green {
  color: var(--neon-green);
  text-shadow: var(--glow-green);
}

@keyframes neon-flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Holographic Glass */
.holo-glass {
  background: linear-gradient(135deg,
    rgba(0, 255, 255, 0.1) 0%,
    rgba(128, 0, 255, 0.05) 50%,
    rgba(0, 128, 255, 0.1) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.holo-glass::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(0, 255, 255, 0.2),
    transparent);
  animation: holo-scan 3s infinite;
}

@keyframes holo-scan {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Cyber Buttons */
.cyber-btn {
  position: relative;
  background: transparent;
  border: 2px solid var(--neon-cyan);
  color: var(--neon-cyan);
  padding: 12px 30px;
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 2px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 100%, 20px 100%);
}

.cyber-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--neon-cyan), transparent);
  transition: left 0.5s;
}

.cyber-btn:hover::before {
  left: 100%;
}

.cyber-btn:hover {
  color: var(--bg-space);
  background: var(--neon-cyan);
  box-shadow: var(--glow-cyan);
  text-shadow: none;
}

/* Holographic Cards */
.holo-card {
  background: linear-gradient(135deg,
    rgba(0, 255, 255, 0.1) 0%,
    rgba(128, 0, 255, 0.05) 50%,
    rgba(0, 128, 255, 0.1) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 15px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.holo-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
    var(--neon-cyan),
    var(--neon-purple),
    var(--neon-blue),
    var(--neon-cyan));
  border-radius: 15px;
  z-index: -1;
  animation: border-glow 3s linear infinite;
}

@keyframes border-glow {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.holo-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0, 255, 255, 0.3),
    0 0 60px rgba(0, 255, 255, 0.2);
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: var(--bg-dark);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--neon-cyan), var(--neon-purple));
  border-radius: 6px;
  box-shadow: inset 0 0 6px rgba(0, 255, 255, 0.5);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--neon-purple), var(--neon-pink));
}

/* Selection */
::selection {
  background: var(--neon-cyan);
  color: var(--bg-space);
  text-shadow: none;
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 5rem;
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Exo 2', sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
.font-orbitron {
  font-family: 'Orbitron', monospace;
}

.font-exo {
  font-family: 'Exo 2', sans-serif;
}

.font-rajdhani {
  font-family: 'Rajdhani', sans-serif;
}

/* Neon Text Effects */
.text-neon-blue {
  color: var(--neon-blue);
  text-shadow: 0 0 10px var(--neon-blue), 0 0 20px var(--neon-blue), 0 0 30px var(--neon-blue);
}

.text-neon-purple {
  color: var(--neon-purple);
  text-shadow: 0 0 10px var(--neon-purple), 0 0 20px var(--neon-purple), 0 0 30px var(--neon-purple);
}

.text-neon-green {
  color: var(--neon-green);
  text-shadow: 0 0 10px var(--neon-green), 0 0 20px var(--neon-green), 0 0 30px var(--neon-green);
}

.text-neon-cyan {
  color: var(--neon-cyan);
  text-shadow: 0 0 10px var(--neon-cyan), 0 0 20px var(--neon-cyan), 0 0 30px var(--neon-cyan);
}

.text-gradient-neon {
  background: var(--gradient-neon);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-cyber {
  background: var(--gradient-cyber);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glassmorphism */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
}

.glass-dark {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
}

/* Neon Glow Effects */
.glow-blue {
  box-shadow: 0 0 20px var(--neon-blue), 0 0 40px var(--neon-blue), 0 0 60px var(--neon-blue);
}

.glow-purple {
  box-shadow: 0 0 20px var(--neon-purple), 0 0 40px var(--neon-purple), 0 0 60px var(--neon-purple);
}

.glow-green {
  box-shadow: 0 0 20px var(--neon-green), 0 0 40px var(--neon-green), 0 0 60px var(--neon-green);
}

.glow-cyan {
  box-shadow: 0 0 20px var(--neon-cyan), 0 0 40px var(--neon-cyan), 0 0 60px var(--neon-cyan);
}

/* Futuristic Buttons */
.btn-neon {
  position: relative;
  padding: 12px 32px;
  background: transparent;
  border: 2px solid var(--neon-blue);
  color: var(--neon-blue);
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 2px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.btn-neon::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--neon-blue), transparent);
  transition: left 0.5s;
}

.btn-neon:hover::before {
  left: 100%;
}

.btn-neon:hover {
  color: white;
  background: var(--neon-blue);
  box-shadow: 0 0 30px var(--neon-blue);
  text-shadow: 0 0 10px white;
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px var(--neon-blue); }
  50% { box-shadow: 0 0 40px var(--neon-blue), 0 0 60px var(--neon-blue); }
}

@keyframes slide-in-left {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slide-in-right {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes fade-in-up {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-neon);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gradient-cyber);
}

/* Selection */
::selection {
  background: var(--neon-blue);
  color: white;
}


