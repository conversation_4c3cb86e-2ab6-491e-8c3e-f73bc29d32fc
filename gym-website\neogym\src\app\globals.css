@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Exo+2:wght@300;400;500;600;700&family=Rajdhani:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

/* Sci-Fi Design System */
:root {
  /* Neon Colors */
  --neon-blue: #00d4ff;
  --neon-purple: #8b5cf6;
  --neon-green: #00ff88;
  --neon-pink: #ff0080;
  --neon-cyan: #00ffff;

  /* Dark Theme */
  --bg-primary: #0a0a0f;
  --bg-secondary: #1a1a2e;
  --bg-tertiary: #16213e;
  --bg-glass: rgba(255, 255, 255, 0.05);

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b8c5d6;
  --text-muted: #6b7280;

  /* Gradients */
  --gradient-neon: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
  --gradient-cyber: linear-gradient(135deg, var(--neon-cyan), var(--neon-green));
  --gradient-dark: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 5rem;
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Exo 2', sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
.font-orbitron {
  font-family: 'Orbitron', monospace;
}

.font-exo {
  font-family: 'Exo 2', sans-serif;
}

.font-rajdhani {
  font-family: 'Rajdhani', sans-serif;
}

/* Neon Text Effects */
.text-neon-blue {
  color: var(--neon-blue);
  text-shadow: 0 0 10px var(--neon-blue), 0 0 20px var(--neon-blue), 0 0 30px var(--neon-blue);
}

.text-neon-purple {
  color: var(--neon-purple);
  text-shadow: 0 0 10px var(--neon-purple), 0 0 20px var(--neon-purple), 0 0 30px var(--neon-purple);
}

.text-neon-green {
  color: var(--neon-green);
  text-shadow: 0 0 10px var(--neon-green), 0 0 20px var(--neon-green), 0 0 30px var(--neon-green);
}

.text-neon-cyan {
  color: var(--neon-cyan);
  text-shadow: 0 0 10px var(--neon-cyan), 0 0 20px var(--neon-cyan), 0 0 30px var(--neon-cyan);
}

.text-gradient-neon {
  background: var(--gradient-neon);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-cyber {
  background: var(--gradient-cyber);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glassmorphism */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
}

.glass-dark {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
}

/* Neon Glow Effects */
.glow-blue {
  box-shadow: 0 0 20px var(--neon-blue), 0 0 40px var(--neon-blue), 0 0 60px var(--neon-blue);
}

.glow-purple {
  box-shadow: 0 0 20px var(--neon-purple), 0 0 40px var(--neon-purple), 0 0 60px var(--neon-purple);
}

.glow-green {
  box-shadow: 0 0 20px var(--neon-green), 0 0 40px var(--neon-green), 0 0 60px var(--neon-green);
}

.glow-cyan {
  box-shadow: 0 0 20px var(--neon-cyan), 0 0 40px var(--neon-cyan), 0 0 60px var(--neon-cyan);
}

/* Futuristic Buttons */
.btn-neon {
  position: relative;
  padding: 12px 32px;
  background: transparent;
  border: 2px solid var(--neon-blue);
  color: var(--neon-blue);
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 2px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.btn-neon::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--neon-blue), transparent);
  transition: left 0.5s;
}

.btn-neon:hover::before {
  left: 100%;
}

.btn-neon:hover {
  color: white;
  background: var(--neon-blue);
  box-shadow: 0 0 30px var(--neon-blue);
  text-shadow: 0 0 10px white;
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px var(--neon-blue); }
  50% { box-shadow: 0 0 40px var(--neon-blue), 0 0 60px var(--neon-blue); }
}

@keyframes slide-in-left {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slide-in-right {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes fade-in-up {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-neon);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gradient-cyber);
}

/* Selection */
::selection {
  background: var(--neon-blue);
  color: white;
}


