'use client';

import Link from 'next/link';

import { 
  Zap, 
  MapPin, 
  Phone, 
  Mail, 
  Facebook, 
  Twitter, 
  Instagram, 
  Youtube,
  ArrowUp
} from 'lucide-react';

const Footer = () => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const footerLinks = {
    company: [
      { name: 'About NeoGym', href: '/about' },
      { name: 'Our Mission', href: '/mission' },
      { name: 'Careers', href: '/careers' },
      { name: 'Press', href: '/press' },
    ],
    services: [
      { name: 'VR HIIT', href: '/classes/vr-hiit' },
      { name: 'Gravity Yoga', href: '/classes/gravity-yoga' },
      { name: 'AI Coaching', href: '/classes/ai-coaching' },
      { name: 'Biometric Tracking', href: '/services/biometric' },
    ],
    support: [
      { name: 'Help Center', href: '/help' },
      { name: 'Contact Us', href: '/contact' },
      { name: 'Privacy Policy', href: '/privacy' },
      { name: 'Terms of Service', href: '/terms' },
    ],
  };

  const socialLinks = [
    { name: 'Facebook', icon: Facebook, href: '#' },
    { name: 'Twitter', icon: Twitter, href: '#' },
    { name: 'Instagram', icon: Instagram, href: '#' },
    { name: 'YouTube', icon: Youtube, href: '#' },
  ];

  return (
    <footer className="relative bg-black/50 backdrop-blur-xl border-t border-white/10">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <Link href="/" className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center glow-blue">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <span className="font-orbitron text-2xl font-bold text-gradient-neon">
                NEO<span className="text-neon-green">GYM</span>
              </span>
            </Link>
            <p className="text-gray-400 mb-6 max-w-md font-exo">
              The future of fitness starts now. Experience cutting-edge technology, 
              AI-powered training, and immersive workouts that transform your body and mind.
            </p>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <MapPin className="w-5 h-5 text-neon-blue" />
                <span className="text-gray-400">2077 Future Street, Neo City, NC 12345</span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-neon-blue" />
                <span className="text-gray-400">+1 (555) NEO-GYMS</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-neon-blue" />
                <span className="text-gray-400"><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="font-orbitron text-lg font-semibold text-white mb-6">Company</h3>
            <ul className="space-y-3">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-neon-blue transition-colors duration-300 font-exo"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services Links */}
          <div>
            <h3 className="font-orbitron text-lg font-semibold text-white mb-6">Services</h3>
            <ul className="space-y-3">
              {footerLinks.services.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-neon-blue transition-colors duration-300 font-exo"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 className="font-orbitron text-lg font-semibold text-white mb-6">Support</h3>
            <ul className="space-y-3">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-neon-blue transition-colors duration-300 font-exo"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Social Links & Newsletter */}
        <div className="border-t border-white/10 mt-12 pt-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-6 mb-6 md:mb-0">
              <span className="font-orbitron text-white font-medium">Follow the Future:</span>
              {socialLinks.map((social) => {
                const Icon = social.icon;
                return (
                  <Link
                    key={social.name}
                    href={social.href}
                    className="p-2 rounded-lg glass hover:glow-blue transition-all duration-300 group"
                  >
                    <Icon className="w-5 h-5 text-gray-400 group-hover:text-neon-blue transition-colors duration-300" />
                  </Link>
                );
              })}
            </div>

            {/* Back to Top */}
            <button
              onClick={scrollToTop}
              className="flex items-center space-x-2 px-4 py-2 glass rounded-lg hover:glow-blue transition-all duration-300 group"
            >
              <ArrowUp className="w-4 h-4 text-neon-blue group-hover:animate-bounce" />
              <span className="font-rajdhani text-white">Back to Top</span>
            </button>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-white/10 mt-8 pt-8 text-center">
          <p className="text-gray-400 font-exo">
            © 2024 NeoGym. All rights reserved. The future of fitness is here.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
