'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { CardProps } from '@/types';

const Card: React.FC<CardProps> = ({
  children,
  className,
  hover = false,
  ...props
}) => {
  const baseClasses = 'bg-white rounded-xl shadow-lg overflow-hidden';
  const hoverClasses = hover ? 'transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl hover:-translate-y-1 cursor-pointer' : '';

  return (
    <div
      className={cn(baseClasses, hoverClasses, className)}
      {...props}
    >
      {children}
    </div>
  );
};

export default Card;
