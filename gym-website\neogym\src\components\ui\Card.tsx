'use client';

import { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface CardProps {
  children: ReactNode;
  className?: string;
  hover?: boolean;
  glass?: boolean;
}

const Card = ({
  children,
  className,
  hover = true,
  glass = true
}: CardProps) => {
  const baseClasses = 'relative rounded-2xl transition-all duration-300';

  const glassClasses = glass
    ? 'bg-white/5 backdrop-blur-xl border border-white/10'
    : 'bg-slate-800/50 border border-slate-700/50';

  const hoverClasses = hover
    ? 'hover:scale-105 hover:-translate-y-2 hover:shadow-2xl'
    : '';

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className={cn(
        baseClasses,
        glassClasses,
        hoverClasses,
        className
      )}
    >
      {children}
    </motion.div>
  );
};

export default Card;
