'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Check, Zap, Crown, Rocket, Star } from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';

const Membership = () => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  const plans = [
    {
      id: 'standard',
      name: 'Standard',
      description: 'Perfect for fitness enthusiasts ready to experience the future',
      icon: Zap,
      color: 'blue',
      popular: false,
      pricing: {
        monthly: 99,
        yearly: 999
      },
      features: [
        'Access to basic AI trainers',
        'Standard VR environments',
        'Basic biometric tracking',
        '24/7 gym access',
        'Mobile app access',
        'Community challenges',
        'Progress analytics',
        'Email support'
      ]
    },
    {
      id: 'premium',
      name: 'Premium AI',
      description: 'Advanced AI coaching with personalized optimization',
      icon: Crown,
      color: 'purple',
      popular: true,
      pricing: {
        monthly: 199,
        yearly: 1999
      },
      features: [
        'All Standard features',
        'Advanced AI trainers (ARIA-7, NOVA-X)',
        'Premium VR environments',
        'Advanced biometric analysis',
        'Personalized nutrition AI',
        'Recovery optimization',
        'Priority booking',
        'Video call support',
        'Custom workout plans',
        'Genetic fitness profiling'
      ]
    },
    {
      id: 'elite',
      name: 'Elite',
      description: 'The ultimate futuristic fitness experience',
      icon: Rocket,
      color: 'green',
      popular: false,
      pricing: {
        monthly: 399,
        yearly: 3999
      },
      features: [
        'All Premium AI features',
        'Exclusive elite AI trainers',
        'Custom VR environment creation',
        'Neural feedback training',
        'Holographic personal training',
        'Concierge health services',
        'Private training pods',
        '24/7 dedicated support',
        'Quarterly health assessments',
        'Access to experimental programs',
        'VIP events and workshops'
      ]
    }
  ];

  const savings = {
    standard: Math.round(((plans[0].pricing.monthly * 12) - plans[0].pricing.yearly) / (plans[0].pricing.monthly * 12) * 100),
    premium: Math.round(((plans[1].pricing.monthly * 12) - plans[1].pricing.yearly) / (plans[1].pricing.monthly * 12) * 100),
    elite: Math.round(((plans[2].pricing.monthly * 12) - plans[2].pricing.yearly) / (plans[2].pricing.monthly * 12) * 100)
  };

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-b from-purple-900/10 via-transparent to-green-900/10"></div>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8">
            <Crown className="w-5 h-5 text-neon-purple" />
            <span className="font-orbitron text-sm font-medium text-white">
              MEMBERSHIP PLANS
            </span>
          </div>
          
          <h2 className="font-orbitron text-4xl md:text-6xl font-bold mb-8">
            <span className="text-white">CHOOSE YOUR </span>
            <span className="text-gradient-neon">FUTURE</span>
            <br />
            <span className="text-gradient-cyber">FITNESS LEVEL</span>
          </h2>
          
          <p className="font-exo text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-12">
            Unlock the power of next-generation fitness technology with our flexible membership options.
          </p>

          {/* Billing Toggle */}
          <div className="inline-flex items-center glass rounded-lg p-1">
            <button
              onClick={() => setBillingCycle('monthly')}
              className={`px-6 py-3 rounded-md font-orbitron font-medium transition-all duration-300 ${
                billingCycle === 'monthly'
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingCycle('yearly')}
              className={`px-6 py-3 rounded-md font-orbitron font-medium transition-all duration-300 relative ${
                billingCycle === 'yearly'
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              Yearly
              <span className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                Save 20%
              </span>
            </button>
          </div>
        </motion.div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {plans.map((plan, index) => {
            const Icon = plan.icon;
            const price = plan.pricing[billingCycle];
            const planSavings = billingCycle === 'yearly' ? savings[plan.id as keyof typeof savings] : 0;
            
            return (
              <motion.div
                key={plan.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="relative"
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
                    <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-full font-orbitron font-bold text-sm">
                      MOST POPULAR
                    </div>
                  </div>
                )}
                
                <Card 
                  glow={plan.color as any} 
                  className={`p-8 h-full relative ${plan.popular ? 'scale-105 border-2 border-purple-500/50' : ''}`}
                >
                  {/* Plan Header */}
                  <div className="text-center mb-8">
                    <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl mb-4 mx-auto">
                      <Icon className="w-8 h-8 text-neon-blue" />
                    </div>
                    
                    <h3 className="font-orbitron text-2xl font-bold text-white mb-2">
                      {plan.name}
                    </h3>
                    
                    <p className="font-exo text-gray-400 mb-6">
                      {plan.description}
                    </p>
                    
                    <div className="mb-4">
                      <span className="font-orbitron text-4xl font-bold text-gradient-neon">
                        ${price}
                      </span>
                      <span className="font-exo text-gray-400 ml-2">
                        /{billingCycle === 'monthly' ? 'month' : 'year'}
                      </span>
                    </div>
                    
                    {billingCycle === 'yearly' && planSavings > 0 && (
                      <div className="inline-flex items-center space-x-1 bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-sm">
                        <Star className="w-4 h-4" />
                        <span>Save {planSavings}%</span>
                      </div>
                    )}
                  </div>

                  {/* Features */}
                  <div className="mb-8">
                    <ul className="space-y-3">
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start space-x-3">
                          <Check className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                          <span className="font-exo text-gray-300 text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* CTA Button */}
                  <Button 
                    variant={plan.popular ? 'primary' : 'neon'} 
                    className="w-full"
                    glow={plan.popular}
                  >
                    {plan.popular ? 'Start Premium Trial' : `Choose ${plan.name}`}
                  </Button>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <p className="font-exo text-gray-400 mb-8">
            All plans include a 7-day free trial. No commitment, cancel anytime.
          </p>
          
          <div className="flex flex-wrap justify-center gap-8 text-sm text-gray-500">
            <span>✓ No setup fees</span>
            <span>✓ 30-day money-back guarantee</span>
            <span>✓ Pause membership anytime</span>
            <span>✓ Upgrade/downgrade flexibility</span>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Membership;
