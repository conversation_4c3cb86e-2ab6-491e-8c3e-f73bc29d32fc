'use client';

import { useState } from 'react';
import { Check, Zap, Crown, Rocket, Star } from 'lucide-react';

const Membership = () => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  const plans = [
    {
      id: 'standard',
      name: 'Standard',
      description: 'Perfect for fitness enthusiasts',
      icon: Zap,
      popular: false,
      pricing: {
        monthly: 99,
        yearly: 999
      },
      features: [
        'Access to AI trainers',
        'VR environments',
        'Biometric tracking',
        '24/7 gym access',
        'Mobile app access',
        'Community challenges',
        'Progress analytics',
        'Email support'
      ]
    },
    {
      id: 'premium',
      name: 'Premium',
      description: 'Advanced AI coaching with personalization',
      icon: Crown,
      popular: true,
      pricing: {
        monthly: 199,
        yearly: 1999
      },
      features: [
        'All Standard features',
        'Advanced AI trainers',
        'Premium VR environments',
        'Advanced biometric analysis',
        'Personalized nutrition AI',
        'Recovery optimization',
        'Priority booking',
        'Video call support',
        'Custom workout plans',
        'Genetic fitness profiling'
      ]
    },
    {
      id: 'elite',
      name: 'Elite',
      description: 'The ultimate fitness experience',
      icon: Rocket,
      popular: false,
      pricing: {
        monthly: 399,
        yearly: 3999
      },
      features: [
        'All Premium features',
        'Exclusive elite AI trainers',
        'Custom VR environment creation',
        'Neural feedback training',
        'Personal training',
        'Concierge health services',
        'Private training pods',
        '24/7 dedicated support',
        'Quarterly health assessments',
        'Access to experimental programs',
        'VIP events and workshops'
      ]
    }
  ];

  const savings = {
    standard: Math.round(((plans[0].pricing.monthly * 12) - plans[0].pricing.yearly) / (plans[0].pricing.monthly * 12) * 100),
    premium: Math.round(((plans[1].pricing.monthly * 12) - plans[1].pricing.yearly) / (plans[1].pricing.monthly * 12) * 100),
    elite: Math.round(((plans[2].pricing.monthly * 12) - plans[2].pricing.yearly) / (plans[2].pricing.monthly * 12) * 100)
  };

  return (
    <section className="py-24 bg-gray-900">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 px-4 py-2 glass rounded-full mb-8">
            <Crown className="w-4 h-4 text-blue-400" />
            <span className="text-sm font-medium text-gray-300">
              Membership Plans
            </span>
          </div>

          <h2 className="font-space text-4xl md:text-6xl font-bold mb-8">
            <span className="text-white">Choose Your </span>
            <span className="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">Plan</span>
          </h2>

          <p className="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed mb-12">
            Unlock the power of next-generation fitness technology with our flexible membership options.
          </p>

          {/* Billing Toggle */}
          <div className="inline-flex items-center glass rounded-lg p-1">
            <button
              onClick={() => setBillingCycle('monthly')}
              className={`px-6 py-3 rounded-md font-medium transition-all duration-200 ${
                billingCycle === 'monthly'
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingCycle('yearly')}
              className={`px-6 py-3 rounded-md font-medium transition-all duration-200 relative ${
                billingCycle === 'yearly'
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              Yearly
              <span className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                Save 20%
              </span>
            </button>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan) => {
            const Icon = plan.icon;
            const price = plan.pricing[billingCycle];
            const planSavings = billingCycle === 'yearly' ? savings[plan.id as keyof typeof savings] : 0;

            return (
              <div
                key={plan.id}
                className="relative"
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
                    <div className="bg-blue-500 text-white px-4 py-2 rounded-full font-medium text-sm">
                      Most Popular
                    </div>
                  </div>
                )}

                <div className={`card p-8 h-full relative ${plan.popular ? 'border-blue-500' : ''}`}>
                  {/* Plan Header */}
                  <div className="text-center mb-8">
                    <div className="flex items-center justify-center w-16 h-16 bg-blue-500/20 rounded-xl mb-4 mx-auto">
                      <Icon className="w-8 h-8 text-blue-400" />
                    </div>

                    <h3 className="font-space text-2xl font-bold text-white mb-2">
                      {plan.name}
                    </h3>

                    <p className="text-gray-400 mb-6">
                      {plan.description}
                    </p>

                    <div className="mb-4">
                      <span className="text-4xl font-bold text-white">
                        ${price}
                      </span>
                      <span className="text-gray-400 ml-2">
                        /{billingCycle === 'monthly' ? 'month' : 'year'}
                      </span>
                    </div>

                    {billingCycle === 'yearly' && planSavings > 0 && (
                      <div className="inline-flex items-center space-x-1 bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-sm">
                        <Star className="w-4 h-4" />
                        <span>Save {planSavings}%</span>
                      </div>
                    )}
                  </div>

                  {/* Features */}
                  <div className="mb-8">
                    <ul className="space-y-3">
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start space-x-3">
                          <Check className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-300 text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* CTA Button */}
                  <button className={plan.popular ? 'btn-primary w-full' : 'btn-secondary w-full'}>
                    {plan.popular ? 'Start Premium Trial' : `Choose ${plan.name}`}
                  </button>
                </div>
              </div>
            );
          })}
        </div>

        {/* Additional Info */}
        <div className="text-center mt-16">
          <p className="text-gray-400 mb-8">
            All plans include a 7-day free trial. No commitment, cancel anytime.
          </p>

          <div className="flex flex-wrap justify-center gap-8 text-sm text-gray-500">
            <span>✓ No setup fees</span>
            <span>✓ 30-day money-back guarantee</span>
            <span>✓ Pause membership anytime</span>
            <span>✓ Upgrade/downgrade flexibility</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Membership;
