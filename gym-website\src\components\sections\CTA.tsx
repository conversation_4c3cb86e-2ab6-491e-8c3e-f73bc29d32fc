'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ArrowR<PERSON>, Star } from 'lucide-react';
import Button from '@/components/ui/Button';
import Section from '@/components/ui/Section';

const CTA: React.FC = () => {
  return (
    <Section background="dark" className="relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900" />
        <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 via-transparent to-accent-500/10" />
        <div
          className="absolute inset-0 bg-repeat opacity-20"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        />
        {/* Animated gradient orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent-500/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '1s'}}></div>
      </div>

      <div className="relative z-10 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="max-w-4xl mx-auto"
        >
          {/* Badge */}
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary-500/30 to-accent-500/30 backdrop-blur-md border border-primary-400/40 rounded-full text-primary-200 text-sm font-bold mb-8 shadow-xl">
            <Star className="w-5 h-5 mr-3 fill-current text-yellow-400" />
            Join 5000+ Happy Members
          </div>

          {/* Heading */}
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold leading-tight tracking-tight mb-8 text-white text-center">
            Ready to Transform Your{' '}
            <span className="block mt-2 bg-gradient-to-r from-blue-300 via-blue-200 to-orange-300 bg-clip-text text-transparent font-extrabold">
              Fitness Journey?
            </span>
          </h2>

          {/* Description */}
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
            Start your transformation today with a free trial. Experience our world-class facilities, 
            expert trainers, and supportive community.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 mb-16">
            <button className="w-full sm:w-auto px-8 py-4 text-lg font-bold bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-2xl hover:shadow-blue-500/50 flex items-center justify-center">
              Start Free Trial
              <ArrowRight className="ml-3 w-5 h-5 transition-transform hover:translate-x-1" />
            </button>

            <button className="w-full sm:w-auto px-8 py-4 text-lg font-semibold bg-white/15 backdrop-blur-md border-2 border-white/40 text-white hover:bg-white hover:text-gray-900 rounded-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-xl">
              Schedule Tour
            </button>
          </div>

          {/* Trust Indicators */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center group">
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="text-3xl font-extrabold bg-gradient-to-r from-primary-300 to-accent-300 bg-clip-text text-transparent mb-2">No Commitment</div>
                <div className="text-gray-300 text-sm font-medium">Cancel anytime</div>
              </div>
            </div>
            <div className="text-center group">
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="text-3xl font-extrabold bg-gradient-to-r from-primary-300 to-accent-300 bg-clip-text text-transparent mb-2">Free Trial</div>
                <div className="text-gray-300 text-sm font-medium">7 days included</div>
              </div>
            </div>
            <div className="text-center group">
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="text-3xl font-extrabold bg-gradient-to-r from-primary-300 to-accent-300 bg-clip-text text-transparent mb-2">Expert Support</div>
                <div className="text-gray-300 text-sm font-medium">Personal guidance</div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </Section>
  );
};

export default CTA;
