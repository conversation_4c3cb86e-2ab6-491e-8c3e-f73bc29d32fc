'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Star } from 'lucide-react';
import Button from '@/components/ui/Button';
import Section from '@/components/ui/Section';

const CTA: React.FC = () => {
  return (
    <Section background="dark" className="relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-500/20 to-accent-500/20" />
        <div 
          className="absolute inset-0 bg-repeat opacity-30"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        />
      </div>

      <div className="relative z-10 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="max-w-4xl mx-auto"
        >
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 bg-primary-500/20 backdrop-blur-sm border border-primary-500/30 rounded-full text-primary-300 text-sm font-medium mb-6">
            <Star className="w-4 h-4 mr-2 fill-current" />
            Join 5000+ Happy Members
          </div>

          {/* Heading */}
          <h2 className="heading-lg mb-6 text-white">
            Ready to Transform Your{' '}
            <span className="text-gradient bg-gradient-to-r from-primary-400 to-accent-400 bg-clip-text text-transparent">
              Fitness Journey
            </span>
            ?
          </h2>

          {/* Description */}
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
            Start your transformation today with a free trial. Experience our world-class facilities, 
            expert trainers, and supportive community.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12">
            <Button 
              variant="primary" 
              size="lg"
              className="group"
            >
              Start Free Trial
              <ArrowRight className="ml-2 w-5 h-5 transition-transform group-hover:translate-x-1" />
            </Button>
            
            <Button 
              variant="secondary" 
              size="lg"
              className="bg-white/10 backdrop-blur-sm border-white/30 text-white hover:bg-white hover:text-gray-900"
            >
              Schedule Tour
            </Button>
          </div>

          {/* Trust Indicators */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto text-center">
            <div>
              <div className="text-2xl font-bold text-primary-400 mb-1">No Commitment</div>
              <div className="text-gray-400 text-sm">Cancel anytime</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary-400 mb-1">Free Trial</div>
              <div className="text-gray-400 text-sm">7 days included</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary-400 mb-1">Expert Support</div>
              <div className="text-gray-400 text-sm">Personal guidance</div>
            </div>
          </div>
        </motion.div>
      </div>
    </Section>
  );
};

export default CTA;
