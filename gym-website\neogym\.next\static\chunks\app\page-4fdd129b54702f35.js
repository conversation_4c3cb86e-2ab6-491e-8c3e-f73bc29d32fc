(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{787:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var a=s(5155),i=s(2115),n=s(1539),r=s(7951),l=s(9964),o=s(8564),c=s(5196);let d=()=>{let[e,t]=(0,i.useState)("monthly"),s=[{id:"standard",name:"Standard",description:"Perfect for fitness enthusiasts",icon:n.A,popular:!1,pricing:{monthly:99,yearly:999},features:["Access to AI trainers","VR environments","Biometric tracking","24/7 gym access","Mobile app access","Community challenges","Progress analytics","Email support"]},{id:"premium",name:"Premium",description:"Advanced AI coaching with personalization",icon:r.A,popular:!0,pricing:{monthly:199,yearly:1999},features:["All Standard features","Advanced AI trainers","Premium VR environments","Advanced biometric analysis","Personalized nutrition AI","Recovery optimization","Priority booking","Video call support","Custom workout plans","Genetic fitness profiling"]},{id:"elite",name:"Elite",description:"The ultimate fitness experience",icon:l.A,popular:!1,pricing:{monthly:399,yearly:3999},features:["All Premium features","Exclusive elite AI trainers","Custom VR environment creation","Neural feedback training","Personal training","Concierge health services","Private training pods","24/7 dedicated support","Quarterly health assessments","Access to experimental programs","VIP events and workshops"]}],d={standard:Math.round((12*s[0].pricing.monthly-s[0].pricing.yearly)/(12*s[0].pricing.monthly)*100),premium:Math.round((12*s[1].pricing.monthly-s[1].pricing.yearly)/(12*s[1].pricing.monthly)*100),elite:Math.round((12*s[2].pricing.monthly-s[2].pricing.yearly)/(12*s[2].pricing.monthly)*100)};return(0,a.jsx)("section",{className:"py-24 bg-gray-900",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsxs)("div",{className:"inline-flex items-center space-x-2 px-4 py-2 glass rounded-full mb-8",children:[(0,a.jsx)(r.A,{className:"w-4 h-4 text-blue-400"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-300",children:"Membership Plans"})]}),(0,a.jsxs)("h2",{className:"font-space text-4xl md:text-6xl font-bold mb-8",children:[(0,a.jsx)("span",{className:"text-white",children:"Choose Your "}),(0,a.jsx)("span",{className:"bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent",children:"Plan"})]}),(0,a.jsx)("p",{className:"text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed mb-12",children:"Unlock the power of next-generation fitness technology with our flexible membership options."}),(0,a.jsxs)("div",{className:"inline-flex items-center glass rounded-lg p-1",children:[(0,a.jsx)("button",{onClick:()=>t("monthly"),className:"px-6 py-3 rounded-md font-medium transition-all duration-200 ".concat("monthly"===e?"bg-blue-500 text-white":"text-gray-400 hover:text-white"),children:"Monthly"}),(0,a.jsxs)("button",{onClick:()=>t("yearly"),className:"px-6 py-3 rounded-md font-medium transition-all duration-200 relative ".concat("yearly"===e?"bg-blue-500 text-white":"text-gray-400 hover:text-white"),children:["Yearly",(0,a.jsx)("span",{className:"absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full",children:"Save 20%"})]})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto",children:s.map(t=>{let s=t.icon,i=t.pricing[e],n="yearly"===e?d[t.id]:0;return(0,a.jsxs)("div",{className:"relative",children:[t.popular&&(0,a.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2 z-20",children:(0,a.jsx)("div",{className:"bg-blue-500 text-white px-4 py-2 rounded-full font-medium text-sm",children:"Most Popular"})}),(0,a.jsxs)("div",{className:"card p-8 h-full relative ".concat(t.popular?"border-blue-500":""),children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-16 h-16 bg-blue-500/20 rounded-xl mb-4 mx-auto",children:(0,a.jsx)(s,{className:"w-8 h-8 text-blue-400"})}),(0,a.jsx)("h3",{className:"font-space text-2xl font-bold text-white mb-2",children:t.name}),(0,a.jsx)("p",{className:"text-gray-400 mb-6",children:t.description}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("span",{className:"text-4xl font-bold text-white",children:["$",i]}),(0,a.jsxs)("span",{className:"text-gray-400 ml-2",children:["/","monthly"===e?"month":"year"]})]}),"yearly"===e&&n>0&&(0,a.jsxs)("div",{className:"inline-flex items-center space-x-1 bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-sm",children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:["Save ",n,"%"]})]})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("ul",{className:"space-y-3",children:t.features.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-start space-x-3",children:[(0,a.jsx)(c.A,{className:"w-5 h-5 text-green-400 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-gray-300 text-sm",children:e})]},t))})}),(0,a.jsx)("button",{className:t.popular?"btn-primary w-full":"btn-secondary w-full",children:t.popular?"Start Premium Trial":"Choose ".concat(t.name)})]})]},t.id)})}),(0,a.jsxs)("div",{className:"text-center mt-16",children:[(0,a.jsx)("p",{className:"text-gray-400 mb-8",children:"All plans include a 7-day free trial. No commitment, cancel anytime."}),(0,a.jsxs)("div",{className:"flex flex-wrap justify-center gap-8 text-sm text-gray-500",children:[(0,a.jsx)("span",{children:"✓ No setup fees"}),(0,a.jsx)("span",{children:"✓ 30-day money-back guarantee"}),(0,a.jsx)("span",{children:"✓ Pause membership anytime"}),(0,a.jsx)("span",{children:"✓ Upgrade/downgrade flexibility"})]})]})]})})}},2205:(e,t,s)=>{"use strict";s.d(t,{default:()=>m});var a=s(5155),i=s(6408),n=s(9376),r=s(1539),l=s(6785),o=s(9397),c=s(8564),d=s(9037);let m=()=>{let e=[{id:1,name:"ARIA-7",title:"AI Strength Specialist",specialty:"Neural-Enhanced Strength Training",experience:"10,000+ Sessions",rating:4.9,avatar:"/api/placeholder/300/300",description:"Advanced AI trainer specializing in biomechanical optimization and strength enhancement through neural feedback.",skills:["Form Analysis","Progressive Overload","Injury Prevention","Neural Optimization"],achievements:["99.2% Success Rate","50% Faster Results","Zero Injuries"],icon:n.A,color:"blue"},{id:2,name:"NOVA-X",title:"VR Cardio Master",specialty:"Immersive Cardio Experiences",experience:"8,500+ Sessions",rating:4.8,avatar:"/api/placeholder/300/300",description:"Virtual reality fitness expert creating immersive cardio adventures that make workouts feel like epic quests.",skills:["VR Environment Design","Cardio Optimization","Gamification","Endurance Building"],achievements:["95% Retention Rate","40% Improved Endurance","Award-Winning VR Design"],icon:r.A,color:"purple"},{id:3,name:"ZENITH-9",title:"Biometric Yoga Guide",specialty:"Mind-Body Synchronization",experience:"12,000+ Sessions",rating:5,avatar:"/api/placeholder/300/300",description:"Holistic wellness AI combining ancient yoga wisdom with cutting-edge biometric monitoring for perfect balance.",skills:["Breath Analysis","Flexibility Tracking","Stress Reduction","Mindfulness"],achievements:["Perfect 5.0 Rating","60% Stress Reduction","Meditation Master"],icon:l.A,color:"green"},{id:4,name:"TITAN-5",title:"HIIT Performance Coach",specialty:"High-Intensity Optimization",experience:"9,200+ Sessions",rating:4.9,avatar:"/api/placeholder/300/300",description:"Elite performance AI designed for maximum intensity training with real-time adaptation to your limits.",skills:["HIIT Protocols","Recovery Optimization","Performance Analytics","Motivation Systems"],achievements:["35% Faster Fat Loss","98% Goal Achievement","Elite Athlete Approved"],icon:o.A,color:"cyan"}];return(0,a.jsxs)("section",{className:"py-24 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-green-900/10 via-transparent to-cyan-900/10"}),(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-20",children:[(0,a.jsxs)("div",{className:"inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8",children:[(0,a.jsx)(n.A,{className:"w-5 h-5 text-neon-green"}),(0,a.jsx)("span",{className:"font-orbitron text-sm font-medium text-white",children:"AI TRAINERS"})]}),(0,a.jsxs)("h2",{className:"font-orbitron text-4xl md:text-6xl font-bold mb-8",children:[(0,a.jsx)("span",{className:"text-white",children:"MEET YOUR "}),(0,a.jsx)("span",{className:"text-gradient-neon",children:"DIGITAL"}),(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"text-gradient-cyber",children:"COACHES"})]}),(0,a.jsx)("p",{className:"font-exo text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed",children:"Our AI trainers combine thousands of hours of expertise with real-time adaptation, providing personalized coaching that evolves with your progress."})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16",children:e.map((e,t)=>{let s=e.icon;return(0,a.jsx)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2*t},viewport:{once:!0},children:(0,a.jsxs)("div",{className:"glass rounded-2xl p-8 h-full relative overflow-hidden hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 pointer-events-none"}),(0,a.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent"}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-6 mb-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-24 h-24 bg-gradient-to-br from-slate-700 to-slate-800 rounded-2xl flex items-center justify-center",children:(0,a.jsx)(s,{className:"w-12 h-12 text-cyan-400"})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-transparent via-cyan-400/20 to-transparent rounded-2xl"}),(0,a.jsx)("div",{className:"absolute top-2 left-0 w-full h-0.5 bg-cyan-400/30"}),(0,a.jsx)("div",{className:"absolute bottom-2 left-0 w-full h-0.5 bg-cyan-400/30"})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-orbitron text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent mb-2",children:e.name}),(0,a.jsx)("p",{className:"font-rajdhani text-lg text-cyan-400 mb-2",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:[(0,a.jsxs)("span",{className:"flex items-center space-x-1",children:[(0,a.jsx)(c.A,{className:"w-4 h-4 text-yellow-400 fill-current"}),(0,a.jsx)("span",{children:e.rating})]}),(0,a.jsx)("span",{children:e.experience})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"font-orbitron text-sm font-semibold text-white mb-2",children:"SPECIALTY"}),(0,a.jsx)("p",{className:"font-exo text-gray-300",children:e.specialty})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("p",{className:"font-exo text-gray-400 leading-relaxed",children:e.description})}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"font-orbitron text-sm font-semibold text-white mb-3",children:"CORE SKILLS"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.skills.map(e=>(0,a.jsx)("span",{className:"px-3 py-1 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-xs font-rajdhani text-blue-300",children:e},e))})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h4",{className:"font-orbitron text-sm font-semibold text-white mb-3",children:"ACHIEVEMENTS"}),(0,a.jsx)("div",{className:"space-y-2",children:e.achievements.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.A,{className:"w-4 h-4 text-yellow-400"}),(0,a.jsx)("span",{className:"font-exo text-sm text-gray-300",children:e})]},e))})]}),(0,a.jsxs)("button",{className:"w-full px-6 py-3 bg-transparent border-2 border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black rounded-lg font-orbitron font-semibold transition-all duration-300",children:["Train with ",e.name]})]})]})},e.id)})}),(0,a.jsx)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center",children:(0,a.jsxs)("div",{className:"glass rounded-2xl p-12 max-w-4xl mx-auto hover:shadow-lg hover:shadow-purple-500/20 transition-all duration-300",children:[(0,a.jsx)("h3",{className:"font-orbitron text-3xl font-bold bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent mb-6",children:"Ready to Meet Your Perfect AI Match?"}),(0,a.jsx)("p",{className:"font-exo text-lg text-gray-300 leading-relaxed mb-8",children:"Our advanced matching algorithm will pair you with the ideal AI trainer based on your goals, preferences, and fitness level."}),(0,a.jsx)("button",{className:"px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg font-orbitron font-bold text-white text-lg hover:from-blue-500 hover:to-purple-500 transition-all duration-300 shadow-lg hover:shadow-blue-500/50",children:"Find My AI Trainer"})]})})]})]})}},2719:(e,t,s)=>{"use strict";s.d(t,{default:()=>x});var a=s(5155),i=s(2115),n=s(6408),r=s(760),l=s(8564),o=s(224),c=s(1007),d=s(2355),m=s(3052);let x=()=>{let[e,t]=(0,i.useState)(0),s=[{id:1,name:"Sarah Chen",title:"Biotech Engineer",location:"Neo Tokyo",rating:5,text:"NeoGym's AI trainer ARIA-7 completely transformed my approach to fitness. The neural feedback technology helped me achieve a 40% strength increase in just 3 months. It's like having a personal trainer from the future!",avatar:"/api/placeholder/80/80",achievement:"40% Strength Increase",timeframe:"3 months"},{id:2,name:"Marcus Rodriguez",title:"VR Developer",location:"Cyber City",rating:5,text:"The VR HIIT sessions with NOVA-X are absolutely incredible. I've never enjoyed cardio this much! The immersive environments make you forget you're even working out. Lost 25 pounds and gained so much endurance.",avatar:"/api/placeholder/80/80",achievement:"25 lbs Weight Loss",timeframe:"4 months"},{id:3,name:"Dr. Aisha Patel",title:"Neuroscientist",location:"Future Labs",rating:5,text:"As a scientist, I was skeptical about biometric yoga, but ZENITH-9's mind-body synchronization protocols are revolutionary. My stress levels dropped by 60% and my flexibility improved dramatically.",avatar:"/api/placeholder/80/80",achievement:"60% Stress Reduction",timeframe:"2 months"},{id:4,name:"Alex Thompson",title:"Professional Athlete",location:"Elite Sports Complex",rating:5,text:"TITAN-5's HIIT optimization pushed my performance to levels I never thought possible. The real-time adaptation to my limits helped me break multiple personal records. This is the future of athletic training.",avatar:"/api/placeholder/80/80",achievement:"Multiple PRs",timeframe:"6 weeks"},{id:5,name:"Luna Kim",title:"Tech Entrepreneur",location:"Innovation District",rating:5,text:"The holographic personal training sessions are mind-blowing. Having a dedicated AI coach that learns and adapts to my schedule and preferences has made consistency effortless. Best investment I've ever made.",avatar:"/api/placeholder/80/80",achievement:"Perfect Consistency",timeframe:"5 months"}],x=()=>{t(e=>(e+1)%s.length)};return(0,i.useEffect)(()=>{let e=setInterval(x,5e3);return()=>clearInterval(e)},[x]),(0,a.jsxs)("section",{className:"py-24 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-cyan-900/10 via-transparent to-blue-900/10"}),(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,a.jsxs)("div",{className:"inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8",children:[(0,a.jsx)(l.A,{className:"w-5 h-5 text-neon-cyan"}),(0,a.jsx)("span",{className:"font-orbitron text-sm font-medium text-white",children:"MEMBER TESTIMONIALS"})]}),(0,a.jsxs)("h2",{className:"font-orbitron text-4xl md:text-6xl font-bold mb-8",children:[(0,a.jsx)("span",{className:"text-white",children:"VOICES FROM "}),(0,a.jsx)("span",{className:"text-gradient-neon",children:"THE"}),(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"text-gradient-cyber",children:"FUTURE"})]}),(0,a.jsx)("p",{className:"font-exo text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed",children:"Hear from our members who have experienced the transformative power of next-generation fitness technology."})]}),(0,a.jsx)("div",{className:"max-w-4xl mx-auto mb-16",children:(0,a.jsx)(r.N,{mode:"wait",children:(0,a.jsx)(n.P.div,{initial:{opacity:0,x:100},animate:{opacity:1,x:0},exit:{opacity:0,x:-100},transition:{duration:.5},children:(0,a.jsxs)("div",{className:"glass rounded-2xl p-12 text-center relative hover:shadow-lg hover:shadow-cyan-500/20 transition-all duration-300",children:[(0,a.jsx)("div",{className:"absolute top-8 left-8",children:(0,a.jsx)(o.A,{className:"w-12 h-12 text-cyan-400 opacity-30"})}),(0,a.jsx)("div",{className:"flex justify-center mb-6",children:[...Array(s[e].rating)].map((e,t)=>(0,a.jsx)(l.A,{className:"w-6 h-6 text-yellow-400 fill-current"},t))}),(0,a.jsxs)("blockquote",{className:"text-xl text-gray-300 leading-relaxed mb-8 italic",children:["“",s[e].text,"”"]}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-6",children:[(0,a.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-slate-700 to-slate-800 rounded-full flex items-center justify-center",children:(0,a.jsx)(c.A,{className:"w-10 h-10 text-neon-cyan"})}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("h4",{className:"font-orbitron text-xl font-bold text-white",children:s[e].name}),(0,a.jsx)("p",{className:"font-rajdhani text-neon-cyan",children:s[e].title}),(0,a.jsx)("p",{className:"font-exo text-sm text-gray-400",children:s[e].location})]}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-500/20 to-blue-500/20 border border-green-500/30 rounded-lg px-4 py-2",children:[(0,a.jsx)("p",{className:"font-orbitron text-sm font-bold text-green-400",children:s[e].achievement}),(0,a.jsxs)("p",{className:"font-exo text-xs text-gray-400",children:["in ",s[e].timeframe]})]})})]})]})},e)})}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-8 mb-12",children:[(0,a.jsx)("button",{onClick:()=>{t(e=>(e-1+s.length)%s.length)},className:"p-3 glass rounded-full hover:bg-white/10 transition-colors duration-300 group",children:(0,a.jsx)(d.A,{className:"w-6 h-6 text-white group-hover:text-neon-cyan transition-colors duration-300"})}),(0,a.jsx)("div",{className:"flex space-x-3",children:s.map((s,i)=>(0,a.jsx)("button",{onClick:()=>t(i),className:"w-3 h-3 rounded-full transition-all duration-300 ".concat(i===e?"bg-neon-cyan shadow-[0_0_10px_#00ffff]":"bg-white/30 hover:bg-white/50")},i))}),(0,a.jsx)("button",{onClick:x,className:"p-3 glass rounded-full hover:bg-white/10 transition-colors duration-300 group",children:(0,a.jsx)(m.A,{className:"w-6 h-6 text-white group-hover:text-neon-cyan transition-colors duration-300"})})]}),(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-orbitron text-3xl font-bold text-gradient-neon mb-2",children:"4.9"}),(0,a.jsx)("div",{className:"font-exo text-gray-400",children:"Average Rating"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-orbitron text-3xl font-bold text-gradient-cyber mb-2",children:"10K+"}),(0,a.jsx)("div",{className:"font-exo text-gray-400",children:"Happy Members"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-orbitron text-3xl font-bold text-gradient-neon mb-2",children:"95%"}),(0,a.jsx)("div",{className:"font-exo text-gray-400",children:"Goal Achievement"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-orbitron text-3xl font-bold text-gradient-cyber mb-2",children:"24/7"}),(0,a.jsx)("div",{className:"font-exo text-gray-400",children:"AI Support"})]})]})]})]})}},3934:(e,t,s)=>{"use strict";s.d(t,{default:()=>m});var a=s(5155),i=s(6408),n=s(9376),r=s(2657),l=s(1539),o=s(9397),c=s(6785),d=s(3314);let m=()=>{let e=[{icon:n.A,title:"AI Personal Trainers",description:"Advanced AI algorithms analyze your form, track progress, and provide real-time coaching tailored to your unique fitness journey.",color:"blue"},{icon:r.A,title:"Biometric Tracking",description:"Cutting-edge sensors monitor heart rate, muscle activation, and movement patterns for optimal workout efficiency.",color:"purple"},{icon:l.A,title:"VR Integration",description:"Immerse yourself in virtual environments that make workouts engaging, from climbing mountains to exploring alien worlds.",color:"green"},{icon:o.A,title:"Smart Equipment",description:"IoT-enabled machines automatically adjust to your settings and provide detailed performance analytics.",color:"cyan"},{icon:c.A,title:"Precision Nutrition",description:"AI-powered meal planning based on your genetic profile, fitness goals, and real-time metabolic data.",color:"blue"},{icon:d.A,title:"Neural Feedback",description:"Advanced neurofeedback technology helps optimize mind-muscle connection and mental performance.",color:"purple"}];return(0,a.jsxs)("section",{className:"py-24 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-transparent via-blue-900/10 to-transparent"}),(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-20",children:[(0,a.jsxs)("div",{className:"inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8",children:[(0,a.jsx)(n.A,{className:"w-5 h-5 text-cyan-400"}),(0,a.jsx)("span",{className:"font-orbitron text-sm font-medium text-white",children:"FUTURE TECHNOLOGY"})]}),(0,a.jsxs)("h2",{className:"font-orbitron text-4xl md:text-6xl font-bold mb-8",children:[(0,a.jsx)("span",{className:"text-white",children:"BEYOND "}),(0,a.jsx)("span",{className:"bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent",children:"HUMAN"}),(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent",children:"LIMITS"})]}),(0,a.jsx)("p",{className:"font-exo text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed",children:"NeoGym represents the convergence of cutting-edge technology and human potential. Our revolutionary approach combines artificial intelligence, virtual reality, and advanced biometrics to create the ultimate fitness experience."})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20",children:e.map((e,t)=>{let s=e.icon;return(0,a.jsx)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},children:(0,a.jsxs)("div",{className:"glass rounded-2xl p-8 h-full hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl mb-6 mx-auto",children:(0,a.jsx)(s,{className:"w-8 h-8 text-cyan-400"})}),(0,a.jsx)("h3",{className:"font-orbitron text-xl font-bold text-white mb-4 text-center",children:e.title}),(0,a.jsx)("p",{className:"font-exo text-gray-400 leading-relaxed text-center",children:e.description})]})},e.title)})}),(0,a.jsx)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center",children:(0,a.jsxs)("div",{className:"glass rounded-2xl p-12 max-w-4xl mx-auto hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300",children:[(0,a.jsx)("h3",{className:"font-orbitron text-3xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent mb-6",children:"Our Philosophy"}),(0,a.jsx)("p",{className:"text-lg text-gray-300 leading-relaxed mb-8",children:"“The future of fitness isn't just about stronger bodies—it's about enhanced minds, optimized performance, and the seamless integration of human potential with technological innovation. At NeoGym, we don't just train your body; we evolve your entire being.”"}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-0.5 bg-gradient-to-r from-transparent to-blue-500"}),(0,a.jsx)("span",{className:"font-orbitron text-sm text-cyan-400 font-medium",children:"DR. ALEX CHEN, FOUNDER & CEO"}),(0,a.jsx)("div",{className:"w-12 h-0.5 bg-gradient-to-l from-transparent to-purple-500"})]})]})})]})]})}},4646:(e,t,s)=>{Promise.resolve().then(s.bind(s,4775)),Promise.resolve().then(s.bind(s,3934)),Promise.resolve().then(s.bind(s,5949)),Promise.resolve().then(s.bind(s,8977)),Promise.resolve().then(s.bind(s,8431)),Promise.resolve().then(s.bind(s,787)),Promise.resolve().then(s.bind(s,2719)),Promise.resolve().then(s.bind(s,2205))},4775:(e,t,s)=>{"use strict";s.d(t,{default:()=>A});var a=s(5155),i=s(2115),n=s(6874),r=s.n(n),l=s(1539),o=s(9397),c=s(7580),d=s(9074),m=s(1586),x=s(9420),h=s(4416),p=s(4783);let u=()=>{let[e,t]=(0,i.useState)(!1),[s,n]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=()=>{n(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let u=[{name:"Home",href:"/",icon:l.A},{name:"About",href:"/about",icon:o.A},{name:"Classes",href:"/classes",icon:c.A},{name:"Trainers",href:"/trainers",icon:d.A},{name:"Membership",href:"/membership",icon:m.A},{name:"Contact",href:"/contact",icon:x.A}];return(0,a.jsx)("header",{className:"fixed top-0 left-0 right-0 z-50 transition-all duration-300 ".concat(s?"glass backdrop-blur-xl":"bg-transparent"),children:(0,a.jsxs)("nav",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsxs)(r(),{href:"/",className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center",children:(0,a.jsx)(l.A,{className:"w-6 h-6 text-white"})}),(0,a.jsx)("span",{className:"font-space text-xl font-bold text-white",children:"NeoGym"})]}),(0,a.jsx)("div",{className:"hidden md:flex items-center space-x-8",children:u.map(e=>(0,a.jsx)(r(),{href:e.href,className:"text-gray-300 hover:text-white transition-colors duration-200",children:e.name},e.name))}),(0,a.jsx)("div",{className:"hidden md:block",children:(0,a.jsx)("button",{className:"btn-primary",children:"Get Started"})}),(0,a.jsx)("button",{onClick:()=>t(!e),className:"md:hidden p-2 rounded-lg glass hover:bg-white/10 transition-colors duration-200",children:e?(0,a.jsx)(h.A,{className:"w-6 h-6 text-white"}):(0,a.jsx)(p.A,{className:"w-6 h-6 text-white"})})]}),e&&(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsxs)("div",{className:"glass rounded-lg mt-4 p-4 space-y-4",children:[u.map(e=>(0,a.jsx)(r(),{href:e.href,onClick:()=>t(!1),className:"block p-3 rounded-lg hover:bg-white/10 transition-colors duration-200 text-gray-300 hover:text-white",children:e.name},e.name)),(0,a.jsx)("div",{className:"pt-4 border-t border-white/10",children:(0,a.jsx)("button",{className:"btn-primary w-full",children:"Get Started"})})]})})]})})};var g=s(488),b=s(8175),f=s(5684),y=s(2925),v=s(4516),j=s(8883),N=s(9881);let w=()=>{let e={company:[{name:"About NeoGym",href:"/about"},{name:"Our Mission",href:"/mission"},{name:"Careers",href:"/careers"},{name:"Press",href:"/press"}],services:[{name:"VR HIIT",href:"/classes/vr-hiit"},{name:"Gravity Yoga",href:"/classes/gravity-yoga"},{name:"AI Coaching",href:"/classes/ai-coaching"},{name:"Biometric Tracking",href:"/services/biometric"}],support:[{name:"Help Center",href:"/help"},{name:"Contact Us",href:"/contact"},{name:"Privacy Policy",href:"/privacy"},{name:"Terms of Service",href:"/terms"}]},t=[{name:"Facebook",icon:g.A,href:"#"},{name:"Twitter",icon:b.A,href:"#"},{name:"Instagram",icon:f.A,href:"#"},{name:"YouTube",icon:y.A,href:"#"}];return(0,a.jsx)("footer",{className:"relative bg-black/50 backdrop-blur-xl border-t border-white/10",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsxs)(r(),{href:"/",className:"flex items-center space-x-3 mb-6",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center glow-blue",children:(0,a.jsx)(l.A,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("span",{className:"font-orbitron text-2xl font-bold text-gradient-neon",children:["NEO",(0,a.jsx)("span",{className:"text-neon-green",children:"GYM"})]})]}),(0,a.jsx)("p",{className:"text-gray-400 mb-6 max-w-md font-exo",children:"The future of fitness starts now. Experience cutting-edge technology, AI-powered training, and immersive workouts that transform your body and mind."}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(v.A,{className:"w-5 h-5 text-neon-blue"}),(0,a.jsx)("span",{className:"text-gray-400",children:"2077 Future Street, Neo City, NC 12345"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(x.A,{className:"w-5 h-5 text-neon-blue"}),(0,a.jsx)("span",{className:"text-gray-400",children:"+1 (555) NEO-GYMS"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 text-neon-blue"}),(0,a.jsx)("span",{className:"text-gray-400",children:"<EMAIL>"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-orbitron text-lg font-semibold text-white mb-6",children:"Company"}),(0,a.jsx)("ul",{className:"space-y-3",children:e.company.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:e.href,className:"text-gray-400 hover:text-neon-blue transition-colors duration-300 font-exo",children:e.name})},e.name))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-orbitron text-lg font-semibold text-white mb-6",children:"Services"}),(0,a.jsx)("ul",{className:"space-y-3",children:e.services.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:e.href,className:"text-gray-400 hover:text-neon-blue transition-colors duration-300 font-exo",children:e.name})},e.name))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-orbitron text-lg font-semibold text-white mb-6",children:"Support"}),(0,a.jsx)("ul",{className:"space-y-3",children:e.support.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:e.href,className:"text-gray-400 hover:text-neon-blue transition-colors duration-300 font-exo",children:e.name})},e.name))})]})]}),(0,a.jsx)("div",{className:"border-t border-white/10 mt-12 pt-8",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-6 mb-6 md:mb-0",children:[(0,a.jsx)("span",{className:"font-orbitron text-white font-medium",children:"Follow the Future:"}),t.map(e=>{let t=e.icon;return(0,a.jsx)(r(),{href:e.href,className:"p-2 rounded-lg glass hover:glow-blue transition-all duration-300 group",children:(0,a.jsx)(t,{className:"w-5 h-5 text-gray-400 group-hover:text-neon-blue transition-colors duration-300"})},e.name)})]}),(0,a.jsxs)("button",{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"flex items-center space-x-2 px-4 py-2 glass rounded-lg hover:glow-blue transition-all duration-300 group",children:[(0,a.jsx)(N.A,{className:"w-4 h-4 text-neon-blue group-hover:animate-bounce"}),(0,a.jsx)("span",{className:"font-rajdhani text-white",children:"Back to Top"})]})]})}),(0,a.jsx)("div",{className:"border-t border-white/10 mt-8 pt-8 text-center",children:(0,a.jsx)("p",{className:"text-gray-400 font-exo",children:"\xa9 2024 NeoGym. All rights reserved. The future of fitness is here."})})]})})},A=e=>{let{children:t}=e;return(0,a.jsxs)("div",{className:"min-h-screen bg-black",children:[(0,a.jsx)(u,{}),(0,a.jsx)("main",{children:t}),(0,a.jsx)(w,{})]})}},5949:(e,t,s)=>{"use strict";s.d(t,{default:()=>x});var a=s(5155),i=s(2115),n=s(6408),r=s(1714),l=s(6785),o=s(9376),c=s(1539),d=s(7580),m=s(4186);let x=()=>{let[e,t]=(0,i.useState)("all"),s=[{id:1,title:"VR HIIT Infinity",category:"vr",description:"High-intensity interval training in immersive virtual environments. Battle through alien landscapes while burning calories.",duration:"45 min",intensity:"High",participants:"8-12",icon:r.A,image:"/api/placeholder/400/300",features:["Virtual Reality","Heart Rate Monitoring","Calorie Tracking"],color:"blue"},{id:2,title:"Gravity Yoga Flow",category:"biometric",description:"Anti-gravity yoga sessions with real-time posture analysis and breathing optimization through advanced sensors.",duration:"60 min",intensity:"Medium",participants:"6-10",icon:l.A,image:"/api/placeholder/400/300",features:["Anti-Gravity","Posture Analysis","Breathing Optimization"],color:"purple"},{id:3,title:"AI Strength Mastery",category:"ai",description:"Personalized strength training with AI form correction and adaptive resistance based on your performance.",duration:"50 min",intensity:"High",participants:"1-4",icon:o.A,image:"/api/placeholder/400/300",features:["AI Form Correction","Adaptive Resistance","Progress Tracking"],color:"green"},{id:4,title:"Neural Sync Cardio",category:"biometric",description:"Cardio workouts synchronized with your brainwaves for optimal performance and mental clarity.",duration:"40 min",intensity:"Medium",participants:"5-8",icon:c.A,image:"/api/placeholder/400/300",features:["Brainwave Sync","Mental Clarity","Cardio Optimization"],color:"cyan"},{id:5,title:"Hologram Boxing",category:"vr",description:"Box against holographic opponents with real-time technique analysis and impact measurement.",duration:"35 min",intensity:"High",participants:"4-6",icon:l.A,image:"/api/placeholder/400/300",features:["Holographic Opponents","Technique Analysis","Impact Measurement"],color:"blue"},{id:6,title:"Collective Mind Fitness",category:"group",description:"Group workouts where participants' biometrics are synchronized for enhanced team performance.",duration:"55 min",intensity:"Medium",participants:"12-20",icon:d.A,image:"/api/placeholder/400/300",features:["Team Sync","Group Biometrics","Collective Goals"],color:"purple"}],x="all"===e?s:s.filter(t=>t.category===e);return(0,a.jsxs)("section",{className:"py-24 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-purple-900/10 via-transparent to-blue-900/10"}),(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,a.jsxs)("div",{className:"inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8",children:[(0,a.jsx)(r.A,{className:"w-5 h-5 text-purple-400"}),(0,a.jsx)("span",{className:"font-orbitron text-sm font-medium text-white",children:"FUTURISTIC CLASSES"})]}),(0,a.jsxs)("h2",{className:"font-orbitron text-4xl md:text-6xl font-bold mb-8",children:[(0,a.jsx)("span",{className:"text-white",children:"NEXT-GEN "}),(0,a.jsx)("span",{className:"bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent",children:"TRAINING"}),(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent",children:"PROGRAMS"})]}),(0,a.jsx)("p",{className:"font-exo text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed",children:"Experience revolutionary fitness classes that blend cutting-edge technology with proven training methodologies for unprecedented results."})]}),(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"flex flex-wrap justify-center gap-4 mb-16",children:[{id:"all",name:"All Classes"},{id:"vr",name:"VR Training"},{id:"ai",name:"AI Coaching"},{id:"biometric",name:"Biometric"},{id:"group",name:"Group Sessions"}].map(s=>(0,a.jsx)("button",{onClick:()=>t(s.id),className:"px-6 py-3 rounded-lg font-orbitron font-medium transition-all duration-300 ".concat(e===s.id?"bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg":"glass text-gray-300 hover:text-white hover:bg-white/10"),children:s.name},s.id))}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:x.map((e,t)=>{let s=e.icon;return(0,a.jsx)(n.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},layout:!0,children:(0,a.jsxs)("div",{className:"glass rounded-2xl overflow-hidden h-full hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300",children:[(0,a.jsxs)("div",{className:"relative h-48 bg-gradient-to-br from-slate-800 to-slate-900 flex items-center justify-center",children:[(0,a.jsx)(s,{className:"w-16 h-16 text-cyan-400 opacity-50"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"font-orbitron text-xl font-bold text-white",children:e.title}),(0,a.jsx)(s,{className:"w-6 h-6 text-cyan-400"})]}),(0,a.jsx)("p",{className:"font-exo text-gray-400 mb-6 leading-relaxed",children:e.description}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.A,{className:"w-4 h-4 text-cyan-400 mx-auto mb-1"}),(0,a.jsx)("span",{className:"font-rajdhani text-sm text-gray-400",children:e.duration})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(c.A,{className:"w-4 h-4 text-purple-400 mx-auto mb-1"}),(0,a.jsx)("span",{className:"font-rajdhani text-sm text-gray-400",children:e.intensity})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.A,{className:"w-4 h-4 text-green-400 mx-auto mb-1"}),(0,a.jsx)("span",{className:"font-rajdhani text-sm text-gray-400",children:e.participants})]})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:e.features.map(e=>(0,a.jsx)("span",{className:"px-3 py-1 bg-white/10 rounded-full text-xs font-rajdhani text-gray-300",children:e},e))}),(0,a.jsx)("button",{className:"w-full px-6 py-3 bg-transparent border-2 border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black rounded-lg font-orbitron font-semibold transition-all duration-300",children:"Book Session"})]})]})},e.id)})})]})]})}},8431:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var a=s(5155),i=s(1539),n=s(2138),r=s(5690);let l=()=>(0,a.jsxs)("section",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 to-black",children:[(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,a.jsxs)("div",{className:"inline-flex items-center space-x-2 px-4 py-2 glass rounded-full mb-8",children:[(0,a.jsx)(i.A,{className:"w-4 h-4 text-blue-400"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-300",children:"Next Generation Fitness"})]}),(0,a.jsxs)("h1",{className:"font-space text-5xl md:text-7xl font-bold mb-6",children:[(0,a.jsx)("span",{className:"text-white",children:"Transform Your"}),(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent",children:"Fitness Journey"})]}),(0,a.jsx)("p",{className:"text-xl text-gray-400 mb-12 max-w-2xl mx-auto",children:"Experience the future of fitness with AI-powered training, virtual reality workouts, and personalized coaching."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-4 mb-16",children:[(0,a.jsxs)("button",{className:"btn-primary flex items-center space-x-2",children:[(0,a.jsx)("span",{children:"Get Started"}),(0,a.jsx)(n.A,{className:"w-4 h-4"})]}),(0,a.jsxs)("button",{className:"btn-secondary flex items-center space-x-2",children:[(0,a.jsx)(r.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Watch Demo"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:"10,000+"}),(0,a.jsx)("div",{className:"text-gray-400",children:"Active Members"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:"50+"}),(0,a.jsx)("div",{className:"text-gray-400",children:"AI Trainers"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:"24/7"}),(0,a.jsx)("div",{className:"text-gray-400",children:"Access"})]})]})]})}),(0,a.jsx)("div",{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsx)("div",{className:"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center",children:(0,a.jsx)("div",{className:"w-1 h-3 bg-white rounded-full mt-2 animate-bounce"})}),(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"Scroll to explore"})]})})]})},8977:(e,t,s)=>{"use strict";s.d(t,{default:()=>x});var a=s(5155),i=s(2115),n=s(6408),r=s(4516),l=s(9420),o=s(8883),c=s(4186),d=s(1539),m=s(2486);let x=()=>{let[e,t]=(0,i.useState)({name:"",email:"",phone:"",interest:"",message:""}),[s,x]=(0,i.useState)(!1),h=s=>{t({...e,[s.target.name]:s.target.value})},p=async s=>{s.preventDefault(),x(!0),await new Promise(e=>setTimeout(e,2e3)),console.log("Form submitted:",e),x(!1),t({name:"",email:"",phone:"",interest:"",message:""})},u=[{icon:r.A,title:"Location",details:["2077 Future Street","Neo City, NC 12345"],color:"blue"},{icon:l.A,title:"Phone",details:["+1 (555) NEO-GYMS","+****************"],color:"purple"},{icon:o.A,title:"Email",details:["<EMAIL>","<EMAIL>"],color:"green"},{icon:c.A,title:"Hours",details:["24/7 AI Access","Human Support: 6AM-10PM"],color:"cyan"}];return(0,a.jsxs)("section",{className:"py-24 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-blue-900/10 via-transparent to-purple-900/10"}),(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,a.jsxs)("div",{className:"inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8",children:[(0,a.jsx)(d.A,{className:"w-5 h-5 text-neon-blue"}),(0,a.jsx)("span",{className:"font-orbitron text-sm font-medium text-white",children:"GET IN TOUCH"})]}),(0,a.jsxs)("h2",{className:"font-orbitron text-4xl md:text-6xl font-bold mb-8",children:[(0,a.jsx)("span",{className:"text-white",children:"START YOUR "}),(0,a.jsx)("span",{className:"text-gradient-neon",children:"FUTURE"}),(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"text-gradient-cyber",children:"FITNESS JOURNEY"})]}),(0,a.jsx)("p",{className:"font-exo text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed",children:"Ready to experience the next evolution of fitness? Contact us to schedule your personalized tour and free trial session."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-7xl mx-auto",children:[(0,a.jsx)(n.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:(0,a.jsxs)("div",{className:"glass rounded-2xl p-8 hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300",children:[(0,a.jsx)("h3",{className:"font-orbitron text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent mb-8",children:"Send us a Message"}),(0,a.jsxs)("form",{onSubmit:p,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block font-orbitron text-sm font-medium text-white mb-2",children:"Name *"}),(0,a.jsx)("input",{type:"text",name:"name",value:e.name,onChange:h,required:!0,className:"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",placeholder:"Your full name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block font-orbitron text-sm font-medium text-white mb-2",children:"Email *"}),(0,a.jsx)("input",{type:"email",name:"email",value:e.email,onChange:h,required:!0,className:"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",placeholder:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block font-orbitron text-sm font-medium text-white mb-2",children:"Phone"}),(0,a.jsx)("input",{type:"tel",name:"phone",value:e.phone,onChange:h,className:"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",placeholder:"(*************"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block font-orbitron text-sm font-medium text-white mb-2",children:"Interest"}),(0,a.jsxs)("select",{name:"interest",value:e.interest,onChange:h,className:"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",children:[(0,a.jsx)("option",{value:"",children:"Select your interest"}),(0,a.jsx)("option",{value:"membership",children:"Membership Plans"}),(0,a.jsx)("option",{value:"trial",children:"Free Trial"}),(0,a.jsx)("option",{value:"corporate",children:"Corporate Programs"}),(0,a.jsx)("option",{value:"personal",children:"Personal Training"}),(0,a.jsx)("option",{value:"other",children:"Other"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block font-orbitron text-sm font-medium text-white mb-2",children:"Message"}),(0,a.jsx)("textarea",{name:"message",value:e.message,onChange:h,rows:5,className:"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 resize-none",placeholder:"Tell us about your fitness goals and how we can help..."})]}),(0,a.jsx)("button",{type:"submit",className:"w-full px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg font-orbitron font-bold text-white text-lg hover:from-blue-500 hover:to-purple-500 transition-all duration-300 shadow-lg hover:shadow-blue-500/50 disabled:opacity-50",disabled:s,children:s?(0,a.jsxs)("span",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,a.jsx)("span",{children:"Sending..."})]}):(0,a.jsxs)("span",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)(m.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Send Message"})]})})]})]})}),(0,a.jsxs)(n.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-8",children:[u.map((e,t)=>{let s=e.icon;return(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},children:(0,a.jsx)("div",{className:"glass rounded-2xl p-6 hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg",children:(0,a.jsx)(s,{className:"w-6 h-6 text-cyan-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-orbitron text-lg font-bold text-white mb-2",children:e.title}),e.details.map((e,t)=>(0,a.jsx)("p",{className:"font-exo text-gray-300",children:e},t))]})]})})},e.title)}),(0,a.jsxs)("div",{className:"glass rounded-2xl p-6 hover:shadow-lg hover:shadow-purple-500/20 transition-all duration-300",children:[(0,a.jsx)("h4",{className:"font-orbitron text-lg font-bold text-white mb-4",children:"Find Us"}),(0,a.jsx)("div",{className:"h-48 bg-gradient-to-br from-slate-800 to-slate-900 rounded-lg flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(r.A,{className:"w-12 h-12 text-purple-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"font-exo text-gray-400",children:"Interactive Map"}),(0,a.jsx)("p",{className:"font-exo text-sm text-gray-500",children:"Coming Soon"})]})})]})]})]})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[178,441,684,358],()=>t(4646)),_N_E=e.O()}]);