'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Play, ArrowR<PERSON>, Star, Zap, Trophy, Target } from 'lucide-react';
import Button from '@/components/ui/Button';

const Hero: React.FC = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Elite Background System */}
      <div className="absolute inset-0 z-0">
        {/* Primary Background */}
        <div
          className="w-full h-full bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1534438327276-14e5300c3a48?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')`
          }}
        />

        {/* Elite Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/80 via-black/60 to-black/80"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-primary-900/40 via-transparent to-accent-900/20"></div>

        {/* Animated Geometric Patterns */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent-500 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-primary-500/20 to-accent-500/20 rounded-full blur-3xl animate-spin" style={{animationDuration: '20s'}}></div>
        </div>

        {/* Elite Pattern Overlay */}
        <div
          className="absolute inset-0 opacity-5"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Cpath d='M30 30l15-15v30l-15-15zm-15 0l15 15v-30l-15 15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        />
      </div>

      {/* Elite Content Container */}
      <div className="relative z-10 container-elite text-center text-white pt-32 pb-20">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
          className="max-w-6xl mx-auto"
        >
          {/* Elite Status Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary-500/30 to-accent-500/30 backdrop-blur-xl border border-primary-400/40 rounded-full text-white text-sm font-bold mb-12 shadow-2xl hover:scale-105 transition-transform duration-300 cursor-pointer group"
          >
            <Star className="w-5 h-5 mr-3 text-yellow-400 animate-pulse" />
            <span className="text-gradient-gold">ELITE MEMBERSHIP</span>
            <span className="ml-3 px-3 py-1 bg-yellow-400/20 rounded-full text-yellow-300 text-xs font-bold">EXCLUSIVE</span>
          </motion.div>

          {/* Elite Hero Heading */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="mb-12"
          >
            <h1 className="heading-hero text-center mb-6">
              <span className="block text-white text-shadow-elite mb-4">
                TRANSFORM
              </span>
              <span className="block text-gradient-elite font-black">
                YOUR LIMITS
              </span>
            </h1>
            <div className="flex items-center justify-center space-x-8 mb-8">
              <div className="h-px w-16 bg-gradient-to-r from-transparent to-primary-400"></div>
              <div className="flex items-center space-x-2">
                <Zap className="w-6 h-6 text-yellow-400 animate-pulse" />
                <span className="text-xl font-bold text-yellow-400 tracking-wider">ELITE</span>
                <Trophy className="w-6 h-6 text-yellow-400 animate-pulse" />
              </div>
              <div className="h-px w-16 bg-gradient-to-l from-transparent to-accent-400"></div>
            </div>
          </motion.div>

          {/* Elite Subtitle */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="mb-16"
          >
            <p className="text-body-lg text-gray-200 max-w-4xl mx-auto leading-relaxed font-medium mb-8">
              Experience the pinnacle of fitness excellence. Join an exclusive community where
              <span className="text-gradient-gold font-bold"> world-class facilities</span>,
              <span className="text-gradient-gold font-bold"> elite trainers</span>, and
              <span className="text-gradient-gold font-bold"> cutting-edge technology</span>
              converge to unlock your ultimate potential.
            </p>

            {/* Elite Features */}
            <div className="flex flex-wrap items-center justify-center gap-6 text-sm font-semibold text-gray-300">
              <div className="flex items-center space-x-2">
                <Target className="w-4 h-4 text-primary-400" />
                <span>Personalized Training</span>
              </div>
              <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
              <div className="flex items-center space-x-2">
                <Trophy className="w-4 h-4 text-yellow-400" />
                <span>Elite Equipment</span>
              </div>
              <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
              <div className="flex items-center space-x-2">
                <Star className="w-4 h-4 text-accent-400" />
                <span>24/7 Access</span>
              </div>
            </div>
          </motion.div>

          {/* Elite CTA Section */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.9 }}
            className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-20"
          >
            <button className="btn-elite-primary group relative overflow-hidden">
              <span className="relative z-10 flex items-center">
                START ELITE JOURNEY
                <ArrowRight className="ml-3 w-5 h-5 transition-transform group-hover:translate-x-1" />
              </span>
            </button>

            <button className="btn-elite-ghost group">
              <Play className="mr-3 w-5 h-5 transition-transform group-hover:scale-110" />
              WATCH TRANSFORMATION
            </button>
          </motion.div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-3xl mx-auto"
          >
            <div className="text-center group">
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="text-4xl md:text-5xl font-extrabold bg-gradient-to-r from-primary-300 to-accent-300 bg-clip-text text-transparent mb-3">5000+</div>
                <div className="text-gray-200 text-sm font-semibold uppercase tracking-wider">Happy Members</div>
              </div>
            </div>
            <div className="text-center group">
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="text-4xl md:text-5xl font-extrabold bg-gradient-to-r from-primary-300 to-accent-300 bg-clip-text text-transparent mb-3">50+</div>
                <div className="text-gray-200 text-sm font-semibold uppercase tracking-wider">Expert Trainers</div>
              </div>
            </div>
            <div className="text-center group">
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="text-4xl md:text-5xl font-extrabold bg-gradient-to-r from-primary-300 to-accent-300 bg-clip-text text-transparent mb-3">24/7</div>
                <div className="text-gray-200 text-sm font-semibold uppercase tracking-wider">Access Available</div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10"
      >
        <div className="flex flex-col items-center text-white/70">
          <span className="text-sm mb-2 uppercase tracking-wide">Scroll Down</span>
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <motion.div
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 1.5, repeat: Infinity }}
              className="w-1 h-3 bg-white/50 rounded-full mt-2"
            />
          </div>
        </div>
      </motion.div>
    </section>
  );
};

export default Hero;
