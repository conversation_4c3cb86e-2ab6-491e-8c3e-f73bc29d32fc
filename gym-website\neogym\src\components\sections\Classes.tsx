'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Headset, Zap, Brain, Target, Users, Clock } from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';

const Classes = () => {
  const [activeFilter, setActiveFilter] = useState('all');

  const filters = [
    { id: 'all', name: 'All Classes' },
    { id: 'vr', name: 'VR Training' },
    { id: 'ai', name: 'AI Coaching' },
    { id: 'biometric', name: 'Biometric' },
    { id: 'group', name: 'Group Sessions' }
  ];

  const classes = [
    {
      id: 1,
      title: 'VR HIIT Infinity',
      category: 'vr',
      description: 'High-intensity interval training in immersive virtual environments. Battle through alien landscapes while burning calories.',
      duration: '45 min',
      intensity: 'High',
      participants: '8-12',
      icon: Headset,
      image: '/api/placeholder/400/300',
      features: ['Virtual Reality', 'Heart Rate Monitoring', 'Calorie Tracking'],
      color: 'blue'
    },
    {
      id: 2,
      title: 'Gravity Yoga Flow',
      category: 'biometric',
      description: 'Anti-gravity yoga sessions with real-time posture analysis and breathing optimization through advanced sensors.',
      duration: '60 min',
      intensity: 'Medium',
      participants: '6-10',
      icon: Target,
      image: '/api/placeholder/400/300',
      features: ['Anti-Gravity', 'Posture Analysis', 'Breathing Optimization'],
      color: 'purple'
    },
    {
      id: 3,
      title: 'AI Strength Mastery',
      category: 'ai',
      description: 'Personalized strength training with AI form correction and adaptive resistance based on your performance.',
      duration: '50 min',
      intensity: 'High',
      participants: '1-4',
      icon: Brain,
      image: '/api/placeholder/400/300',
      features: ['AI Form Correction', 'Adaptive Resistance', 'Progress Tracking'],
      color: 'green'
    },
    {
      id: 4,
      title: 'Neural Sync Cardio',
      category: 'biometric',
      description: 'Cardio workouts synchronized with your brainwaves for optimal performance and mental clarity.',
      duration: '40 min',
      intensity: 'Medium',
      participants: '5-8',
      icon: Zap,
      image: '/api/placeholder/400/300',
      features: ['Brainwave Sync', 'Mental Clarity', 'Cardio Optimization'],
      color: 'cyan'
    },
    {
      id: 5,
      title: 'Hologram Boxing',
      category: 'vr',
      description: 'Box against holographic opponents with real-time technique analysis and impact measurement.',
      duration: '35 min',
      intensity: 'High',
      participants: '4-6',
      icon: Target,
      image: '/api/placeholder/400/300',
      features: ['Holographic Opponents', 'Technique Analysis', 'Impact Measurement'],
      color: 'blue'
    },
    {
      id: 6,
      title: 'Collective Mind Fitness',
      category: 'group',
      description: 'Group workouts where participants\' biometrics are synchronized for enhanced team performance.',
      duration: '55 min',
      intensity: 'Medium',
      participants: '12-20',
      icon: Users,
      image: '/api/placeholder/400/300',
      features: ['Team Sync', 'Group Biometrics', 'Collective Goals'],
      color: 'purple'
    }
  ];

  const filteredClasses = activeFilter === 'all' 
    ? classes 
    : classes.filter(cls => cls.category === activeFilter);

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-b from-purple-900/10 via-transparent to-blue-900/10"></div>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8">
            <Headset className="w-5 h-5 text-neon-purple" />
            <span className="font-orbitron text-sm font-medium text-white">
              FUTURISTIC CLASSES
            </span>
          </div>
          
          <h2 className="font-orbitron text-4xl md:text-6xl font-bold mb-8">
            <span className="text-white">NEXT-GEN </span>
            <span className="text-gradient-neon">TRAINING</span>
            <br />
            <span className="text-gradient-cyber">PROGRAMS</span>
          </h2>
          
          <p className="font-exo text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Experience revolutionary fitness classes that blend cutting-edge technology 
            with proven training methodologies for unprecedented results.
          </p>
        </motion.div>

        {/* Filter Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-4 mb-16"
        >
          {filters.map((filter) => (
            <button
              key={filter.id}
              onClick={() => setActiveFilter(filter.id)}
              className={`px-6 py-3 rounded-lg font-orbitron font-medium transition-all duration-300 ${
                activeFilter === filter.id
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
                  : 'glass text-gray-300 hover:text-white hover:bg-white/10'
              }`}
            >
              {filter.name}
            </button>
          ))}
        </motion.div>

        {/* Classes Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredClasses.map((classItem, index) => {
            const Icon = classItem.icon;
            return (
              <motion.div
                key={classItem.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                layout
              >
                <Card glow={classItem.color as any} className="overflow-hidden h-full">
                  {/* Image Placeholder */}
                  <div className="relative h-48 bg-gradient-to-br from-slate-800 to-slate-900 flex items-center justify-center">
                    <Icon className="w-16 h-16 text-neon-blue opacity-50" />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  </div>
                  
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-orbitron text-xl font-bold text-white">
                        {classItem.title}
                      </h3>
                      <Icon className="w-6 h-6 text-neon-blue" />
                    </div>
                    
                    <p className="font-exo text-gray-400 mb-6 leading-relaxed">
                      {classItem.description}
                    </p>
                    
                    {/* Class Info */}
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <div className="text-center">
                        <Clock className="w-4 h-4 text-neon-blue mx-auto mb-1" />
                        <span className="font-rajdhani text-sm text-gray-400">{classItem.duration}</span>
                      </div>
                      <div className="text-center">
                        <Zap className="w-4 h-4 text-neon-purple mx-auto mb-1" />
                        <span className="font-rajdhani text-sm text-gray-400">{classItem.intensity}</span>
                      </div>
                      <div className="text-center">
                        <Users className="w-4 h-4 text-neon-green mx-auto mb-1" />
                        <span className="font-rajdhani text-sm text-gray-400">{classItem.participants}</span>
                      </div>
                    </div>
                    
                    {/* Features */}
                    <div className="flex flex-wrap gap-2 mb-6">
                      {classItem.features.map((feature) => (
                        <span
                          key={feature}
                          className="px-3 py-1 bg-white/10 rounded-full text-xs font-rajdhani text-gray-300"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                    
                    <Button variant="neon" className="w-full">
                      Book Session
                    </Button>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Classes;
