'use client';

import { motion } from 'framer-motion';
import { Brain, Zap, Target, Activity, Star, Award } from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';

const Trainers = () => {
  const trainers = [
    {
      id: 1,
      name: 'ARIA-7',
      title: 'AI Strength Specialist',
      specialty: 'Neural-Enhanced Strength Training',
      experience: '10,000+ Sessions',
      rating: 4.9,
      avatar: '/api/placeholder/300/300',
      description: 'Advanced AI trainer specializing in biomechanical optimization and strength enhancement through neural feedback.',
      skills: ['Form Analysis', 'Progressive Overload', 'Injury Prevention', 'Neural Optimization'],
      achievements: ['99.2% Success Rate', '50% Faster Results', 'Zero Injuries'],
      icon: Brain,
      color: 'blue'
    },
    {
      id: 2,
      name: 'NOVA-X',
      title: 'VR Cardio Master',
      specialty: 'Immersive Cardio Experiences',
      experience: '8,500+ Sessions',
      rating: 4.8,
      avatar: '/api/placeholder/300/300',
      description: 'Virtual reality fitness expert creating immersive cardio adventures that make workouts feel like epic quests.',
      skills: ['VR Environment Design', 'Cardio Optimization', 'Gamification', 'Endurance Building'],
      achievements: ['95% Retention Rate', '40% Improved Endurance', 'Award-Winning VR Design'],
      icon: Zap,
      color: 'purple'
    },
    {
      id: 3,
      name: 'ZENITH-9',
      title: 'Biometric Yoga Guide',
      specialty: 'Mind-Body Synchronization',
      experience: '12,000+ Sessions',
      rating: 5.0,
      avatar: '/api/placeholder/300/300',
      description: 'Holistic wellness AI combining ancient yoga wisdom with cutting-edge biometric monitoring for perfect balance.',
      skills: ['Breath Analysis', 'Flexibility Tracking', 'Stress Reduction', 'Mindfulness'],
      achievements: ['Perfect 5.0 Rating', '60% Stress Reduction', 'Meditation Master'],
      icon: Target,
      color: 'green'
    },
    {
      id: 4,
      name: 'TITAN-5',
      title: 'HIIT Performance Coach',
      specialty: 'High-Intensity Optimization',
      experience: '9,200+ Sessions',
      rating: 4.9,
      avatar: '/api/placeholder/300/300',
      description: 'Elite performance AI designed for maximum intensity training with real-time adaptation to your limits.',
      skills: ['HIIT Protocols', 'Recovery Optimization', 'Performance Analytics', 'Motivation Systems'],
      achievements: ['35% Faster Fat Loss', '98% Goal Achievement', 'Elite Athlete Approved'],
      icon: Activity,
      color: 'cyan'
    }
  ];

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-b from-green-900/10 via-transparent to-cyan-900/10"></div>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center space-x-2 px-6 py-3 glass rounded-full mb-8">
            <Brain className="w-5 h-5 text-neon-green" />
            <span className="font-orbitron text-sm font-medium text-white">
              AI TRAINERS
            </span>
          </div>
          
          <h2 className="font-orbitron text-4xl md:text-6xl font-bold mb-8">
            <span className="text-white">MEET YOUR </span>
            <span className="text-gradient-neon">DIGITAL</span>
            <br />
            <span className="text-gradient-cyber">COACHES</span>
          </h2>
          
          <p className="font-exo text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            Our AI trainers combine thousands of hours of expertise with real-time adaptation, 
            providing personalized coaching that evolves with your progress.
          </p>
        </motion.div>

        {/* Trainers Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {trainers.map((trainer, index) => {
            const Icon = trainer.icon;
            return (
              <motion.div
                key={trainer.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
              >
                <div className="glass rounded-2xl p-8 h-full relative overflow-hidden hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300">
                  {/* Hologram Effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 pointer-events-none"></div>
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent"></div>
                  
                  <div className="relative z-10">
                    {/* Avatar & Basic Info */}
                    <div className="flex items-start space-x-6 mb-6">
                      <div className="relative">
                        <div className="w-24 h-24 bg-gradient-to-br from-slate-700 to-slate-800 rounded-2xl flex items-center justify-center">
                          <Icon className="w-12 h-12 text-cyan-400" />
                        </div>
                        {/* Hologram lines */}
                        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-cyan-400/20 to-transparent rounded-2xl"></div>
                        <div className="absolute top-2 left-0 w-full h-0.5 bg-cyan-400/30"></div>
                        <div className="absolute bottom-2 left-0 w-full h-0.5 bg-cyan-400/30"></div>
                      </div>
                      
                      <div className="flex-1">
                        <h3 className="font-orbitron text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent mb-2">
                          {trainer.name}
                        </h3>
                        <p className="font-rajdhani text-lg text-cyan-400 mb-2">
                          {trainer.title}
                        </p>
                        <div className="flex items-center space-x-4 text-sm text-gray-400">
                          <span className="flex items-center space-x-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span>{trainer.rating}</span>
                          </span>
                          <span>{trainer.experience}</span>
                        </div>
                      </div>
                    </div>

                    {/* Specialty */}
                    <div className="mb-6">
                      <h4 className="font-orbitron text-sm font-semibold text-white mb-2">SPECIALTY</h4>
                      <p className="font-exo text-gray-300">{trainer.specialty}</p>
                    </div>

                    {/* Description */}
                    <div className="mb-6">
                      <p className="font-exo text-gray-400 leading-relaxed">
                        {trainer.description}
                      </p>
                    </div>

                    {/* Skills */}
                    <div className="mb-6">
                      <h4 className="font-orbitron text-sm font-semibold text-white mb-3">CORE SKILLS</h4>
                      <div className="flex flex-wrap gap-2">
                        {trainer.skills.map((skill) => (
                          <span
                            key={skill}
                            className="px-3 py-1 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-xs font-rajdhani text-blue-300"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Achievements */}
                    <div className="mb-8">
                      <h4 className="font-orbitron text-sm font-semibold text-white mb-3">ACHIEVEMENTS</h4>
                      <div className="space-y-2">
                        {trainer.achievements.map((achievement) => (
                          <div key={achievement} className="flex items-center space-x-2">
                            <Award className="w-4 h-4 text-yellow-400" />
                            <span className="font-exo text-sm text-gray-300">{achievement}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* CTA */}
                    <button className="w-full px-6 py-3 bg-transparent border-2 border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black rounded-lg font-orbitron font-semibold transition-all duration-300">
                      Train with {trainer.name}
                    </button>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="glass rounded-2xl p-12 max-w-4xl mx-auto hover:shadow-lg hover:shadow-purple-500/20 transition-all duration-300">
            <h3 className="font-orbitron text-3xl font-bold bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent mb-6">
              Ready to Meet Your Perfect AI Match?
            </h3>
            <p className="font-exo text-lg text-gray-300 leading-relaxed mb-8">
              Our advanced matching algorithm will pair you with the ideal AI trainer
              based on your goals, preferences, and fitness level.
            </p>
            <button className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg font-orbitron font-bold text-white text-lg hover:from-blue-500 hover:to-purple-500 transition-all duration-300 shadow-lg hover:shadow-blue-500/50">
              Find My AI Trainer
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Trainers;
